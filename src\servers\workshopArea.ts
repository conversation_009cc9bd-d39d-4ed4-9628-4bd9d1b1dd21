import { request } from './request'

// 获取车间区域树形数据
export const getWorkshopAreaTree = (data) => request({ url: '/api/businessBase/workshopArea/leftStructureColumn', data })
// 获取车间区域列表
export const getWorkshopAreas = (data) => request({ url: '/api/businessBase/workshopArea/index', data })
// 新建车间区域
export const addWorkshopArea = (data) => request({ url: '/api/businessBase/workshopArea/store', data })
// 更新车间区域
export const updateWorkshopArea = (id, data) => request({ url: `/api/businessBase/workshopArea/update/${id}`, data })
// 查看车间区域详情
export const viewWorkshopArea = (id) => request({ url: `/api/businessBase/workshopArea/show/${id}` })
// 删除车间区域
export const deleteWorkshopArea = (id) => request({ url: `/api/businessBase/workshopArea/destroy/${id}` })
// 更新区域状态
export const updateAreaStatus = (id, data) => request({ url: `/api/businessBase/workshopArea/updateStatus/${id}`, data })
// 获取下拉选项
export const getSelectOptions = () => request({ url: '/api/businessBase/workshopArea/getSelect' })

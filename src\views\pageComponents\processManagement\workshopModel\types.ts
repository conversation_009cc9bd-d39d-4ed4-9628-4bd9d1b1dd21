/* eslint-disable no-unused-vars */
export type ModuleType = 'company' | 'workshop' | 'area' | 'workcenter'
export type ActionType = 'add' | 'edit' | 'view' | 'delete'
export type ModuleActionType = `${ActionType}-${ModuleType}`

/** 生产实体类型枚举 */
export enum WORKSHOP_TYPE {
  /** 公司 */
  COMPANY = 0,
  /** 车间 */
  WORKSHOP = 1,
  /** 区域 */
  AREA = 2,
  /** 工作中心 */
  CENTER = 3,
}

/** workshop_type 映射表 */
export const WORKSHOP_TYPE_MAP = {
  [WORKSHOP_TYPE.COMPANY]: 'company',
  [WORKSHOP_TYPE.WORKSHOP]: 'workshop',
  [WORKSHOP_TYPE.AREA]: 'area',
  [WORKSHOP_TYPE.CENTER]: 'workcenter',
}

/** type对应模块名称 */
export const TYPE_NAME = {
  workshop: '车间',
  area: '区域',
  workcenter: '工作中心',
}

export const PAGE_TYPE_MAP = {
  workshop: { name: '车间', pageIndexCode: 'workshopIndex' },
  area: { name: '区域', pageIndexCode: 'workshopAreaIndex' },
  workcenter: { name: '工作中心', pageIndexCode: 'workshopWorkcenterIndex' },
}

/** 可操作的节点：不同页面下根据workshop_type判断是否可操作 */
export const CAN_OPERATE_NODE = {
  workshop: [WORKSHOP_TYPE.COMPANY, WORKSHOP_TYPE.WORKSHOP],
  area: [WORKSHOP_TYPE.WORKSHOP, WORKSHOP_TYPE.AREA],
  workcenter: [WORKSHOP_TYPE.WORKSHOP, WORKSHOP_TYPE.AREA],
}

export const COMPANY_MENU = [{ key: 'add-workshop', title: '添加车间' }]
export const WORKSHOP_MENU = [
  { key: 'view-workshop', title: '查看详情' },
  { key: 'edit-workshop', title: '编辑信息' },
  { key: 'add-area', title: '添加区域' },
  { key: 'delete-workshop', title: '删除' },
]
export const AREA_WORKSHOP_MENU = [
  { key: 'view-workshop', title: '查看详情' },
  { key: 'edit-workshop', title: '编辑信息' },
  { key: 'add-area', title: '添加区域' },
]
export const AREA_MENU = [
  { key: 'view-area', title: '查看详情' },
  { key: 'edit-area', title: '编辑信息' },
  { key: 'add-workcenter', title: '添加工作中心' },
  { key: 'delete-area', title: '删除' },
]

/** 右侧下拉菜单选项 */
export const MENU_MAP = {
  company: COMPANY_MENU,
  workshop: WORKSHOP_MENU,
  area_workshop: AREA_WORKSHOP_MENU,
  area: AREA_MENU,
}

/** 底部添加按钮 */
export const BOTTOM_ADD_ITEM = {
  workshop: { menuKey: 'add', menuName: '新建车间' },
  area: { menuKey: 'add', menuName: '新建区域' },
  workcenter: { menuKey: 'add', menuName: '新建工作中心' },
}

/** 底部按钮是否操作：上一层级的节点可操作 */
export const CAN_EDIT_BOTTOM_BTN = {
  workshop: WORKSHOP_TYPE.COMPANY,
  area: WORKSHOP_TYPE.WORKSHOP,
  workcenter: WORKSHOP_TYPE.AREA,
}

export const TREE_MAP = {
  company: {
    title: '公司',
    menu: COMPANY_MENU,
  },
  workshop: {
    title: '车间',
    menu: WORKSHOP_MENU,
  },
  area: {
    title: '区域',
    menu: AREA_MENU,
  },
}

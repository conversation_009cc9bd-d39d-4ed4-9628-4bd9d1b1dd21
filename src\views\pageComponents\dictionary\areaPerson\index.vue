<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.AREA_PERSON" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.AREA_PERSON" :get-list="GetList" :isIndex="true">
      <template #right-btn>
        <a-button type="primary" @click="tapAdd('add')" :icon="h(PlusOutlined)">新建区域人员</a-button>
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row, rowIndex }">
        <div class="btnBox">
          <a-button :disabled="!btnPermission[81003]" @click="detail(row, rowIndex)" class="btn">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('compiler', row, rowIndex)" :disabled="!btnPermission[81004]">编辑</a-menu-item>
                <a-menu-item @click="tapAdd('removes', row)" :disabled="!btnPermission[81005]">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="520"
      :title="roleModuleType == 'add' ? '新建区域人员' : '编辑区域人员'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="序号" name="rowIndex" v-if="roleModuleType == 'compiler'">
          <span>{{ rowIdx }}</span>
        </a-form-item>
        <a-form-item label="车间" name="workshop">
          <a-input v-model:value="editForm.workshop" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="区域" name="area">
          <a-input v-model:value="editForm.area" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="生产阶别" name="type1">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.type1" placeholder="请选择" :options="arr1"></a-select>
        </a-form-item>
        <a-form-item label="工作人员" name="person">
          <a-input v-model:value="editForm.person" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="岗位" name="post">
          <a-input v-model:value="editForm.post" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-input v-model:value="editForm.remark" placeholder="请输入" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { h, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Add, Delete, GetList, Update } from '@/servers/processParamtters'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()

const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  type1: [{ required: true, message: '请选择生产阶别', trigger: ['change', 'blur'] }],
  type2: [{ required: true, message: '请选择工序类别', trigger: ['change', 'blur'] }],
  code: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  type3: [{ required: true, message: '请选择参数类型', trigger: ['change', 'blur'] }],
  value: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
}
const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '请选择区域',
    value: null,
    type: 'select',
    selectArr: [
      { label: '区域1', value: 1 },
      { label: '区域2', value: 2 },
      { label: '区域3', value: 3 },
    ],
    key: 'area',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['create_start_at', 'create_end_at'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['update_start_at', 'update_end_at'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const arr1 = ref([
  { label: '半成品', value: 1 },
  { label: '灌装', value: 2 },
  { label: '包装', value: 3 },
])
const rowIdx = ref(0)
const editForm = reactive({
  id: null,
  workshop: '',
  area: '',
  type1: null,
  person: '',
  post: '',
  remark: '',
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.AREA_PERSON) {
    const arr: any[] = []
    obj.AREA_PERSON.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  search()
  initScreening()
})

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapAdd = (type: string, row: any = '', rowIndex: number = 0) => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      editForm.workshop = ''
      editForm.area = ''
      editForm.type1 = null
      editForm.person = ''
      editForm.post = ''
      editForm.remark = ''
      break
    case 'compiler':
      rowIdx.value = rowIndex + 1
      editForm.id = row.id
      editForm.workshop = row.workshop
      editForm.area = row.area
      editForm.type1 = row.type1
      editForm.person = row.person
      editForm.post = row.post
      editForm.remark = row.remark
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除区域人员'
      visibleData.content = `是否确认删除区域人员？删除前，请先移除关联该区域人员的所有数据，否则会操作失败！`
      visibleData.confirmBtnText = '确认删除'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}
// 详情
const detail = (item, idx) => {
  detailDrawerRef.value?.open(item.id, idx)
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

// 新增
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Add(obj).then((res) => {
    if (res.success) {
      message.success('新增成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 编辑
const upRoleDate = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Update(obj).then((res) => {
    if (res.success) {
      message.success('修改成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  Delete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

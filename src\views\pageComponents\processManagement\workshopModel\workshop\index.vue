<template>
  <div class="flex flex-1 !flex-row px-20 py-12">
    <tree-selector ref="treeRef" type="workshop" @update="refresh" />
    <div class="flex flex-1 flex-col">
      <div class="header-container">
        <div class="header-item active">车间</div>
        <div class="header-item">区域</div>
        <div class="header-item">工作中心</div>
      </div>
      <Form v-model:form="formArr" :page-type="PageType.WORKSHOP" :showSetting="false" @search="search" @setting="tableRef?.showTableSetting()" />
      <!-- 表格 -->
      <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.WORKSHOP" :get-list="getWorkshops">
        <template #right-btn>
          <a-button type="primary" :icon="h(PlusOutlined)" v-if="btnPermission[PERM_CODE.CREATE] && canCreateItem" @click="handleUpdateWorkshop('add')">新建车间</a-button>
        </template>
        <template #status="{ row, rowIndex }">
          <a-switch
            v-if="btnPermission[PERM_CODE.EDIT]"
            v-model:checked="row.status"
            :checkedValue="1"
            :unCheckedValue="0"
            checked-children="启用"
            un-checked-children="停用"
            @click="handleSwitch(row, rowIndex)"
          />
          <span v-else>{{ row.status ? '启用' : '停用' }}</span>
        </template>
        <template #creator_name="{ row }">
          <span>{{ row.creator?.real_name || '--' }}</span>
        </template>
        <template #created_at="{ row }">
          <span>{{ row.creator?.time || '--' }}</span>
        </template>
        <template #operate="{ row }">
          <div class="btnBox">
            <a-button v-if="btnPermission[PERM_CODE.VIEW]" @click="handleViewWorkshop(row.id)" class="btn">查看</a-button>
            <a-dropdown>
              <a-button>更多</a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="btnPermission[PERM_CODE.EDIT]" @click="handleUpdateWorkshop('edit', row.id)">编辑</a-menu-item>
                  <a-menu-item v-if="btnPermission[PERM_CODE.DELETE]" @click="handleDeleteWorkshop(row.id)">
                    <span class="text-red-500">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </BaseTable>
    </div>
    <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
  </div>
</template>

<script lang="ts" setup>
import { h, shallowRef, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getWorkshops, getWorkshopNames } from '@/servers/workshop'
import { PageType } from '@/common/enum'
import { PERM_CODE } from './type'
import TreeSelector from '../TreeSelector.vue'
import WorkshopUpdateDrawer from './components/WorkshopUpdateDrawer.vue'
import WorkshopViewDrawer from './components/WorkshopViewDrawer.vue'
import WorkshopDeleteModal from './components/WorkshopDeleteModal.vue'
import StatusUpdateModal from './components/StatusUpdateModal.vue'

const { btnPermission } = usePermission()

const treeRef = ref()
const company_id = computed(() => treeRef.value?.company_id || localStorage.getItem('tree_company'))
const canCreateItem = computed(() => treeRef.value?.canCreateItem) // 添加按钮是否可操作
const workshopOptions = ref() // 车间下拉选项

// 查询表单
const formArr = ref<any[]>([
  {
    label: '请选择车间',
    value: null,
    type: 'select',
    key: 'name',
    showSearch: true,
    selectKey: 'name',
    selectArr: workshopOptions.value,
    fieldNames: { label: 'name', value: 'name' },
  },
])
const tableRef = ref()
const search = () => tableRef.value.search()

// component
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  workshop_id: null,
  company_id: '',
})

const refresh = (type: string = 'index') => {
  tableRef.value.refresh()
  getWorkshopOptions() // 更新车间下拉选项
  if (type === 'index') {
    treeRef.value.getTreeData() // 更新树结构数据
  }
}

/** 创建/编辑车间 */
const handleUpdateWorkshop = (type: string, workshop_id?: number) => {
  component.value = WorkshopUpdateDrawer
  initValue.type = type
  initValue.company_id = company_id
  initValue.workshop_id = workshop_id
  open.value = true
}
/** 查看车间详情 */
const handleViewWorkshop = (workshop_id: number) => {
  component.value = WorkshopViewDrawer
  initValue.workshop_id = workshop_id
  open.value = true
}
/** 删除车间 */
const handleDeleteWorkshop = (workshop_id: number) => {
  component.value = WorkshopDeleteModal
  initValue.workshop_id = workshop_id
  open.value = true
}
/** 切换车间启用状态 */
const handleSwitch = (row, rowIndex: number) => {
  component.value = StatusUpdateModal
  initValue.workshop_id = row.id
  initValue.name = row.name
  initValue.status = row.status // 此时status状态已改变
  tableRef.value.tableData[rowIndex].status = row.status === 1 ? 0 : 1 // 确保切换成功之前status状态不变
  open.value = true
}

/** 获取表单查询选项 */
const getWorkshopOptions = async () => {
  const params = {
    type: 'workshop',
    company_id: company_id.value,
  }
  const res = await getWorkshopNames(params)
  workshopOptions.value = res.data || []
}

watch(
  workshopOptions,
  (newOptions) => {
    formArr.value[0].selectArr = newOptions
  },
  { immediate: true },
)

onMounted(() => {
  getWorkshopOptions()
})
</script>

<style lang="scss" scoped>
.header-container {
  display: inline-flex;
  margin-bottom: 8px;

  .header-item {
    align-content: center;
    width: 60px;
    height: 28px;
    color: #000;
    text-align: center;
    border: 1px solid #d3d3d3;
    border-right: 0;
  }

  .header-item:last-child {
    width: 70px;
    border: 1px solid #d3d3d3;
  }

  .header-item.active {
    color: #fff;
    background: #409eff;
    border-color: #409eff;
  }
}
</style>

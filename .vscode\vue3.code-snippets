{
  "Print unibest Vue3 SFC": {
    "scope": "vue",
    "prefix": "v3",
    "body": [
      "<template>",
      "  <div class=\"\">$2</div>",
      "</template>\n",
      "<script lang=\"ts\" setup>",
      "//$3",
      "</script>\n",
      "<style lang=\"scss\" scoped>",
      "//$4",
      "</style>\n",
    ],
  },
  "Print unibest Vue3 Mobile SFC": {
    "scope": "vue",
    "prefix": "v3m",
    "body": [
      "<template>",
      "  <div class=\"\">$2</div>",
      "</template>\n",
      "<script lang=\"ts\" setup>",
      "definePageMeta({",
      "  layout: 'mobile',",
      "})",
      "//$3",
      "</script>\n",
      "<style lang=\"scss\" scoped>",
      "//$4",
      "</style>\n",
    ],
  },
}

<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.BARCODE_CONFIG" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.BARCODE_CONFIG" :get-list="GetConfigList" :isIndex="true">
      <template #right-btn>
        <a-button type="primary" @click="tapAdd('add')" :icon="h(PlusOutlined)">新建条码生成配置</a-button>
      </template>

      <template #barcode_value="{ row }">
        <span>{{ configureType?.find((i) => i.value == row.barcode_value)?.label }}</span>
      </template>
      <template #is_default="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" v-model:checked="row.is_default" checked-children="是" un-checked-children="否" />
      </template>
      <template #created_at="{ row }">
        <span>{{ row.created_at ? row.created_at.slice(0, 16) : '' }}</span>
      </template>
      <template #updated_at="{ row }">
        <span>{{ row.updated_at ? row.updated_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button @click="detail(row)" class="btn">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('preview', row)">预览</a-menu-item>
                <a-menu-item @click="tapAdd('compiler', row)">编辑</a-menu-item>
                <a-menu-item @click="tapAdd('removes', row)">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="1250px"
      :title="roleModuleType == 'add' ? '新建条码生成配置' : '编辑条码生成配置'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-col :span="12">
          <a-form-item label="配置类型" name="barcode_value">
            <a-select
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              v-model:value="editForm.barcode_value"
              placeholder="请选择"
              :options="configureType"
              @change="getDataOption(editForm.barcode_value)"
              @clear="dataOptions = []"
              allow-clear
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="配置编码" name="code">
            <a-input v-model:value="editForm.code" placeholder="请输入" :maxlength="30" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="配置名称" name="name">
            <a-input v-model:value="editForm.name" placeholder="请输入" :maxlength="50" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" name="remark">
            <a-input v-model:value="editForm.remark" placeholder="请输入" :maxlength="200" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否默认" name="is_default">
            <a-switch class="btn" v-model:checked="editForm.is_default" checked-children="是" un-checked-children="否" :checkedValue="true" :unCheckedValue="false" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <div class="w-full">
            <SimpleTable :tableKey="tableKey" :data="details">
              <template #sort="{ rowIndex }">
                <span>{{ rowIndex + 1 }}</span>
              </template>
              <template #code_segment="{ row, rowIndex }">
                <a-select v-model:value="row.code_segment" placeholder="请选择" :options="codeSegmentType" allow-clear class="w-full" @change="resetRow(rowIndex)"></a-select>
              </template>
              <template #value_range="{ row, rowIndex }">
                <a-input v-if="row.code_segment == 2" v-model:value="row.value_range" placeholder="请输入" :maxlength="50" allow-clear />
                <a-select v-if="row.code_segment == 3" v-model:value="row.value_range" placeholder="请选择" :options="dateType" allow-clear class="w-full" @change="resetRow1(rowIndex)"></a-select>
                <a-select v-if="row.code_segment == 4" v-model:value="row.value_range" placeholder="请选择" :options="serialType" allow-clear class="w-full" @change="resetRow1(rowIndex)"></a-select>
              </template>
              <template #length="{ row }">
                <a-input-number v-if="row.code_segment == 4" v-model:value="row.length" placeholder="请输入" allow-clear :min="0" :precision="0" class="w-full" />
              </template>
              <template #padding_mode="{ row, rowIndex }">
                <a-select v-if="row.code_segment == 4" v-model:value="row.padding_mode" placeholder="请选择" :options="fillType" allow-clear class="w-full" @change="resetRow2(rowIndex)"></a-select>
              </template>
              <template #padding_char="{ row }">
                <a-input v-if="[2, 3].includes(row.padding_mode)" v-model:value="row.padding_char" placeholder="请输入" :maxlength="1" allow-clear />
              </template>
              <template #start_serial="{ row }">
                <a-input v-if="[2, 3].includes(row.padding_mode)" v-model:value="row.start_serial" placeholder="请输入" :maxlength="1" allow-clear />
              </template>
              <template #data_source="{ row }">
                <a-select v-if="row.code_segment == 1" v-model:value="row.data_source" placeholder="请选择" :options="dataOptions" allow-clear class="w-full"></a-select>
              </template>
              <template #serial_calc="{ row }">
                <a-checkbox v-model:checked="row.serial_calc" :disabled="row.code_segment == 4"></a-checkbox>
              </template>
              <template #operate="{ rowIndex }">
                <a-button size="small" @click="addBtn(rowIndex)" type="primary">添加</a-button>
                <a-popconfirm title="确认删除该行数据吗?" ok-text="确定" cancel-text="取消" @confirm="removeBtn(rowIndex)">
                  <a-button size="small" v-if="details.length > 1" class="ml-10px" type="primary" danger>删除</a-button>
                </a-popconfirm>
              </template>
            </SimpleTable>
          </div>
        </a-col>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tempPreview">预览</a-button>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { h, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { GetConfigList, DataItemFields, Create, Update, Delete, GetOptions, Enable, Preview, PreviewTemp } from '@/servers/barcodeConfig'
import { validateStr, validCode } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import DetailDrawer from './components/DetailDrawer.vue'

// const { btnPermission } = usePermission()
const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  barcode_value: [{ required: true, message: '请选择配置类型', trigger: ['change', 'blur'] }],
  code: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 30),
      message: '输入内容不可超过30字符',
    },
    {
      validator: (_rule, value) => validCode(_rule, value),
      message: '配置编码不能出现中文',
    },
  ],
  name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
}
const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索配置编码',
    value: null,
    type: 'input',
    key: 'code',
  },
  {
    label: '请输入配置名称',
    value: null,
    type: 'input',
    key: 'name',
  },
  {
    label: '配置类型',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'barcode_value',
    multiple: false,
  },
  {
    label: '是否默认',
    value: null,
    type: 'select',
    selectArr: [
      {
        label: '是',
        value: true,
      },
      {
        label: '否',
        value: false,
      },
    ],
    key: 'is_default',
    multiple: false,
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const configureType = ref([])
const fillType = ref([])
const codeSegmentType = ref([])
const dateType = ref([])
const serialType = ref([])
const dataOptions = ref([])
const tableKey = [
  { title: '配置顺序', field: 'sort', width: 80 },
  { title: '码段', field: 'code_segment' },
  { title: '值域', field: 'value_range' },
  { title: '长度', field: 'length' },
  { title: '填充方式', field: 'padding_mode' },
  { title: '填充字符', field: 'padding_char' },
  { title: '起始流水号', field: 'start_serial' },
  { title: '数据源配置内容', field: 'data_source' },
  { title: '流水号计算', field: 'serial_calc' },
  { title: '操作', field: 'operate', width: 150 },
]
const editForm = reactive({
  id: null,
  barcode_value: null,
  code: '',
  name: '',
  is_default: false,
  remark: '',
})
const details: any = ref([])
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.BARCODE_CONFIG) {
    const arr: any[] = []
    obj.BARCODE_CONFIG.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

onMounted(() => {
  getOptions()
  search()
  initScreening()
})

const getOptions = async () => {
  const optionsCache = await GetOptions({})
  // 配置类型
  configureType.value = optionsCache.data.CO_CONFIGURE_TYPE.map((x) => {
    return {
      label: x.key,
      value: x.value,
    }
  })
  // 填充类型
  fillType.value = optionsCache.data.CO_FILL_TYPE
  // 码段类型
  codeSegmentType.value = optionsCache.data.CO_CODE_SEGMENT
  // 日期类型
  dateType.value = optionsCache.data.DATE.map((x) => {
    return {
      label: x.name,
      value: x.id,
    }
  })
  // 流水号类型
  serialType.value = optionsCache.data.SERIAL.map((x) => {
    return {
      label: x.name,
      value: x.id,
    }
  })
  formArr.value.forEach((item) => {
    if (item.key === 'barcode_value') {
      item.selectArr = configureType.value
    }
  })
}

const getDataOption = async (val) => {
  const res = await DataItemFields({ barcode_value: val })
  dataOptions.value = res.data || []
  details.value = [{ code_segment: null, value_range: null, length: null, padding_mode: null, padding_char: null, start_serial: null, data_source: null, serial_calc: null }]
}

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapAdd = async (type: string, row: any = '') => {
  switch (type) {
    case 'preview':
      Preview({ code: row.code }).then((res) => {
        visibleData.isShow = true
        visibleData.title = '条码预览结果'
        visibleData.content = `${res.data.preview_value}`
        visibleData.confirmBtnText = '确定'
        visibleData.isCancelBtn = false
        visibleData.okFn = () => {
          visibleData.isShow = false
        }
      })
      break
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      editForm.id = null
      editForm.barcode_value = null
      editForm.code = ''
      editForm.name = ''
      editForm.is_default = false
      editForm.remark = ''
      details.value = [{ code_segment: null, value_range: null, length: null, padding_mode: null, padding_char: null, start_serial: null, data_source: null, serial_calc: null }]
      break
    case 'compiler':
      editForm.id = row.id
      editForm.barcode_value = row.barcode_value
      editForm.code = row.code
      editForm.name = row.name
      editForm.is_default = row.is_default
      editForm.remark = row.remark
      details.value = row.details.map((x) => {
        return {
          ...x,
          value_range: x.code_segment == 2 ? x.value_range : Number(x.value_range),
        }
      })
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除条码生成配置'
      visibleData.content = `是否确认删除条码生成配置？删除前，请先删除关联该条码生成配置的所有数据，否则会操作失败！`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}

const addBtn = (index) => {
  const obj = { code_segment: null, value_range: null, length: null, padding_mode: null, padding_char: null, start_serial: null, data_source: null, serial_calc: null }
  details.value.splice(index + 1, 0, obj)
}
const removeBtn = (index) => {
  details.value.splice(index, 1)
}

const resetRow = (idx) => {
  details.value[idx].value_range = null
  details.value[idx].length = null
  details.value[idx].padding_mode = null
  details.value[idx].padding_char = null
  details.value[idx].start_serial = null
  details.value[idx].data_source = null
  details.value[idx].serial_calc = false
}

const resetRow1 = (idx) => {
  details.value[idx].length = null
  details.value[idx].padding_mode = null
  details.value[idx].padding_char = null
  details.value[idx].start_serial = null
  details.value[idx].data_source = null
  details.value[idx].serial_calc = false
}

const resetRow2 = (idx) => {
  details.value[idx].padding_char = null
  details.value[idx].start_serial = null
}

// 检查details
const checkDetail = () => {
  for (let i = 0; i < details.value.length; i++) {
    const item = details.value[i]
    if (item.code_segment === null) {
      message.error(`第${i + 1}行码段类型为空`)
      return false
    }
    if (item.code_segment === 1 && item.data_source === null) {
      message.error(`第${i + 1}行数据源配置内容为空`)
      return false
    }
    if ((item.code_segment === 2 || item.code_segment === 3) && item.value_range === null) {
      message.error(`第${i + 1}行值域为空`)
      return false
    }
    if (item.code_segment === 4) {
      if (item.value_range === null) {
        message.error(`第${i + 1}行值域为空`)
        return false
      }
      if (item.length === null) {
        message.error(`第${i + 1}行长度不能为空`)
        return false
      }
      if (item.padding_mode === null) {
        message.error(`第${i + 1}行填充模式不能为空`)
        return false
      }
      if ((item.padding_mode === 2 || item.padding_mode === 3) && item.padding_char === null) {
        message.error(`第${i + 1}行填充字符不能为空`)
        return false
      }
      if ((item.padding_mode === 2 || item.padding_mode === 3) && item.start_serial === null) {
        message.error(`第${i + 1}行起始流水号不能为空`)
        return false
      }
    }
  }
  return true
}

const addRole = () => {
  const isPass = checkDetail()
  if (!isPass) {
    return
  }
  const obj = JSON.parse(JSON.stringify(editForm))
  obj.details = details.value.map((item, index) => ({
    sort: index + 1,
    code_segment: item.code_segment,
    value_range: item.code_segment === 2 ? item.value_range : Number(item.value_range),
    length: item.length,
    padding_mode: item.padding_mode,
    padding_char: item.padding_char,
    start_serial: item.start_serial,
    data_source: item.data_source,
    serial_calc: item.serial_calc,
  }))
  Create(obj).then((res) => {
    if (res.success) {
      message.success('新增成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 编辑
const upRoleDate = () => {
  const isPass = checkDetail()
  if (!isPass) {
    return
  }
  const obj = JSON.parse(JSON.stringify(editForm))
  obj.details = details.value.map((item, index) => ({
    id: item.id,
    sort: index + 1,
    code_segment: item.code_segment,
    value_range: item.code_segment === 2 ? item.value_range : Number(item.value_range),
    length: item.length,
    padding_mode: item.padding_mode,
    padding_char: item.padding_char,
    start_serial: item.start_serial,
    data_source: item.data_source,
    serial_calc: item.serial_calc,
  }))
  Update(obj).then((res) => {
    if (res.success) {
      message.success('修改成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

const tempPreview = () => {
  const isPass = checkDetail()
  if (!isPass) {
    return
  }
  const obj = JSON.parse(JSON.stringify(editForm))
  obj.details = details.value.map((item, index) => ({
    id: item.id,
    sort: index + 1,
    code_segment: item.code_segment,
    value_range: item.code_segment === 2 ? item.value_range : Number(item.value_range),
    length: item.length,
    padding_mode: item.padding_mode,
    padding_char: item.padding_char,
    start_serial: item.start_serial,
    data_source: item.data_source,
    serial_calc: item.serial_calc,
  }))
  PreviewTemp(obj).then((res) => {
    if (res.success) {
      visibleData.isShow = true
      visibleData.title = '条码预览结果'
      visibleData.content = `${res.data.preview_value}`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = false
      visibleData.okFn = () => {
        visibleData.isShow = false
      }
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  Delete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}

const tapSwitch = (e, row) => {
  const obj = { id: row.id, is_default: e ? 1 : 0 }
  Enable(obj).then((res) => {
    if (res.success) {
      message.success('操作成功')
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id)
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

import { request } from './request'
// 企业列表
export const Enterprise = (data) => request({ url: '/api/personnel/company/enterprise', data })
// 公司列表
export const Company = (data) => request({ url: '/api/personnel/company/company', data })
// 部门列表
export const Department = (data) => request({ url: '/api/personnel/company/department', data })
// 详情
export const ShowEnterprise = (data) => request({ url: '/api/personnel/company/showEnterprise', data })
// 详情
export const ShowDepartment = (data) => request({ url: '/api/personnel/company/showDepartment', data })
// 详情
export const Show = (data) => request({ url: '/api/personnel/company/show', data })
// // 下拉
// export const SelectEnterprise = (data) => request({ url: '/api/personnel/company/selectEnterprise', data })
// 下拉
export const SelectDepartment = (data) => request({ url: '/api/personnel/company/selectDepartment', data })
// 下拉
export const Select = (data) => request({ url: '/api/personnel/company/select', data })

// 同步
export const SyncEnterprise = (data) => request({ url: '/api/personnel/company/syncEnterprise', data })
// 同步
export const SyncDepartment = (data) => request({ url: '/api/personnel/company/syncDepartment', data })
// 同步
export const Sync = (data) => request({ url: '/api/personnel/company/sync', data })
// 岗位列表
export const Post = (data) => request({ url: '/api/personnel/position/index', data })
// 新增岗位
export const AddPosition = (data) => request({ url: '/api/personnel/position/positionStore', data })
// 编辑岗位
export const PositionUpdate = (data) => request({ url: '/api/personnel/position/positionUpdate', data })
// 分配用户列表
export const GetUserList = (data) => request({ url: '/api/personnel/position/getUserList', data })
// 授权
export const AssignUserPosition = (data) => request({ url: '/api/personnel/position/assignUserPosition', data })
// 状态切换
export const PositionStatus = (data) => request({ url: '/api/personnel/position/positionStatus', data })
// 详情
export const GetPositionInfo = (data) => request({ url: '/api/personnel/position/getPositionInfo', data })
// 删除岗位
export const PositionDelete = (data) => request({ url: '/api/personnel/position/positionDelete', data })

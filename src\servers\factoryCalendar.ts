import { request } from './request'

// 获取工厂日历列表
export const getFactoryCalendars = (data) => request({ url: '/api/businessBase/factoryCalendar/index', data })
// 获取班别/时段
export const getSelectOptions = () => request({ url: '/api/businessBase/factoryCalendar/getSelect' })
// 新建工厂日历
export const addFactoryCalendar = (data) => request({ url: '/api/businessBase/factoryCalendar/store', data })
// 编辑工厂日历
export const editFactoryCalendar = (id, data) => request({ url: `/api/businessBase/factoryCalendar/update/${id}`, data })
// 查看工厂日历
export const viewFactoryCalendar = (id) => request({ url: `/api/businessBase/factoryCalendar/show/${id}` })
// 删除工厂日历
export const deleteFactoryCalendar = (id) => request({ url: `/api/businessBase/factoryCalendar/destroy/${id}`, id })
// 更新工厂日历状态
export const updateCalendarStatus = (id, data) => request({ url: `/api/businessBase/factoryCalendar/updateStatus/${id}`, data })

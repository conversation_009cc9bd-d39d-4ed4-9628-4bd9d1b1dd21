<template>
  <a-modal :open="open" title="删除工艺流程" @cancel="handleCancel">
    <span class="whitespace-pre-line leading-24">{{ content }}</span>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" danger @click="handleSubmit">确认删除</a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { deleteProcedure } from '@/servers/processFlow'

const props = defineProps<{
  open: boolean
  initValue: {
    process_flows_id: number
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const content =
  '您即将删除的工艺流程在生产中起着关键作用。删除后，以下情况将会发生：\n该工艺流程的所有详细步骤、参数设置等信息将被永久删除，无法恢复。\n正在使用此工艺流程的生产任务可能会中断，需要重新安排生产计划和调整工艺参数。\n与该工艺流程相关的历史生产数据统计和分析可能会不完整，影响对生产过程的追溯和评估。\n请确认您已充分了解上述影响，并谨慎考虑后再进行操作。是否继续？'

const handleCancel = () => {
  emits('update:open', false)
}

const handleSubmit = async () => {
  const res = await deleteProcedure({ id: props.initValue.process_flows_id })
  message.success(res.message)
  emits('update:open', false)
  emits('update')
}
</script>

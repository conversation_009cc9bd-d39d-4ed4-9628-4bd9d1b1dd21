import { request } from './request'
// 获取下拉选项
export const GetOptions = (data) => request({ url: '/api/manufacturingCenter/workOrder/optionList', data }, 'GET')
// 获取列表
export const GetList = (data) => request({ url: '/api/manufacturingCenter/workOrder/list', data })
// 新建
export const Add = (data) => request({ url: '/api/manufacturingCenter/workOrder/store', data })
// 查看
export const Details = (data) => request({ url: '/api/manufacturingCenter/workOrder/detail', data })
// 编辑
export const Update = (data) => request({ url: '/api/manufacturingCenter/workOrder/edit', data })
// 删除
export const Delete = (data) => request({ url: '/api/manufacturingCenter/workOrder/delete', data })
// 取消工单
export const Cancel = (data) => request({ url: '/api/manufacturingCenter/workOrder/cancel', data })
// 强制完结工单
export const Closure = (data) => request({ url: '/api/manufacturingCenter/workOrder/closure', data })
// 下达工单
export const Issue = (data) => request({ url: '/api/manufacturingCenter/workOrder/issue', data })
// 下达工单
export const IssueDetails = (data) => request({ url: '/api/manufacturingCenter/workOrder/issueDetails', data })
// 工作中心
export const GetWorkCenterList = (data) => request({ url: '/api/businessBase/workshopWorkcenter/getWorkCenterList', data })

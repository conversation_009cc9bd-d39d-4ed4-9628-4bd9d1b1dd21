import { pageTableConfig } from '@/common/pageTableConfig'
import { GetTableConfig, SetTableConfig } from '@/servers/Common'
import eventBus from '@/utils/eventBus'
import { GetLoginUrl } from '@/servers/User'

const lineHeightType = 2 // 行高类型
/**
 * 数字格式化函数
 *
 * 该函数提供了一种灵活的方式将数字格式化为字符串，包括设置精度、千位分隔符、小数点字符、前缀和后缀
 *
 * @param value 要格式化的数字或数字字符串
 * @param precision 小数点后的位数，默认为 2
 * @param separator 千分位分隔符，默认为 ','
 * @param decimal 小数点字符，默认为 '.'
 * @param prefix 数字前的字符串，默认为 undefined
 * @param suffix 数字后的字符串，默认为 undefined
 * @returns 格式化后的字符串；如果输入值不是数字或字符串，则抛出类型错误
 */
export function formatNumber(value: number | string, precision: number = 2, separator: string = ',', decimal: string = '.', prefix?: string, suffix?: string): string {
  // 类型检查
  if (typeof value !== 'number' && typeof value !== 'string') {
    console.warn('Expected value to be of type number or string')
  }
  if (typeof precision !== 'number') {
    console.warn('Expected precision to be of type number')
  }
  // 处理非数值或NaN的情况
  const numValue = Number(value)
  if (Number.isNaN(numValue) || !Number.isFinite(numValue)) {
    return ''
  }
  if (numValue === 0) {
    return numValue.toFixed(precision)
  }
  let formatValue = numValue.toFixed(precision)
  // 如果 separator 是数值而非字符串，会导致错误，此处进行检查
  if (typeof separator === 'string' && separator !== '') {
    const [integerPart, decimalPart] = formatValue.split('.')
    formatValue = integerPart.replace(/(\d)(?=(\d{3})+$)/g, `$1${separator}`) + (decimalPart ? decimal + decimalPart : '')
  }
  return (prefix || '') + formatValue + (suffix || '')
}

// 节流函数
export const throttle = (func, wait) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  let lastArgs: any[] | null = null

  const throttled = (...args) => {
    if (!timeout) {
      func(...args)
      timeout = setTimeout(() => {
        timeout = null
        if (lastArgs) {
          func(...lastArgs)
          lastArgs = null
        }
      }, wait)
    } else {
      lastArgs = args
    }
  }

  return throttled
}

// 防抖函数
export const debounce = (func, wait) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  return (...args) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * @description 设缓存
 */
export const setLocal = (key, value) => {
  if (typeof value === 'object') value = JSON.stringify(value)

  sessionStorage.setItem(key, value)
}

/**
 * @description 获取缓存
 */
export const getLocal = (key = '') => {
  const value = sessionStorage.getItem(key) || ''
  try {
    return JSON.parse(value)
  } catch (err) {
    console.error(err)
    return value
  }
}

/**
 * @description 清除缓存，如有指定key则删除单个，否则删除全部
 */
export function clearLocal(key) {
  if (key) {
    sessionStorage.removeItem(key)
  } else {
    sessionStorage.clear()
  }
}
/**
 * @description: 生成随机数
 * @return {*}
 */
export const getUnid = () => {
  return Math.random().toString(36).substr(2, 10)
}

/**
 * @description 获取浏览器类型
 */
export const getBrowser = () => {
  const browser = {
    versions: (() => {
      const u = navigator.userAgent
      const platform = navigator.platform
      return {
        trident: u.indexOf('Trident') > -1, // IE内核
        presto: u.indexOf('Presto') > -1, // opera内核
        webKit: u.indexOf('AppleWebKit') > -1, // 苹果、谷歌内核
        gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') === -1, // 火狐内核
        mobile: !!u.match(/AppleWebKit.*Mobile.*/), // 是否为移动终端
        ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios终端
        android: u.indexOf('Android') > -1 || u.indexOf('Adr') > -1, // android终端
        iPhone: u.indexOf('iPhone') > -1, // 是否为iPhone或者QQHD浏览器
        iPad: u.indexOf('iPad') > -1, // 是否iPad
        webApp: u.indexOf('Safari') === -1, // 是否web应该程序，没有头部与底部
        weixin: u.indexOf('MicroMessenger') > -1, // 是否微信 （2015-01-22新增）
        qq: u.match(/\sQQ/i), // 是否QQ
        win: platform.indexOf('Win') > -1,
      }
    })(),
    language: navigator.language.toLowerCase(),
  }
  return browser
}

export const upLoadXlsx = (res, fileName = 'table') => {
  const { data, headers } = res
  const blob = new Blob([data], { type: `${headers['content-type']};charset=utf-8` })
  const dom = document.createElement('a')
  const url = window.URL.createObjectURL(blob)
  dom.href = url
  dom.download = decodeURI(`${fileName}.xlsx`)
  dom.style.display = 'none'
  document.body.appendChild(dom)
  dom.click()
  // dom.parentNode && dom.parentNode.removeChild(dom)
  if (dom.parentNode) {
    dom.parentNode.removeChild(dom)
  }
  window.URL.revokeObjectURL(url)
}

// form表单请求参数校验
export const checkFormParams = (data) => {
  const { formArr, obj = {}, callBack } = data
  formArr.forEach((item) => {
    if (item.formKeys && item.formKeys.length && item.value) {
      item.formKeys.forEach((v, i) => {
        obj[v] = item.value[i] || null
      })
    } else obj[item.key] = item.value

    // if (item.multiple) {
    //   obj[item.key] = Array.isArray(obj[item.key]) ? item.value.join(',') : item.value
    // }
  })
  for (const key in obj) {
    if ((!obj[key] && obj[key] != 0) || obj[key].length < 1) {
      obj[key] = null
    }
  }
  if (callBack) {
    callBack(obj)
  }
  return obj
}
// 保存列表配置
export const setTableConfig = (arr, page_type, line_height) => {
  return new Promise((resolve) => {
    const data = cloneDeep({ maps: arr, page_type, line_height })
    data.maps.sort((a, b) => a.index - b.index)
    data.maps.forEach((item, i) => {
      item.index = i + 1
      if (item.serverIsSort) {
        item.is_sort = true
        item.sort_type = item.sortType
        item.sort_index = item.sortIndex
      } else {
        item.is_sort = false
        item.sort_type = ''
        item.sort_index = 0
      }
      delete item.serverIsSort
      delete item.sortType
      delete item.sortIndex
    })
    SetTableConfig(data).then(() => {
      resolve(1)
    })
  })
}
// 获取列表配置
export const getTableConfig = (page_type, is_def) => {
  return GetTableConfig({ page_type, is_def: is_def || false }).then((res) => {
    res.data.maps.forEach((e) => {
      e.visible = e.is_show
    })
    return res.data
  })
}
// 列表宽度改变
export const tableWColumnWidthChange = (column, tableKey, type, $table) => {
  tableKey.forEach((x) => {
    if (x.key === column.field) {
      x.width = px22(column.renderWidth)
    } else if (!x.width) {
      x.width = px22($table.getTableColumn().fullColumn.find((y) => y.field === x.key)?.renderWidth || 0)
    }
  })
  setTableConfig(tableKey, type, lineHeightType)
}
// 页面内权限配置
export const checkedBtnPermission = async (code?: string) => {
  return new Promise((resolve) => {
    const path = code || useRoute().path
    let navPageArr = localStorage.getItem('navPageArr') as any
    navPageArr = JSON.parse(navPageArr)
    const arr: Record<string, boolean> = {}

    navPageArr.forEach((item) => {
      if (item.path === path) {
        item.btnList?.forEach((btnItem) => {
          arr[`${btnItem.id}`] = true
          console.log(btnItem)
          btnItem.children?.forEach((btnItem2) => {
            arr[`${btnItem2.id}`] = true
            console.log(btnItem2)
          })
        })
      }
    })
    resolve(arr)
  })
}
export const initTable = (pageType, $table, tableKey, lineHeightType, isConfigDefault) => {
  return new Promise((resolve) => {
    getTableConfig(pageType, isConfigDefault)
      .then((res) => {
        lineHeightType.value = res.line_height
        lineHeightType = res.line_height
        if ($table) {
          tableKey.value = []
          setTimeout(() => {
            const defaultTableKey = pageTableConfig[pageType]

            tableKey.value = defaultTableKey
              .map((v) => {
                const val = { ...v }
                const serverObj = res?.maps?.find((x) => x.key === v.key)
                if (serverObj) {
                  Object.assign(val, {
                    index: serverObj.index,
                    width: serverObj.width,
                    is_show: serverObj.is_show,
                    freeze: serverObj.freeze,
                    serverIsSort: serverObj.is_sort,
                    sortType: serverObj.sort_type || 'ASC',
                    sortIndex: serverObj.sort_index,
                  })
                }
                return val
              })
              .sort((a, b) => a.index - b.index)

            setTimeout(() => {
              $table.getTableColumn().fullColumn.forEach((x) => {
                res.maps.forEach((y) => {
                  if (x.field === y.key) {
                    x.visible = y.is_show
                    x.width = x.width ? ((window.innerWidth * Number(x.width)) / 1920).toFixed(0) : null
                  }
                })
              })
              $table.refreshColumn()
              resolve(tableKey.value)
            }, 0)
          }, 0)
        }
      })
      .catch((e) => {
        console.log(e)

        if ($table) {
          tableKey.value = []
          setTimeout(() => {
            const defaultTableKey = pageTableConfig[pageType]
            tableKey.value = defaultTableKey
            setTimeout(() => {
              $table.getTableColumn().fullColumn.forEach((x) => {
                defaultTableKey.forEach((y) => {
                  if (x.field === y.key) {
                    x.visible = y.is_show
                    x.width = x.width ? ((window.innerWidth * Number(x.width)) / 1920).toFixed(0) : null
                  }
                })
              })
              $table.refreshColumn()
              resolve(true)
            }, 0)
          }, 0)
        }
        resolve(true)
      })
  })
}

// tableKey补上原表格配置
export const flattenTableColumn = (params) => {
  const { tableKey, pageType } = params
  if (!pageType) return []
  const defaultTableKey = pageTableConfig[pageType]
  return tableKey.map((v) => {
    const found = defaultTableKey.find((f) => f.key === v.key) || {}
    return {
      ...found,
      ...v,
    }
  })
}

export const validateStr = async (_rule, value, num) => {
  let limit = 200
  if (typeof num == 'number') limit = num
  if (String(value).length > limit) {
    return Promise.reject()
  }
  return Promise.resolve()
}

export const validateFile = async (_rule, value) => {
  console.log(value)

  if (value.length == 0) {
    console.log(1)

    return Promise.reject()
  }
  console.log(2)

  return Promise.resolve()
}

export const validateLink = async (_rule, value) => {
  if (!/^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/.test(value) && value != null && value != '') {
    return Promise.reject()
  }
  return Promise.resolve()
}

export const validateMail = async (_rule, value) => {
  if (!/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/.test(value) && value != '' && value != null) {
    return Promise.reject()
  }
  return Promise.resolve()
}

// 校验是否包含中文
export const validCode = (_rule, val) => {
  const reg = /^[^\u4e00-\u9fa5]*$/
  if (!reg.test(val.trim() || '')) {
    return Promise.reject()
  }
  return Promise.resolve()
}

export const imgLoadErrorFallBack = () => {
  return 'data:image/png;base64,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'
}
export const getUrlParams = (name) => {
  const queryString = window.location.search || window.location.hash.split('?')[1]
  const urlParams = new URLSearchParams(queryString)
  return urlParams.get(name)
}
export const beforLogout = () => {
  const keysToKeep = Object.keys(localStorage).filter((key) => ['userPsd', 'userAccount', 'rememberUserPsd', 'autoLogin', 'showAuditRecord'].indexOf(key) != -1)
  const obj = {}
  keysToKeep.forEach((key) => {
    const value = localStorage.getItem(key)
    obj[key] = value
  })
  localStorage.clear()
  for (const key in obj) {
    localStorage.setItem(key, obj[key])
  }
  eventBus.emit('clearWatermark')
}

export const jumpUMCForLogin = () => {
  GetLoginUrl().then((res) => {
    window.location.href = res.data
  })
}

// px 根据 rem缩放
export const px2 = (px) => {
  return +((window.innerWidth * Number(px)) / 1920).toFixed(0)
}

export const px22 = (px) => {
  return ((px * 1920) / window.innerWidth).toFixed(0)
}

export const sumNum = (list: any[], field: string) => {
  let count = 0
  list.forEach((item) => {
    if (item[field]) {
      count += Number(formatNum(item[field]))
    }
  })
  return number2(count)
}

// 将千分符的数字转换为数字 如果不是数字返回0
export const formatNum = (num) => {
  return `${num}`.replace(/,/g, '') || 0
}

/**
 * @description 根据value获取option的label
 * @param value 值
 * @param options 选项
 * @returns
 */
export const formatOptionLabel = (value, options) => {
  const option = options.find((item) => item.value == value)
  return option ? option.label : ''
}

/**
 * @description  数字格式化
 * @param {number} num 数字
 * @param {number} n 小数点后位数
 */
export const number2 = (num, n = 2) => {
  return num ? num.toFixed(n).replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0.00'
}

export const urlFormat = (url) => {
  url = url.startsWith('/') ? url : `/${url}`

  url = url.startsWith('http') ? url : `${import.meta.env.VITE_APP_BASE_API}${url}`

  return url
}

export const download = (res, fileName = 'table') => {
  const { data, headers } = res
  const blob = new Blob([data], { type: `${headers['content-type']};charset=utf-8` })
  const dom = document.createElement('a')
  const url = window.URL.createObjectURL(blob)
  dom.href = url
  dom.download = decodeURI(`${fileName}`)
  dom.style.display = 'none'
  document.body.appendChild(dom)
  dom.click()
  // dom.parentNode && dom.parentNode.removeChild(dom)
  if (dom.parentNode) {
    dom.parentNode.removeChild(dom)
  }
  window.URL.revokeObjectURL(url)
}

export const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

/**
 * 支持自定义字段筛选，过滤选项
 * @param input 输入内容
 * @param option 选项
 * @param key 选项的key
 * @returns
 */
export const customFilterOption = (input: string, option: any, key: string = 'label') => {
  return option[key].toLowerCase().indexOf(input.toLowerCase()) >= 0
}

export const cloneDeep = (data: any) => JSON.parse(JSON.stringify(data))

<template>
  <div class="transferBox">
    <div class="leftBox">
      <div class="titleBox">
        <div>不启用的角色</div>
        <span class="description">{{ getBeforeMoveTotal() }}/{{ getCanMoveTotal() }}条</span>
      </div>
      <div class="contentBox">
        <a-input v-model:value="leftBoxSearchValue" placeholder="搜索">
          <template #suffix><SearchOutlined /></template>
        </a-input>
        <div class="rightArr">
          <div
            @click="
              () => {
                item.disabled ? null : (item.checked = !item.checked)
              }
            "
            class="selectedItem"
            :class="{ cantuseItem: item.status == 0 }"
            v-for="(item, index) in filterOption(
              leftBoxSearchValue,
              leftArr.filter((e) => !e.disabled),
            )"
            :key="index"
          >
            <a-checkbox :disabled="item.disabled" @click.stop="null" v-model:checked="item.checked"></a-checkbox>
            <span :class="{ cantuseLabel: item.status == 0 }">{{ item.label }}</span>
            <span class="cantuseLabel" v-show="item.status == 0" style="white-space: nowrap">(停用)</span>
          </div>
          <div
            v-show="
              filterOption(
                leftBoxSearchValue,
                leftArr.filter((e) => !e.disabled),
              ).length === 0
            "
            class="emtpyContent"
          >
            暂无数据
          </div>
        </div>
      </div>
    </div>
    <div class="btns">
      <div @click="getBeforeMoveTotal() === 0 ? null : toRight()" class="btn" :class="{ disable: getBeforeMoveTotal() === 0 }"><RightOutlined /></div>
      <div @click="rightArr.filter((e) => e.checked).length == 0 ? null : toLeft()" class="btn" :class="{ disable: rightArr.filter((e) => e.checked).length == 0 }"><LeftOutlined /></div>
    </div>
    <div class="rightBox">
      <div class="titleBox">
        <div>启用的角色</div>
        <span class="description">{{ rightArr.filter((e) => e.checked).length }}/{{ rightArr.length }}条</span>
      </div>
      <div class="contentBox">
        <a-input placeholder="搜索" v-model:value="rightBoxSearchValue">
          <template #suffix><SearchOutlined /></template>
        </a-input>
        <div class="rightArr">
          <div
            @click="
              () => {
                item.checked = !item.checked
              }
            "
            class="selectedItem"
            :class="{ cantuseItem: item.status == 0 }"
            v-for="(item, index) in filterOption(rightBoxSearchValue, rightArr)"
            :key="index"
          >
            <a-checkbox @click.stop="null" v-model:checked="item.checked"></a-checkbox>
            <span :class="{ cantuseLabel: item.status == 0 }">{{ item.label }}</span>
            <span class="cantuseLabel" v-show="item.status == 0">(停用)</span>
          </div>
          <div v-show="filterOption(rightBoxSearchValue, rightArr).length === 0" class="emtpyContent">暂无数据</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { pinyin } from 'pinyin-pro'
import { LeftOutlined, RightOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { cloneDeep } from 'lodash'

const props = defineProps({
  options: {
    type: Array<any>,
    default: [],
  },
  value: {
    type: Array<any>,
    default: [],
  },
})
const emit = defineEmits(['update:value'])
const rightArr = ref<any[]>([])
const rightBoxSearchValue = ref('')
const leftBoxSearchValue = ref('')
const leftArr = ref<any[]>([])
const toRight = () => {
  const arr: any = cloneDeep(leftArr.value.filter((e) => e.checked))
  arr.forEach((e: any) => {
    e.checked = false
  })
  rightArr.value = arr
  leftArr.value.forEach((x: any) => {
    if (x.checked) {
      x.disabled = true
    }
  })
  emit(
    'update:value',
    rightArr.value.map((e: any) => e.value),
  )
}
const filterOption = (input: string, options: any[]) => {
  if (!input) return options
  const arr: any[] = []
  options.forEach((option: any) => {
    const label = option.label.toLowerCase()
    const fullPinyin = pinyin(option.label, { toneType: 'none' }).replace(/\s+/g, '').toLowerCase()
    const firstPinyin = pinyin(option.label, { toneType: 'none' })
      .split(/\s+/)
      .map((word) => word.charAt(0))
      .join('')
    if (label.indexOf(input.toLowerCase()) >= 0 || firstPinyin.indexOf(input.toLowerCase()) >= 0 || fullPinyin.indexOf(input.toLowerCase()) >= 0) {
      arr.push(option)
    }
  })
  return arr
}
const getBeforeMoveTotal = () => {
  return leftArr.value.filter((e) => e.checked && !e.disabled).length
}
const getCanMoveTotal = () => {
  return leftArr.value.filter((e) => !e.disabled).length
}
const toLeft = () => {
  rightArr.value.forEach((x: any) => {
    leftArr.value.forEach((y: any) => {
      if (x.value == y.value && x.checked) {
        y.disabled = false
        y.checked = false
      }
    })
  })
  rightArr.value = rightArr.value.filter((e: any) => !e.checked)
  emit(
    'update:value',
    rightArr.value.map((e: any) => e.value),
  )
}
onMounted(() => {
  leftArr.value = cloneDeep(props.options)
})
watch(
  () => props.value,
  () => {
    leftArr.value.forEach((x) => {
      if (props.value.indexOf(x.value) != -1) {
        x.checked = true
        x.disabled = true
      }
    })
    rightArr.value = cloneDeep(leftArr.value.filter((e) => e.checked))
    rightArr.value.forEach((x) => {
      x.checked = false
      x.disabled = false
    })
  },
)
</script>

<style lang="scss" scoped>
.transferBox {
  display: flex;
  gap: 24px;
  min-height: 500px;
  max-height: 500px;

  .leftBox {
    flex: 1;
    overflow: hidden;
    border: 1px solid #e7e7e7;

    .contentBox {
      height: calc(100% - 40px);
    }
  }

  .rightBox {
    flex: 1;
    border: 1px solid #e7e7e7;

    .contentBox {
      height: calc(100% - 40px);
    }
  }

  .btns {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    justify-content: center;

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      font-size: 12px;
      color: #000;
      cursor: pointer;
      background-color: rgb(0 0 0 / 3.5%);
      border-radius: 50%;
      transition: all 0.3s;

      &:hover {
        background-color: rgb(0 0 0 / 2%);
      }
    }

    .disable {
      color: rgb(0 0 0 / 50%);
      cursor: not-allowed;
      background-color: rgb(0 0 0 / 2%);

      &:hover {
        background-color: rgb(0 0 0 / 2%);
      }
    }
  }

  .titleBox {
    justify-content: space-between;
    padding: 8px 12px;
    background-color: rgb(0 0 0 / 2%);
    border-bottom: 1px solid #e7e7e7;

    .description {
      color: rgb(0 0 0 / 70%);
    }
  }

  .radioBox {
    width: 100%;
    padding: 8px 12px 0;
  }

  .contentBox {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
    overflow-y: auto;

    .rightArr {
      display: flex;
      flex-direction: column;
      gap: 4px;
      color: #000;

      .selectedItem {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 4px 8px;
        cursor: pointer;
        border-radius: 2px;
        transition: all 0.3s;

        &:hover {
          background-color: rgb(0 0 0 / 5%);
        }
      }

      .cantuseItem {
        &:hover {
          background-color: rgb(255 77 79 / 10%);
        }
      }
    }
  }

  .ant-radio-group {
    display: flex;
    width: 100%;

    .ant-radio-button-wrapper {
      display: flex;
      flex: 1;
      justify-content: center;
      white-space: nowrap;
    }
  }
}

.loadingIcon {
  font-size: 30px;
  color: #1890ff;
}

.drawerContent {
  height: 100%;
  padding: 4px 12px;
}

.detailTitle {
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.footerBox {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.titleBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;

  .title {
    font-size: 15px;
    line-height: 46px;
  }
}

::v-deep(.ant-tree-list) {
  .ant-tree-treenode {
    width: 100%;
  }
}

::v-deep(.ant-tree-node-content-wrapper) {
  display: inline-block;
  width: 100%;

  &:hover {
    background-color: rgb(0 0 0 / 5%);
  }
}

.emtpyContent {
  margin-top: 16px;
  font-size: 14px;
  color: rgb(0 0 0 / 50%);
  text-align: center;
}

.cantuseLabel {
  color: rgb(0 0 0 / 50%);
}
</style>

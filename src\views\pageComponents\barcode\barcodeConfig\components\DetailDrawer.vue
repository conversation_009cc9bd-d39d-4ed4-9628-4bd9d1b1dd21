<template>
  <a-drawer
    :footer="false"
    v-model:open="detailVisible"
    :width="'65vw'"
    title="查看条码生成配置"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" header="" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="12">
                <p class="label">配置类型</p>
                <p class="value">{{ configureType?.find((i) => i.value == target.barcode_value)?.label }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">配置编码</p>
                <p class="value">{{ target.code }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">配置名称</p>
                <p class="value">{{ target.name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">是否默认</p>
                <p class="value">{{ target.is_default ? '是' : '否' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="24">
                <p class="label">备注</p>
                <div class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: '300px' }">
                    <template #title>
                      {{ target.remark }}
                    </template>
                    {{ target.remark }}
                  </a-tooltip>
                </div>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="条码生成配置明细信息" :style="customStyle">
            <a-row :gutter="16">
              <div class="w-full">
                <SimpleTable :tableKey="tableKey" :data="target.details">
                  <template #code_segment="{ row }">
                    <span>{{ codeSegmentType?.find((i) => i.value == row.code_segment)?.label }}</span>
                  </template>
                  <template #value_range="{ row }">
                    <span v-if="row.code_segment == 3">{{ dateType?.find((i) => i.value == row.value_range)?.label }}</span>
                    <span v-else-if="row.code_segment == 4">{{ serialType?.find((i) => i.value == row.value_range)?.label }}</span>
                    <span v-else>{{ row.value_range }}</span>
                  </template>
                  <template #padding_mode="{ row }">
                    <span>{{ fillType?.find((i) => i.value == row.padding_mode)?.label }}</span>
                  </template>
                  <template #serial_calc="{ row }">
                    <a-checkbox v-model:checked="row.serial_calc" disabled></a-checkbox>
                  </template>
                </SimpleTable>
              </div>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="3" header="其他信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="24">
                <p class="label">创建时间</p>
                <p class="value">{{ target.created_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="24">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.creator?.real_name }}</div>
                      <div>所在部门：{{ target?.creator?.department_name ? target?.creator?.department_name : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.creator?.position_name ? target?.creator?.position_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.creator?.real_name ? target?.creator?.real_name : '--' }}</span>
                      <span v-if="target?.creator?.department_name || target?.creator?.position_name" class="detailValueDescription">
                        （
                        <span v-if="target?.creator?.position_name">{{ target?.creator?.position_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.creator?.department_name">
                          {{ target?.creator?.department_name.length > 10 ? target?.creator?.department_name.slice(0, 10) + '...' : target?.creator?.department_name }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="24">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target.updated_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="24">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.modifier?.real_name }}</div>
                      <div>所在部门：{{ target?.modifier?.department_name ? target?.modifier?.department_name : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.modifier?.position_name ? target?.modifier?.position_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.modifier?.real_name ? target?.modifier?.real_name : '--' }}</span>
                      <span v-if="target?.modifier?.department_name || target?.modifier?.position_name" class="detailValueDescription">
                        （
                        <span v-if="target?.modifier?.position_name">{{ target?.modifier?.position_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.modifier?.department_name">
                          {{ target?.modifier?.department_name.length > 10 ? target?.modifier?.department_name.slice(0, 10) + '...' : target?.modifier?.department_name }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { Show, GetOptions, DataItemFields } from '@/servers/barcodeConfig'
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'

const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'
const activeKey = ref(['1', '2', '3'])
const detailVisible = ref(false)
const detailloading = ref(false)
const configureType = ref([])
const fillType = ref([])
const codeSegmentType = ref([])
const dateType = ref([])
const serialType = ref([])
const dataOptions = ref([])
const target = ref<any>(null)
const tableKey = [
  { title: '配置顺序', field: 'sort', width: 80 },
  { title: '码段', field: 'code_segment' },
  { title: '值域', field: 'value_range' },
  { title: '长度', field: 'length' },
  { title: '填充方式', field: 'padding_mode' },
  { title: '填充字符', field: 'padding_char' },
  { title: '起始流水号', field: 'start_serial' },
  { title: '数据源配置内容', field: 'data_source' },
  { title: '流水号计算', field: 'serial_calc' },
]
const open = async (id) => {
  await getOptions()

  target.value = null
  detailloading.value = true
  detailVisible.value = true

  Show({ id })
    .then((res) => {
      target.value = res.data
      getDataOption()
      detailloading.value = false
    })
    .catch(() => {
      detailloading.value = false
    })
}

const getOptions = async () => {
  const optionsCache = await GetOptions({})
  // 配置类型
  configureType.value = optionsCache.data.CO_CONFIGURE_TYPE.map((x) => {
    return {
      label: x.key,
      value: x.value,
    }
  })
  // 填充类型
  fillType.value = optionsCache.data.CO_FILL_TYPE
  // 码段类型
  codeSegmentType.value = optionsCache.data.CO_CODE_SEGMENT
  // 日期类型
  dateType.value = optionsCache.data.DATE.map((x) => {
    return {
      label: x.name,
      value: x.id,
    }
  })
  // 流水号类型
  serialType.value = optionsCache.data.SERIAL.map((x) => {
    return {
      label: x.name,
      value: x.id,
    }
  })
}

const getDataOption = async () => {
  const res = await DataItemFields({ barcode_value: target.value?.barcode_value })
  dataOptions.value = res.data || []
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

.value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

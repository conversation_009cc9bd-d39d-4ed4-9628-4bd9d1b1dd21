import { createRouter, createWebHashHistory } from 'vue-router'
import otherRouter from './otherRouter'

const routes = [
  {
    path: '/',
    name: '首页',
    component: () => import('@/views/index.vue'),
    children: otherRouter,
  },
  /*  {
    path: '/login',
    name: '登录',
    component: () => import('@/views/login.vue'),
  }, */
  {
    path: '/logout',
    name: '退出登录',
    component: () => import('@/views/logout.vue'),
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior() {
    return { top: 0 }
  },
})

export default router

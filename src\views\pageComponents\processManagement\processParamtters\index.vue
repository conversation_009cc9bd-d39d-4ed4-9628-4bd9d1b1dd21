<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.PROCESS_PARAMTTERS" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.PROCESS_PARAMTTERS" :get-list="GetList" :isIndex="true" :formFormat="formFormat">
      <template #right-btn>
        <a-button type="primary" @click="tapAdd('add')" :icon="h(PlusOutlined)" v-if="btnPermission[470001]">新建工序参数</a-button>
      </template>
      <template #created_at="{ row }">
        <span>{{ row.created_at ? row.created_at.slice(0, 16) : '' }}</span>
      </template>
      <template #updated_at="{ row }">
        <span>{{ row.updated_at ? row.updated_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row, rowIndex }">
        <div class="btnBox">
          <a-button @click="detail(row, rowIndex)" class="btn" v-if="btnPermission[470003]">查看</a-button>
          <a-dropdown>
            <a-button v-if="btnPermission[470002] || btnPermission[470004]">更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('compiler', row, rowIndex)" v-if="btnPermission[470002]">编辑</a-menu-item>
                <a-menu-item @click="tapAdd('removes', row)" v-if="btnPermission[470004]">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="520"
      :title="roleModuleType == 'add' ? '新建工序参数' : '编辑工序参数'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="序号" name="idx" v-if="roleModuleType == 'compiler'">
          <span>{{ idx }}</span>
        </a-form-item>
        <a-form-item label="生产阶别" name="stage_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.stage_id" placeholder="请选择" :options="stageOptions"></a-select>
        </a-form-item>
        <a-form-item label="工序类别" name="category_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.category_id" placeholder="请选择" :options="categoryOptions"></a-select>
        </a-form-item>
        <a-form-item label="参数编码" name="parameter_code">
          <a-input v-model:value="editForm.parameter_code" placeholder="请输入" :maxlength="50" />
        </a-form-item>
        <a-form-item label="参数名称" name="parameter_name">
          <a-input v-model:value="editForm.parameter_name" placeholder="请输入" :maxlength="50" />
        </a-form-item>
        <a-form-item label="参数类型" name="type_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.type_id" placeholder="请选择" :options="paramOptions" @change="changeType"></a-select>
        </a-form-item>
        <a-form-item>
          <template #label>
            <a-tooltip>
              <template #title>
                <p class="text-red">注意：</p>
                <p>1.数值型：仅填数值</p>
                <p>2.数值范围型：仅填数值范围，例如（1,9）</p>
                <p>3.布尔型：仅填是 / 否</p>
                <p>4.字符型：仅填字符，例如6</p>
                <p>5.字符串型：仅填字符，例如是66</p>
              </template>
              参数值
              <QuestionCircleFilled class="text-black" />
            </a-tooltip>
          </template>

          <div class="w-full">
            <SimpleTable :tableKey="tableKey" :data="details">
              <template #value="{ row }">
                <a-select v-model:value="row.value" v-if="editForm.type_id == 4">
                  <a-select-option :key="'是'" :value="'是'">{{ '是' }}</a-select-option>
                  <a-select-option :key="'否'" :value="'否'">{{ '否' }}</a-select-option>
                </a-select>
                <a-input v-else v-model:value.trim="row.value" placeholder="请输入" :maxlength="30" />
              </template>
              <template #operate="{ rowIndex }">
                <a-button size="small" @click="addBtn(rowIndex)" type="primary">添加</a-button>
                <a-popconfirm title="确认删除吗?" ok-text="确定" cancel-text="取消" @confirm="removeBtn(rowIndex)">
                  <a-button size="small" v-if="details.length > 1" class="ml-10px" type="primary" danger>删除</a-button>
                </a-popconfirm>
              </template>
            </SimpleTable>
          </div>
        </a-form-item>
        <a-form-item label="备注" name="remarks">
          <a-input v-model:value="editForm.remarks" placeholder="请输入" :maxlength="200" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { h, onMounted } from 'vue'
import { PlusOutlined, QuestionCircleFilled } from '@ant-design/icons-vue'
import { Add, Delete, GetList, Update, GetOptions } from '@/servers/processParamtters'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()
const formFormat = (data) => {
  return {
    ...data,
    sort_field: 'created_at',
    sort_asc: false,
  }
}
const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  stage_id: [{ required: true, message: '请选择生产阶别', trigger: ['change', 'blur'] }],
  category_id: [{ required: true, message: '请选择工序类别', trigger: ['change', 'blur'] }],
  parameter_code: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  parameter_name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  type_id: [{ required: true, message: '请选择参数类型', trigger: ['change', 'blur'] }],
}

const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索参数编码',
    value: '',
    type: 'input',
    key: 'parameter_code',
  },
  {
    label: '请输入参数名称',
    value: '',
    type: 'input',
    key: 'parameter_name',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
  {
    label: '生产阶别',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'stage_id',
    multiple: true,
  },
  {
    label: '工序类别',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'category_id',
    multiple: true,
  },
  {
    label: '参数类型',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'type_id',
    multiple: true,
  },
])
const stageOptions = ref([])
const categoryOptions = ref([])
const paramOptions = ref([])
const idx = ref(0)
const tableKey = [
  { title: '参数值', field: 'value' },
  { title: '操作', field: 'operate', width: 150 },
]
const editForm = reactive({
  id: null,
  stage_id: null,
  category_id: null,
  parameter_code: '',
  parameter_name: '',
  type_id: null,
  remarks: '',
})
const details: any = ref([])
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.PROCESS_PARAMTTERS) {
    const arr: any[] = []
    obj.PROCESS_PARAMTTERS.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  getOptions()
  search()
  initScreening()
})

const getOptions = () => {
  GetOptions({}).then((res) => {
    stageOptions.value =
      res.data.stage_type.map((i) => {
        return {
          label: i.key,
          value: i.id,
        }
      }) || []

    categoryOptions.value =
      res.data.category_type.map((i) => {
        return {
          label: i.key,
          value: i.id,
        }
      }) || []

    paramOptions.value =
      res.data.param_type.map((i) => {
        return {
          label: i.key,
          value: i.id,
        }
      }) || []

    formArr.value.forEach((item) => {
      if (item.key === 'stage_id') {
        item.selectArr = stageOptions.value
      } else if (item.key === 'category_id') {
        item.selectArr = categoryOptions.value
      } else if (item.key === 'type_id') {
        item.selectArr = paramOptions.value
      }
    })
  })
}

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapAdd = (type: string, row: any = '', rowIndex: number = 0) => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      editForm.id = null
      editForm.stage_id = null
      editForm.category_id = null
      editForm.parameter_code = ''
      editForm.parameter_name = ''
      editForm.type_id = null
      editForm.remarks = ''
      details.value = [{ id: null, value: null }]
      break
    case 'compiler':
      idx.value = rowIndex + 1
      editForm.id = row.id
      editForm.stage_id = row.stage_id
      editForm.category_id = row.category_id
      editForm.parameter_code = row.parameter_code
      editForm.parameter_name = row.parameter_name
      editForm.type_id = row.type_id
      editForm.remarks = row.remarks
      details.value = row.parameter_value || []
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除工序参数'
      visibleData.content = `是否确认删除工序参数？删除前，请先删除关联该工序参数的所有数据，否则会操作失败！`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}
// 详情
const detail = (item, idx) => {
  detailDrawerRef.value?.open(item.id, idx + 1)
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

// 新增
const addRole = () => {
  const isPass = checkDetail()
  if (!isPass) {
    return
  }
  const obj = JSON.parse(JSON.stringify(editForm))
  obj.parameter_value = details.value
  Add(obj).then((res) => {
    if (res.success) {
      message.success('新增成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 编辑
const upRoleDate = () => {
  const isPass = checkDetail()
  if (!isPass) {
    return
  }
  const obj = JSON.parse(JSON.stringify(editForm))
  obj.parameter_value = details.value
  Update(obj).then((res) => {
    if (res.success) {
      message.success('修改成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  Delete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
const changeType = () => {
  details.value = []
  const label = paramOptions.value.find((i) => i.value == editForm.type_id)?.label || ''
  console.log(label, 'label')

  if (label == '布尔型') {
    details.value = [{ value: '是' }, { value: '否' }]
  } else {
    details.value = [{ value: null }]
  }
}

const addBtn = (index) => {
  const label = paramOptions.value.find((i) => i.value == editForm.type_id)?.label || ''
  if (label == '布尔型' && details.value.length == 2) {
    message.error('布尔型参数只能有两个值')
    return
  }
  const obj = { value: null }
  details.value.splice(index + 1, 0, obj)
}

const removeBtn = (index) => {
  details.value.splice(index, 1)
}

const checkDetail = () => {
  for (let i = 0; i < details.value.length; i++) {
    const item = details.value[i]
    if (!item.value) {
      message.error(`第${i + 1}行参数值为空`)
      return false
    }
  }
  return true
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

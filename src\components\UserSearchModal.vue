<template>
  <a-modal :open="openModal" title="选择用户" width="840px" :maskClosable="false" @cancel="handleCancel">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="all" tab="全部" />
      <a-tab-pane v-if="isNeedSelectedTab" key="selected" tab="已选" force-render />
    </a-tabs>
    <!-- 已选不设置条件查询 -->
    <div v-if="activeKey === 'all'" class="search-form flex gap-8 mb-8">
      <div class="form-input flex w-80% gap-8">
        <a-input class="flex-1" v-model:value="formState.user_name" allow-clear placeholder="搜索工号" />
        <a-select
          class="flex-1"
          v-model:value="formState.real_name"
          show-search
          placeholder="搜索真实姓名"
          :options="companyUserOptions"
          :filter-option="(input, option) => customFilterOption(input, option, 'real_name')"
          :field-names="{ label: 'real_name', value: 'real_name' }"
        />
        <a-select class="flex-1" v-model:value="formState.department_id" show-search placeholder="搜索部门" :options="departmentOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.position_id" show-search placeholder="搜索岗位" :options="positionOptions" :filter-option="customFilterOption" />
      </div>
      <div class="form-button flex-1">
        <a-button class="btn" type="primary" @click="handleSearchUser" style="margin-right: 10px">查询</a-button>
        <a-button class="btn" @click="handleReset">重置</a-button>
      </div>
    </div>
    <vxe-table
      :data="activeKey === 'all' ? allTableData : selectedTableData"
      border
      align="center"
      stripe
      ref="tableRef"
      max-height="320px"
      min-height="0"
      size="mini"
      show-overflow
      :row-config="{ keyField: 'account_id' }"
      :checkbox-config="{ reserve: true }"
      @radio-change="handleRadioChange"
      @checkbox-all="handleCheckboxChange"
      @checkbox-change="handleCheckboxChange"
    >
      <vxe-column v-if="type === 'multiple'" type="checkbox" width="50" fixed="left" align="center" />
      <vxe-column v-else type="radio" width="50" fixed="left" align="center" />
      <vxe-column field="company_name" title="组织机构" minWidth="180" />
      <vxe-column field="user_name" title="工号" minWidth="100" />
      <vxe-column field="real_name" title="真实姓名" minWidth="120" />
      <vxe-column field="department_name" title="部门" minWidth="160" />
      <vxe-column field="position_name" title="岗位" minWidth="120" />
    </vxe-table>

    <!-- 全部数据分页 -->
    <div v-if="activeKey === 'all'" class="flex items-center my-8">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="tableParams.total"
          show-size-changer
          v-model:current="tableParams.page"
          v-model:pageSize="tableParams.pageSize"
          :page-size-options="pageSizeOptions"
          size="small"
          @change="handleChangePage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ tableParams.total }}</span>
        </span>
      </div>
    </div>
    <!-- 已选数据分页 -->
    <div v-else class="flex items-center my-8">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="selectedTableParams.total"
          show-size-changer
          v-model:current="selectedTableParams.page"
          v-model:pageSize="selectedTableParams.pageSize"
          :page-size-options="pageSizeOptions"
          size="small"
          @change="handleChangeSelectedPage"
          @showSizeChange="handleChangeSelectedPage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ selectedTableParams.total }}</span>
        </span>
      </div>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { VxeTableEvents } from 'vxe-table'
import { getAccounts, getDepartmentOptions, getPositionOptions, getSelectOptions } from '@/servers/userSearch'
import { customFilterOption } from '@/utils/index'

const props = withDefaults(
  defineProps<{
    openModal: boolean
    type?: string
    data?: any
    isNeedSelectedTab?: boolean
  }>(),
  {
    openModal: false,
    type: 'multiple',
    data: [],
    isNeedSelectedTab: true,
  },
)

const activeKey = ref('all')

const formState = reactive({
  user_name: '',
  real_name: null,
  department_id: [],
  position_id: [],
})

const tableRef = ref()
const pageSizeOptions = ['20', '50', '100', '250']

const allTableData = ref<any>([]) // 全部数据-分页
/** 全部表格分页参数 */
const tableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})
/** 全部-切换分页 */
const handleChangePage = () => {
  getAccountList()
}

const selectedTableData = ref<any>(props.data) // 已选数据-分页
const allSelectedTableData = ref<any>(props.data) // 已选数据-全部
/** 已选表格分页参数 */
const selectedTableParams = reactive({
  page: 1,
  pageSize: 20,
  total: props.data?.length || 0,
})
/** 已选-切换分页-假分页 */
const handleChangeSelectedPage = () => {
  selectedTableData.value = allSelectedTableData.value?.slice((selectedTableParams.page - 1) * selectedTableParams.pageSize, selectedTableParams.page * selectedTableParams.pageSize)
}

const emits = defineEmits(['update:openModal', 'getAccounts'])

/** 表单查询 */
const handleSearchUser = () => {
  tableParams.page = 1
  getAccountList()
}

/** 单选-获取表格选中数据 */
const handleRadioChange: VxeTableEvents.RadioChange<any> = ({ row }) => {
  allSelectedTableData.value = [{ ...row }]
  selectedTableParams.total = allSelectedTableData.value?.length || 0
  handleChangeSelectedPage()
}

/** 多选-获取表格选中数据 */
const handleCheckboxChange = () => {
  const $table = tableRef.value
  if ($table) {
    const currentSelectedRows = $table.getCheckboxRecords()
    const otherSelectedRows = $table.getCheckboxReserveRecords()
    allSelectedTableData.value = [...currentSelectedRows, ...otherSelectedRows]
    selectedTableParams.total = allSelectedTableData.value?.length || 0
    handleChangeSelectedPage()
  }
}

/** 重置 */
const handleReset = () => {
  formState.user_name = ''
  formState.real_name = null
  formState.department_id = []
  formState.position_id = []
  tableParams.page = 1
  getAccountList()
}

const handleCancel = () => {
  emits('update:openModal', false)
}

const handleSubmit = () => {
  if (!allSelectedTableData.value) {
    message.error('请选择用户！')
    return
  }
  emits('getAccounts', allSelectedTableData.value)
  emits('update:openModal', false)
}

/** 获取用户信息列表 */
const getAccountList = async () => {
  const { total, ...otherParams } = tableParams
  const params = {
    ...formState,
    ...otherParams,
  }
  const res = await getAccounts(params)
  allTableData.value = res.data.list || []
  tableParams.total = res.data?.total || 0
  nextTick(() => {
    restoreSelection()
  })
}

/** 恢复复选框回显 */
const restoreSelection = () => {
  const $table = tableRef.value
  console.log('props.data[0]:', props.data[0])
  if (props.data?.length > 0) {
    if (props.type === 'multiple') {
      $table.setCheckboxRow(props.data, true)
    } else {
      $table.setRadioRow(props.data[0])
    }
  }
}

const departmentOptions = ref()
/** 获取部门信息 */
const getDepartmentList = async () => {
  const res = await getDepartmentOptions({})
  departmentOptions.value = res.data?.map((item) => ({ value: item.id, label: item.name })) || []
}

const positionOptions = ref()
/** 获取岗位下拉列表 */
const getPositionList = async () => {
  const res = await getPositionOptions({})
  positionOptions.value = res.data?.list?.map((item) => ({ value: item.id, label: item.name })) || []
}

const companyUserOptions = ref()
/** 获取本公司人员 */
const getCompanyUserList = async () => {
  const res = await getSelectOptions({})
  companyUserOptions.value = res.data?.user || []
}

onMounted(() => {
  getAccountList()
  getDepartmentList()
  getPositionList()
  getCompanyUserList()
})
</script>

<style lang="scss" scoped>
.page-number {
  color: #409eff;
}
</style>

<template>
  <div class="tagFilerBox">
    <div class="label" v-show="label">{{ label }}：</div>
    <div style="display: flex; flex-wrap: wrap; gap: 12px">
      <a-button
        @click="itemClick(item)"
        v-for="(item, index) in data"
        :key="index"
        :style="`${targetItems.find((e) => e === item.value) ? 'background-color:#fff;border-color:#40a9ff;color:#40a9ff;' : 'background-color:#f5f5f5;'}`"
        type="text"
      >
        {{ item.label }}
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
const emits = defineEmits(['change', 'update:value'])
const props = defineProps(['value', 'data', 'multiple', 'label'])
const targetItems = ref([] as any)
const itemClick = (item: any) => {
  const index = targetItems.value.findIndex((e) => e === item.value)
  if (index != -1) {
    targetItems.value.splice(index, 1)
  } else {
    if (!props.multiple) targetItems.value = []
    targetItems.value.push(item.value)
  }
  emits('update:value', targetItems.value)
  emits('change', targetItems.value)
}
watch(
  () => props.value,
  () => {
    targetItems.value = props.value ? props.value : []
  },
)
</script>

<style lang="scss" scoped>
.tagFilerBox {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  font-size: 14px;
  color: #000;

  .label {
    height: 32px;
    line-height: 32px;
    white-space: nowrap;
  }
}
</style>

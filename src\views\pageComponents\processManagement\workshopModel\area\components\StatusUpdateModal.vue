<template>
  <a-modal :open="open" title="区域状态" width="420px" @cancel="handleCancel">
    <span>{{ content }}</span>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确认</a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { updateAreaStatus } from '@/servers/workshopArea'

const props = defineProps<{
  open: boolean
  initValue: { workshop_area_id: number; status: number; name: string }
}>()

const emits = defineEmits(['update:open', 'update'])

const content = `是否确认${props.initValue.status ? '启用' : '停用'}该区域？`

const handleCancel = () => {
  emits('update:open', false)
}

const handleSubmit = async () => {
  const params = {
    id: props.initValue.workshop_area_id,
    status: props.initValue.status,
  }
  const res = await updateAreaStatus(props.initValue.workshop_area_id, params)
  message.success(res.message)
  emits('update:open', false)
  emits('update')
}
</script>

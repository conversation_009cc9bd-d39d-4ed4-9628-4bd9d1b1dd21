<template>
  <div>
    <div class="breadcrumbBox">
      <div class="title">全局水印</div>
    </div>
    <div class="cardBox">
      <div class="card">
        <div class="title">操作界面水印</div>
        <a-button @click="OperationInterfaceWatermarkRef.open()" :disabled="!btnPermission[113000100]">设置</a-button>
        <a-button @click="log(1)" :disabled="!btnPermission[113000200]">日志</a-button>
        <div class="description">配置后将生效于所有操作界面。</div>
      </div>
    </div>
    <div class="breadcrumbBox">
      <div class="title">操作日志</div>
    </div>
    <div class="cardBox">
      <div class="card">
        <div class="title">用户操作日志</div>
        <a-button @click="userLog" :disabled="!btnPermission[113000300]">日志</a-button>
      </div>
    </div>

    <OperationInterfaceWatermark :rolesoptions="rolesOptions" ref="OperationInterfaceWatermarkRef" />
    <log-drawer ref="logDrawerRef" />
    <user-log-drawer ref="userLogDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import { GetRoleSelectOption } from '@/servers/Role'
import LogDrawer from './components/LogDrawer.vue'
import UserLogDrawer from './components/UserLogDrawer.vue'
import OperationInterfaceWatermark from './components/OperationInterfaceWatermark.vue'

const logDrawerRef = ref<any>(null)
const userLogDrawerRef = ref<any>(null)
const rolesOptions = ref([])
const OperationInterfaceWatermarkRef = ref<any>(null)

const { btnPermission } = usePermission()

const log = (page) => {
  logDrawerRef.value.open(page)
}

const userLog = () => {
  userLogDrawerRef.value.open()
}
onMounted(() => {
  GetRoleSelectOption({ page: 1, pageSize: 100 }).then((res) => {
    console.log('获取角色列表：', res)
    res.data.forEach((x) => {
      x.label = x.role_name
      x.value = x.id
    })
    rolesOptions.value = res.data
  })
})
</script>

<style lang="scss" scoped>
.breadcrumbBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  margin-bottom: 8px;

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }
}

.cardBox {
  border: 1px solid #dcdcdc;
  border-radius: 4px;

  .card {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 12px 24px;
    font-size: 12px;
    color: #000;

    .title {
      margin-right: 40px;
    }

    .description {
      margin-left: 10px;
      color: rgb(0 0 0 / 50%);
    }
  }
}
</style>

<template>
  <a-modal width="40vw" :open="visible" centered title="表格设置" @cancel="handleCancel">
    <a-input v-model:value="scrollFilterText" placeholder="输入查找项" @change="changeFilterText" class="tableSetupSearchinput"></a-input>
    <vxe-table ref="tableSettingRef" :border="true" :row-config="{ isHover: true, height: px2(50) }" :custom-config="{ mode: 'popup' }" :data="tabularArr" :show-overflow="true" height="530px">
      <vxe-column width="60">
        <template #header>
          <div style="display: flex; justify-content: center">排序</div>
        </template>
        <template #default>
          <div class="dragBtn">
            <HolderOutlined />
          </div>
        </template>
      </vxe-column>
      <vxe-column :field="column.key" :title="column.title" :width="column.width" v-for="column in setupColumns" :key="column.key">
        <template v-if="column.key == 'show'" #header>
          <div style="display: flex; gap: 8px; align-items: center">
            <a-checkbox v-model:checked="isAllShowChecked" @click="handleAllShowChecked"></a-checkbox>
            <span>显示</span>
          </div>
        </template>
        <template v-if="column.key == 'show'" #default="{ row }">
          <div style="display: flex; justify-content: center">
            <a-checkbox v-model:checked="row.is_show" @click="showChange"></a-checkbox>
          </div>
        </template>
        <template v-if="column.key == 'width'" #default="{ row }">
          <a-input-number @blur="row.width == null ? (row.width = 0) : null" :min="0" v-model:value="row.width" style="width: 100%"></a-input-number>
        </template>
        <template v-if="column.key == 'name'" #default="{ row, $rowIndex }">
          <div :id="`item${$rowIndex}`">{{ row.name }}</div>
        </template>
        <template v-if="column.key == 'freeze'" #default="{ row }">
          <a-radio-group class="item-radio" v-model:value="row.freeze" button-style="solid">
            <a-radio-button :value="1">左侧</a-radio-button>
            <a-radio-button :value="0">不设置</a-radio-button>
            <a-radio-button :value="2">右侧</a-radio-button>
          </a-radio-group>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <a-button @click="handleReset">恢复默认</a-button>
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="hadnleConfirm" type="primary">确定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { pageTableConfig } from '@/common/pageTableConfig'
import { px2, px22, setTableConfig } from '@/utils/index'
import { HolderOutlined } from '@ant-design/icons-vue'
import Sortable from 'sortablejs'
import { type VxeTableInstance } from 'vxe-table'

const emits = defineEmits(['update:visible', 'update:columns'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  columns: {
    type: Array as PropType<any[]>,
    required: true,
  },
  tableRef: {
    type: Object as PropType<VxeTableInstance>,
  },
  pageType: {
    type: Number,
    required: true,
  },
})

const setupColumns = [
  {
    title: '显示',
    dataIndex: 'show',
    key: 'show',
    width: '12%',
  },
  {
    title: '表头',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '列宽（像素）',
    dataIndex: 'width',
    key: 'width',
    width: '20%',
  },
  {
    title: '冻结列',
    dataIndex: 'freeze',
    key: 'freeze',
    width: '32%',
  },
]

const tableSettingRef = ref()
const scrollFilterText = ref('') // 滚动条过滤文本

interface ColumnConfig {
  key: string // 列键
  title: string // 列标题
  index?: number // 索引
  freeze?: 0 | 1 | 2 // 冻结状态
  is_show?: boolean // 是否显示
  name: string // 列名
  width?: number | null // 列宽
}

const tabularArr = ref([] as ColumnConfig[]) // 组件表格数据

// 过滤文本变化
const changeFilterText = () => {
  const filterText = scrollFilterText.value.trim()
  if (!filterText.length) return
  const index = tabularArr.value.findIndex((i) => i.name && i.name.indexOf(scrollFilterText.value) != -1)
  if (index !== -1) {
    tapGo(index)
  }
}
// 滚动
const tapGo = (id) => {
  if (+id < 100) {
    document.getElementById(`item${id}`)?.scrollIntoView({ behavior: 'smooth' })
  }
}

// 拖拽事件
const onDrop = () => {
  new Sortable(tableSettingRef.value?.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
    animation: 300,
    handle: '.dragBtn',
    delay: 10,
    group: 'shared',
    onEnd: (item) => {
      const { oldIndex, newIndex } = item
      const currRow = tabularArr.value.splice(oldIndex, 1)[0]
      tabularArr.value.splice(newIndex, 0, currRow)
    },
  })
}

// 显示全选
const isAllShowChecked = ref(false) // 是否全选
const handleAllShowChecked = () => {
  tabularArr.value.forEach((item) => {
    item.is_show = !isAllShowChecked.value
  })
}
const showChange = () => {
  // let statsu = true
  // setTimeout(() => {
  //   tabularArr.value.forEach((item) => {
  //     if (item.is_show == false) {
  //       statsu = false
  //     }
  //   })
  //   isAllShowChecked.value = statsu
  // }, 100)

  setTimeout(() => {
    isAllShowChecked.value = tabularArr.value.every((item) => item.is_show === true)
  }, 100)
}

// 初始化
const init = () => {
  tabularArr.value = []
  const arr = [] as ColumnConfig[]
  const $table = props.tableRef
  const columns: any = $table?.getTableColumn()
  columns.fullColumn.forEach((item) => {
    if (item.field && !['checkbox', 'seq', 'expand'].includes(item.field)) {
      const defaultValue = props.columns.find((i) => i.key == item.field)
      const obj = {
        ...defaultValue,
        index: item.renderSortNumber,
        key: item.field,
        freeze: !item.fixed ? 0 : item.fixed == 'left' ? 1 : 2,
        is_show: item.visible,
        name: item.title,
        width: item.renderWidth ? px22(item.renderWidth) : defaultValue.width,
      }

      arr.push(obj)
    }
  })
  isAllShowChecked.value = true
  arr.forEach((item) => {
    if (item.is_show == false) {
      isAllShowChecked.value = false
    }
  })
  setTimeout(() => {
    tabularArr.value = [...arr]
    setTimeout(() => {
      onDrop()
      showChange()
    }, 0)
  }, 0)
}

// 确认
const hadnleConfirm = () => {
  handleCancel()
  const arr = [] as ColumnConfig[]
  tabularArr.value.forEach((e, index) => {
    if (e.key) {
      arr.push({ ...e, index: index + 1 })
    }
  })
  emits('update:columns', [])
  nextTick(() => {
    emits('update:columns', [...arr])
    nextTick(() => {
      props.tableRef?.getTableColumn().fullColumn.forEach((x: any) => {
        arr.forEach((y) => {
          if (x.field === y.key) {
            x.width = y.width ? px2(y.width) : null
          }
        })
      })
      props.tableRef?.refreshColumn()
      nextTick(() => {
        setTableConfig(arr, props.pageType)
      })
    })
  })
}
// 取消
const handleCancel = () => {
  emits('update:visible', false)
}
// 重置
const handleReset = () => {
  const res = pageTableConfig[props.pageType!]
  handleCancel()
  emits('update:columns', [])

  nextTick(() => {
    const arr = JSON.parse(JSON.stringify(res))
    arr.forEach((x) => {
      x.width = x.width ? px2(x.width) : 0
    })
    emits('update:columns', arr)
    setTableConfig(
      res.map((x) => ({ ...x, width: x.width ? x.width : 0 })),
      props.pageType,
    )
  })
}

watch(
  () => props.visible,
  (val) => {
    if (val) {
      init()
    }
  },
)

defineExpose({
  handleReset,
})
</script>

<style scoped lang="scss">
.tableSetupSearchinput {
  margin-bottom: 12px;
}

.dragBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  font-size: 18px;
  cursor: move;
}
</style>

<template>
  <vxe-table
    :data="tableData"
    border
    ref="tableRef"
    max-height="320px"
    min-height="0"
    class="principal-table table"
    size="mini"
    show-overflow
    :checkbox-config="{ checkMethod: isRowEditable }"
    @cell-click="handleClickRow"
  >
    <vxe-column field="code" title="设备编码" width="200">
      <template #default="{ row }">
        <span v-if="isViewMode">{{ row.code }}</span>
        <a-input v-else v-model:value="row.code" placeholder="请输入设备编码" />
      </template>
    </vxe-column>
    <vxe-column field="name" title="设备名称" width="200">
      <template #default="{ row }">
        <span v-if="isViewMode">{{ row.name }}</span>
        <a-input v-else v-model:value="row.name" placeholder="请输入设备名称" />
      </template>
    </vxe-column>
    <vxe-column v-if="!isViewMode" title="操作" width="140px">
      <template #default="{ rowIndex }">
        <a-button type="primary" size="small" class="mr-4" @click="handleAddRow(rowIndex)">添加</a-button>
        <a-button v-if="tableData?.length > 1" type="primary" danger size="small" @click="handleRemoveRow(rowIndex)">删除</a-button>
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script lang="ts" setup>
import { cloneDeep } from '@/utils'

const props = defineProps<{
  type: string // 模式：view-查看，edit-编辑
  equipmentList?: any
}>()

const isRowEditable = ({ row }) => row.editable // 表格是否可编辑
const isViewMode = computed(() => props.type === 'view') // 是否仅为查看模式

const tableRef = ref()
const initTableData = {
  code: '', // 设备编码
  name: '', // 设备名称
}
const tableData = ref([cloneDeep(initTableData)])

// 获取当前编辑行
const currentEditRow = ref()
const handleClickRow = ({ row, rowIndex, column }) => {
  currentEditRow.value = { row, rowIndex, field: column.field }
}

/** 表格添加下一行 */
const handleAddRow = (rowIndex: number) => {
  tableData.value.splice(rowIndex + 1, 0, cloneDeep(initTableData))
}
/** 表格删除该行 */
const handleRemoveRow = (rowIndex: number) => {
  tableData.value.splice(rowIndex, 1)
}

watch(
  props,
  (newProps) => {
    // 查看模式直接拿值，新建/编辑模式下需要获取原先数据，若没有则自动赋初始值
    if (isViewMode.value) {
      tableData.value = newProps.equipmentList
    } else {
      tableData.value = newProps.equipmentList?.length ? [...newProps.equipmentList] : [cloneDeep(initTableData)]
    }
  },
  { immediate: true },
)

defineExpose({
  tableData,
})
</script>

<style lang="scss" scoped>
.principal-table {
  :deep(.ant-select) {
    width: 110px;
  }

  .search-input-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 116px;
    height: 28px;
    padding: 3px 11px;
    font-size: 12px;
    border: 1px solid #d3d3d3;
    border-radius: 4px;
  }
}
</style>

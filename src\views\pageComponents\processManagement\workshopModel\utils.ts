import { ModuleActionType } from './types'

/**
 * 获取车间管理页面权限id
 * @param code 页面编码
 * @returns 页面权限id
 */
export const getPagePermissionId = (code: string) => {
  const permissionInfo = JSON.parse(localStorage.getItem('userData') || '')?.permissions_infos
  const pageChildren = permissionInfo?.find((item) => item.path === '/productSetting')?.children || []
  const pageId = pageChildren?.find((item) => item.code === code)?.id
  return pageId
}

/** 所有树节点id */
export const treeAllKeys: string[] = []
/**
 * 为树结构数据添加唯一键uniqueKey，并且收集所有树节点id
 * @param treeData 树结构数据
 * @param parentKey 父节点key值
 * @returns 处理后的树结构数据
 */
export const addTreeKeys = (treeData, parentKey = '') => {
  return treeData.map((node) => {
    // 当前节点的key（如果是根节点直接用id，否则拼接父级key）
    const currentKey = parentKey ? `${parentKey}-${node.id}` : String(node.id)
    treeAllKeys.push(currentKey)

    // 创建新节点（保留原属性）
    const newNode = {
      ...node,
      uniqueKey: currentKey, // 添加唯一key
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      newNode.children = addTreeKeys(node.children, currentKey)
    }

    return newNode
  })
}
export interface ActionPayload {
  nodeId?: string
  extra?: Record<string, any>
}
export interface ActionHandler {
  component: any // 动态加载的组件
  execute?: () => void // 前置逻辑
  // execute?: (payload: ActionPayload) => void // 前置逻辑
}

const actionMap = new Map<ModuleActionType, ActionHandler>()
export const registerWorkshopActions = () => {
  // 新建车间
  actionMap.set('add-workshop', {
    component: defineAsyncComponent(() => import('./workshop/components/WorkshopUpdateDrawer.vue')),
  })
  actionMap.set('edit-workshop', {
    component: defineAsyncComponent(() => import('./workshop/components/WorkshopUpdateDrawer.vue')),
  })
  actionMap.set('view-workshop', {
    component: defineAsyncComponent(() => import('./workshop/components/WorkshopViewDrawer.vue')),
  })
  actionMap.set('delete-workshop', {
    component: defineAsyncComponent(() => import('./workshop/components/WorkshopDeleteModal.vue')),
  })
  actionMap.set('add-area', {
    component: defineAsyncComponent(() => import('./area/components/AreaUpdateDrawer.vue')),
  })
  actionMap.set('edit-area', {
    component: defineAsyncComponent(() => import('./area/components/AreaUpdateDrawer.vue')),
  })
  actionMap.set('view-area', {
    component: defineAsyncComponent(() => import('./area/components/AreaViewDrawer.vue')),
  })
  actionMap.set('delete-area', {
    component: defineAsyncComponent(() => import('./area/components/AreaDeleteModal.vue')),
  })
  actionMap.set('add-workcenter', {
    component: defineAsyncComponent(() => import('./center/components/CenterUpdateDrawer.vue')),
  })
  console.log('actionMap:', actionMap)
}

// 根据不同操作调用页面
export const getActionHandler = (key: ModuleActionType): ActionHandler | undefined => {
  return actionMap.get(key)
}

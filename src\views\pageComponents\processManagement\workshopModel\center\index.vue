<template>
  <div class="flex flex-1 !flex-row px-20 py-12">
    <tree-selector ref="treeRef" type="workcenter" @update="refresh" />
    <div class="flex flex-1 flex-col">
      <div class="header-container">
        <div class="header-item">车间</div>
        <div class="header-item">区域</div>
        <div class="header-item active">工作中心</div>
      </div>
      <Form v-model:form="formArr" :page-type="PageType.WORKSHOP_CENTER" :showSetting="false" @search="search" @setting="tableRef?.showTableSetting()" />
      <!-- 表格 -->
      <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.WORKSHOP_CENTER" :formFormat="formFormat" :get-list="getWorkshopCenters">
        <template #right-btn>
          <a-button type="primary" :icon="h(PlusOutlined)" v-if="btnPermission[PERM_CODE.CREATE] && canCreateItem" @click="handleUpdateCenter('add')">新建工作中心</a-button>
        </template>
        <template #stage_name="{ row }">{{ row.stage?.key || '--' }}</template>
        <template #status="{ row, rowIndex }">
          <a-switch
            v-if="btnPermission[PERM_CODE.EDIT]"
            v-model:checked="row.status"
            :checkedValue="1"
            :unCheckedValue="0"
            checked-children="启用"
            un-checked-children="停用"
            @click="handleSwitch(row, rowIndex)"
          />
          <span v-else>{{ row.status ? '启用' : '停用' }}</span>
        </template>
        <template #creator_name="{ row }">
          <span>{{ row.creator?.real_name || '--' }}</span>
        </template>
        <template #created_at="{ row }">
          <span>{{ row.creator?.time || '--' }}</span>
        </template>
        <template #operate="{ row }">
          <div class="btnBox">
            <a-button v-if="btnPermission[PERM_CODE.VIEW]" @click="handleViewCenter(row.id)" class="btn">查看</a-button>
            <a-dropdown>
              <a-button>更多</a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="btnPermission[PERM_CODE.EDIT]" @click="handleUpdateCenter('edit', row)">编辑</a-menu-item>
                  <a-menu-item v-if="btnPermission[PERM_CODE.DELETE]" @click="handleDeleteCenter(row.id)">
                    <span class="text-red-500">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </BaseTable>
    </div>
    <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
  </div>
</template>

<script lang="ts" setup>
import { h, shallowRef, watch, onMounted, provide } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getWorkshopCenters, getSelectOptions } from '@/servers/workshopCenter'
import { getWorkshopNames } from '@/servers/workshop'
import { PageType } from '@/common/enum'
import { PERM_CODE } from './type'
import TreeSelector from '../TreeSelector.vue'
import CenterUpdateDrawer from './components/CenterUpdateDrawer.vue'
import CenterViewDrawer from './components/CenterViewDrawer.vue'
import CenterDeleteModal from './components/CenterDeleteModal.vue'
import StatusUpdateModal from './components/StatusUpdateModal.vue'

const { btnPermission } = usePermission()

const treeRef = ref()
const company_id = computed(() => treeRef.value?.company_id || localStorage.getItem('tree_company'))
provide('company_id', company_id)
const canCreateItem = computed(() => treeRef.value?.canCreateItem)

// 车间
const centerOptions = ref() // 工作中心下拉选项
const selectedNode = computed(() => treeRef.value?.selectedNode)
const workshop_id = ref() // 车间id
const workshop_area_id = ref() // 车间区域id

// 查询表单
const formArr = ref<any[]>([
  {
    label: '请选择工作中心',
    value: null,
    type: 'select',
    selectArr: centerOptions.value,
    key: 'workcenter_name',
    showSearch: true,
  },
  {
    label: '请选择生产阶别',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'stage_item_id',
  },
])
const tableRef = ref()
const search = () => tableRef.value.search()
const formFormat = (data) => ({
  ...data,
  company_id: company_id.value,
  workshop_id: workshop_id.value,
  workshop_area_id: workshop_area_id.value,
})

// component
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  workcenter_id: null,
  company_id: '',
  workshop_id: '',
  workshop_area_id: '',
})

const refresh = (type: string = 'index') => {
  tableRef.value.refresh()
  getCenterOptions() // 更新工作中心下拉选项
  if (type === 'index') {
    treeRef.value.getTreeData() // 更新树结构数据
  }
}

/** 创建/编辑工作中心 */
const handleUpdateCenter = (type: string, row?: any) => {
  component.value = CenterUpdateDrawer
  initValue.type = type
  initValue.company_id = company_id.value
  initValue.workshop_id = row?.workshop_id || workshop_id.value
  initValue.workshop_area_id = row?.workshop_area_id || workshop_area_id.value
  initValue.workcenter_id = row?.id
  open.value = true
}
/** 查看工作中心详情 */
const handleViewCenter = (workcenter_id: number) => {
  component.value = CenterViewDrawer
  initValue.workcenter_id = workcenter_id
  open.value = true
}
/** 删除工作中心 */
const handleDeleteCenter = (workcenter_id: number) => {
  component.value = CenterDeleteModal
  initValue.workcenter_id = workcenter_id
  open.value = true
}
/** 切换工作中心启用状态 */
const handleSwitch = (row, rowIndex: number) => {
  component.value = StatusUpdateModal
  initValue.workcenter_id = row.id
  initValue.name = row.name
  initValue.status = row.status // 此时status状态已改变
  tableRef.value.tableData[rowIndex].status = row.status === 1 ? 0 : 1 // 确保切换成功之前status状态不变
  open.value = true
}

const productStepOptions = ref()
provide('productStepOptions', productStepOptions)
/** 获取生产阶别下拉列表 */
const getProductStepOptions = async () => {
  const res = await getSelectOptions()
  productStepOptions.value = res.data?.productionLevel?.map((item) => ({
    value: item.id,
    label: item.key,
  }))
  const target = formArr.value?.find((item) => item.key === 'stage_item_id')
  target.selectArr = productStepOptions.value
}

/** 获取表单查询选项 */
const getCenterOptions = async () => {
  const params = {
    type: 'workcenter',
    company_id: company_id.value,
    workshop_id: workshop_id.value,
    workshop_area_id: workshop_area_id.value,
  }
  const res = await getWorkshopNames(params)
  centerOptions.value = res.data?.map((item) => ({ value: item.name, label: item.name })) || []
}

watch(
  centerOptions,
  (newOptions) => {
    formArr.value[0].selectArr = newOptions
  },
  { immediate: true },
)

watch(
  selectedNode,
  (newSelectedNode) => {
    switch (newSelectedNode?.workshop_type) {
      case 1:
        workshop_id.value = newSelectedNode.id
        workshop_area_id.value = undefined
        break
      case 2:
        workshop_id.value = newSelectedNode.workshop_id
        workshop_area_id.value = newSelectedNode.id
        break
      case 3:
        workshop_id.value = newSelectedNode.workshop_id
        workshop_area_id.value = newSelectedNode.workshop_area_id
        break
      default:
        workshop_id.value = undefined
        workshop_area_id.value = undefined
        break
    }
  },
  { immediate: true },
)

watch(
  [workshop_id, workshop_area_id],
  () => {
    tableRef.value?.search()
    getCenterOptions()
  },
  { immediate: true },
)

onMounted(() => {
  getProductStepOptions()
})
</script>

<style lang="scss" scoped>
.header-container {
  display: inline-flex;
  margin-bottom: 8px;

  .header-item {
    align-content: center;
    width: 60px;
    height: 28px;
    color: #000;
    text-align: center;
    border: 1px solid #d3d3d3;
    border-right: 0;
  }

  .header-item:last-child {
    width: 70px;
    border: 1px solid #d3d3d3;
  }

  .header-item.active {
    color: #fff;
    background: #409eff;
    border-color: #409eff;
  }
}
</style>

<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.CENTER_ENABLE" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.CENTER_ENABLE" :get-list="CenterList" :isIndex="true" :isCheckbox="true">
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button @click="closeBtn(row)" class="btn">下线</a-button>
        </div>
      </template>
    </BaseTable>

    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title" :width="900">
      <div class="modalContent">
        <p>
          <span class="text-red">当前派工单状态</span>
          ：{{ remarkForm.order_status_name }}
        </p>
        <p>计划生产数量：{{ remarkForm.plan_nums }}</p>
        <p>实际产出数量：{{ remarkForm.actual_nums }}</p>
        <p>操作影响说明：</p>
        <p>
          1. 若【实际产出 ≥ 计划数量】→ 下线后派工单状态自动变为「
          <span class="text-red">完成</span>
          」，流程正常收尾；
        </p>
        <p>
          2. 若【实际产出 ＜ 计划数量】→ 需手动选择「
          <span class="text-red">强制关结</span>
          」（派工单状态变「
          <span class="text-red">强制关结</span>
          」）或「下线」（保留「
          <span class="text-red">投产</span>
          」状态）
        </p>
        <p>是否执行下线操作？</p>
      </div>
      <a-form ref="remarkFormRef" :model="remarkForm" :rules="remarkRules">
        <a-form-item label="备注" name="closure_remark" :label-col="{ style: { flex: '0 0 50px' } }">
          <a-textarea v-model:value="remarkForm.closure_remark" placeholder="请输入强制关结派工单的原因" :rows="4" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" @click="visibleData.isShow = false">取消</a-button>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="closureRole">强制关结</a-button>
        <a-button type="primary" @click="offlineRole">下线</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { onMounted } from 'vue'

import { CenterList, GetOptions, Closure, GoOffline } from '@/servers/dispatchOrder'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

// const { btnPermission } = usePermission()
const remarkRules: Record<string, Rule[]> = {
  closure_remark: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 200),
      message: '输入内容不可超过200字符',
    },
  ],
}
const visibleData = reactive({
  isShow: false,
  title: '',
  okType: 'primary',
})
const formArr: any = ref([
  {
    label: '搜索派工单号',
    value: '',
    type: 'input',
    key: 'dispatch_order_number',
  },
  {
    label: '请输入物料编码',
    value: '',
    type: 'input',
    key: 'goods_code',
  },
  {
    label: '工作中心',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'work_center_id',
    multiple: true,
  },
  {
    label: '上线时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['开始时间', '结束时间'],
  },
])
const centerOptions = ref([])
const remarkForm = reactive({
  id: null,
  closure_remark: '',
  order_status_name: '',
  plan_nums: 0,
  actual_nums: 0,
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.CENTER_ENABLE) {
    const arr: any[] = []
    obj.CENTER_ENABLE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(async () => {
  await getOptions()
  console.log(formArr.value, 'form')
  search()
  initScreening()
})

const getOptions = () => {
  GetOptions({}).then((res) => {
    centerOptions.value = res.data.work_center || []
    formArr.value.forEach((item) => {
      if (item.key === 'work_center_id') {
        item.selectArr = centerOptions.value
      }
    })
  })
}

const closeBtn = (row) => {
  visibleData.isShow = true
  remarkForm.id = row.id
  remarkForm.order_status_name = row.order_status_name
  remarkForm.plan_nums = row.plan_nums
  remarkForm.actual_nums = row.actual_nums
  remarkForm.closure_remark = ''
}

const tableRef = ref()
const remarkFormRef = ref()
const search = () => tableRef.value.search()

// 强制关结工单
const closureRole = async () => {
  try {
    await remarkFormRef.value.validateFields()
    Closure({ id: remarkForm.id, closure_remark: remarkForm.closure_remark })
      .then((res) => {
        if (res.success) {
          visibleData.isShow = false
          message.success('强制关结工单成功')
          search()
        } else {
          message.error(res.message)
        }
      })
      .catch(() => {
        visibleData.isShow = false
      })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const offlineRole = () => {
  GoOffline({ id: remarkForm.id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('下线成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 0;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

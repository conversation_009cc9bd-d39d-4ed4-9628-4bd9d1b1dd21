<template>
  <a-drawer title="查看生产实体人员" width="650px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" v-bind="formItemLayout">
      <a-form-item label="生产实体" name="workshop_name">
        {{ formState.workshop_name || '--' }}
      </a-form-item>
      <a-form-item label="生产实体类型" name="workshop_type">
        {{ getWorkshopType(formState.workshop_type) || '--' }}
      </a-form-item>
      <a-form-item label="工作人员" name="user_name">
        <div class="w-full">
          <vxe-table :data="userList" border stripe ref="tableRef" max-height="400px" min-height="0" class="table w-full" size="mini" show-overflow>
            <vxe-column field="real_name" width="100" title="真实姓名" />
            <vxe-column field="department_name" width="180" title="部门" />
            <vxe-column field="position_name" width="130" title="岗位" />
          </vxe-table>
          <a-pagination
            class="mt-2"
            show-quick-jumper
            showSizeChanger
            :total="tableParams.total"
            v-model:current="tableParams.page"
            v-model:page-size="tableParams.pageSize"
            :page-size-options="pageSizeOptions"
            @change="handleChangePage"
            size="small"
          />
        </div>
      </a-form-item>
    </a-form>
    <div class="drawer-title mt-32">其他信息</div>
    <a-form :model="formState" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
      <a-form-item label="创建时间" name="created_at">
        <span>{{ formState.created_at || '--' }}</span>
      </a-form-item>
      <a-form-item label="创建人" name="created_name">
        {{ formState.created_name || '--' }}
        <span class="user-info">{{ formState.created_userInfo }}</span>
      </a-form-item>
      <a-form-item label="最后修改时间" name="updated_at">
        {{ formState.updated_at || '--' }}
      </a-form-item>
      <a-form-item label="最后修改人" name="updated_name">
        {{ formState.updated_name || '--' }}
        <span class="user-info">{{ formState.updated_userInfo }}</span>
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { viewWorkshopPerson } from '@/servers/workshopPerson'
import { getWorkshopType } from '../types'

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    id: string
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
}

const formState = reactive({
  workshop_name: '',
  workshop_type: null,
  user_name: '',
  personnel: [],
  userList: [],
  created_at: '',
  created_name: '',
  created_userInfo: '',
  updated_at: '',
  updated_name: '',
  updated_userInfo: '',
})

const userList = ref()
const pageSizeOptions = ref(['10', '20', '30', '40', '50'])
const tableParams = reactive({
  pageSize: 20,
  page: 1,
  total: 0,
})
// 假分页，此时只是用户暂时选中工作人员，不直接调接口
const handleChangePage = () => {
  userList.value = formState.userList?.slice((tableParams.page - 1) * tableParams.pageSize, tableParams.page * tableParams.pageSize)
}

// 关闭drawer
const onClose = () => {
  emits('update:open', false)
}

// 获取生产实体详情
const getWorkshopDetail = async () => {
  const { data } = await viewWorkshopPerson({ id: props.initValue.id })
  Object.assign(formState, {
    ...data,
    userList: data.personnel?.map((item) => item.user),
    created_userInfo: `（ ${data.created_position_name} | ${data.created_department_name} ）`,
    updated_userInfo: `（ ${data.updated_position_name} | ${data.updated_department_name} ）`,
  })
  tableParams.total = formState.userList?.length || 0
  handleChangePage()
}

onMounted(() => {
  getWorkshopDetail()
})
</script>

<style lang="scss" scoped>
.user-info {
  color: #8a8a8a;
}

:deep(.ant-pagination-options .ant-select.ant-select-in-form-item) {
  width: auto !important;
}
</style>

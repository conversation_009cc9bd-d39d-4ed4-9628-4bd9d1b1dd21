import { request } from './request'
// 获取列表
export const GetSetList = (data) => request({ url: '/api/System/DataDictionaryItem/List', data })
// 新建
export const Add = (data) => request({ url: '/api/System/DataDictionaryItem/Store', data })
// 查看
export const Details = (data) => request({ url: '/api/System/DataDictionaryItem/Detail', data })
// 编辑
export const Update = (data) => request({ url: '/api/System/DataDictionaryItem/Update', data })
// 删除
export const Delete = (data) => request({ url: '/api/System/DataDictionaryItem/Destroy', data })
// 修改字典状态
export const ChangeStatus = (data) => request({ url: '/api/System/DataDictionaryItem/ChangeStatus', data })

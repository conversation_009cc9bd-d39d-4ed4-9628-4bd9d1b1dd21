<template>
  <a-drawer
    :footer="false"
    v-model:open="detailVisible"
    :width="'45vw'"
    title="查看区域人员"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" header="" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">序号</p>
                <p class="value">{{ dataIndex }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">车间</p>
                <p class="value">{{ target.workshop }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">区域</p>
                <p class="value">{{ target.area }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">生产阶别</p>
                <p class="value">{{ arr1.find((v) => v.value == target.type1)?.label }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">工作人员</p>
                <p class="value">{{ target.person }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">岗位</p>
                <p class="value">{{ target.post }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">备注</p>
                <p class="value">{{ target.remark }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="其他信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">创建时间</p>
                <p class="value">{{ target.create_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.create_user?.real_name }}</div>
                      <div v-show="target?.create_user?.scope == 1">所属公司：{{ target?.create_user?.sub_company_name ? target?.create_user?.sub_company_name : '--' }}</div>
                      <div v-show="target?.create_user?.scope != 1">所属客户：{{ target?.create_user?.update_user_customer_name ? target?.create_user?.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target?.create_user?.department ? target?.create_user?.department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.create_user?.job_name ? target?.create_user?.job_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.create_user?.real_name ? target?.create_user?.real_name : '--' }}</span>
                      <span v-if="target?.create_user?.department || target?.create_user?.job_name" class="detailValueDescription">
                        （
                        <span v-if="target?.create_user?.job_name">{{ target?.create_user?.job_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.create_user?.department">
                          {{ target?.create_user?.department.length > 10 ? target?.create_user?.department.slice(0, 10) + '...' : target?.create_user?.department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target.update_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.modified_user?.real_name }}</div>
                      <div v-show="target?.modified_user?.scope == 1">所属公司：{{ target?.modified_user?.sub_company_name ? target?.modified_user?.sub_company_name : '--' }}</div>
                      <div v-show="target?.modified_user?.scope != 1">所属客户：{{ target?.modified_user?.update_user_customer_name ? target?.modified_user?.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target?.modified_user?.department ? target?.modified_user?.department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.modified_user?.job_name ? target?.modified_user?.job_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.modified_user?.real_name ? target?.modified_user?.real_name : '--' }}</span>
                      <span v-if="target?.modified_user?.department || target?.modified_user?.job_name" class="detailValueDescription">
                        （
                        <span v-if="target?.modified_user?.job_name">{{ target?.modified_user?.job_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.modified_user?.department">
                          {{ target?.modified_user?.department.length > 10 ? target?.modified_user?.department.slice(0, 10) + '...' : target?.modified_user?.department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { Details } from '@/servers/processParamtters'
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'

const dataIndex = ref(0)
const arr1 = ref([
  { label: '半成品', value: 1 },
  { label: '灌装', value: 2 },
  { label: '包装', value: 3 },
])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'
const activeKey = ref(['1', '2'])
const detailVisible = ref(false)
const detailloading = ref(false)

const target = ref<any>(null)
const open = (id, idx) => {
  dataIndex.value = idx
  target.value = null
  detailloading.value = true
  detailVisible.value = true

  Details({ id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
    })
    .catch(() => {
      detailloading.value = false
    })
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}
</style>

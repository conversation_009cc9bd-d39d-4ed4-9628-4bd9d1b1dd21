<template>
  <a-drawer :footer="false" v-model:open="detailVisible" :width="'35vw'" title="查看公司" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }" :bodyStyle="{ padding: '0' }">
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" header="基本信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="12">
                <p class="label">公司编号</p>
                <p class="value">{{ target.number }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">来源</p>
                <p class="value">{{ target.source || 'UMC' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">公司名称</p>
                <p class="value">{{ target.name }}</p>
              </a-col>

              <a-col class="gutter-row" :span="12">
                <p class="label">所属企业</p>
                <p class="value">{{ target.company?.name || '/' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">上级公司</p>
                <p class="value">{{ target.parent?.name || '/' }}</p>
              </a-col>

              <a-col class="gutter-row" :span="12">
                <p class="label">状态</p>
                <p class="value">{{ target.status == 1 ? '启用' : '停用' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">同步时间</p>
                <p class="value">{{ target.sync_time }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { Show } from '@/servers/0rganization'
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'

const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'
const activeKey = ref(['1'])
const detailVisible = ref(false)
const detailloading = ref(false)

const target = ref<any>(null)
const open = (id) => {
  target.value = null
  detailloading.value = true
  detailVisible.value = true

  Show({ id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
    })
    .catch(() => {
      detailloading.value = false
    })
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

.value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

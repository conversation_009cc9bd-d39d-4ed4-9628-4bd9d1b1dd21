<template>
  <div class="main flex h-full">
    <div class="w-230px mr-16px flex h-full flex-col">
      <a-tree-select
        v-model:value="memberSelectCompany"
        :tree-data="deptAndCompanyTree"
        :tree-line="true && { showLeafIcon: false }"
        :fieldNames="{ label: 'name', value: 'id', children: 'children' }"
        class="w-full"
        @change="handleCompanyChange"
        :disabled="false"
      ></a-tree-select>
      <div class="department-left">
        <div class="bg-#FAFAFB p-12px box-border">
          <a-select
            class="w-full"
            v-model:value="filterValue"
            placeholder="请输入"
            :options="headerOption"
            show-search
            :filter-option="filterHeaderOption"
            allowClear
            :field-names="{ label: 'account_name', value: 'id' }"
            @change="handleSelectTreeFilter"
          >
            <template #option="{ name, company_name }">
              <div class="select-option" :title="name">
                <div class="option-name truncate">{{ name }}</div>
                <div class="option-info text-#999 truncate">
                  {{ company_name.replaceAll('/', '>') }}
                </div>
              </div>
            </template>
          </a-select>
        </div>
        <div class="p-12px arch-left-list relative box-border flex-1 overflow-y-auto overflow-x-hidden">
          <a-spin tip="加载中..." v-if="loading" class="w-full"></a-spin>
          <a-tree
            class="absolute w-full"
            node-key="id"
            :fieldNames="treeProps"
            defaultExpandAll
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
            :tree-data="deptTree"
            @select="handleSelectDept"
          >
            <template #title="data">
              <div class="flex w-full items-center justify-between">
                <a-tooltip :title="data.name" v-if="isEllipsis(data.name)" placement="topLeft">
                  <span class="truncate" :style="{ width: `${120 - (data.level + 1) * 8}px` }">{{ data.name }}</span>
                </a-tooltip>
                <span v-else class="w-120px truncate" :style="{ width: `${120 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
              </div>
            </template>
          </a-tree>
        </div>
        <div class="tree-bottom flex">
          <a-spin v-if="expandLoading" class="w-full p-5px"></a-spin>
          <div v-else class="p-8px flex flex-1 cursor-pointer items-center justify-center" @click="toggleExpandAll">
            <DoubleRightOutlined :class="['expand-icon', { expanded: isAllExpanded }]" />
            <span>{{ isAllExpanded ? '收起全部' : '展开全部' }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="main-right">
      <Form ref="formRef" v-model:form="formArr" :page-type="PageType.USER_MANAGE" @search="search" @setting="tableRef?.showTableSetting()"></Form>
      <BaseTable ref="tableRef" :page-type="PageType.USER_MANAGE" v-model:form="formArr" :get-list="GetList" :data-format="dataFormat" :loading="loading">
        <template #right-btn>
          <a-button type="primary" @click="syncData" :loading="syncLoading" v-if="btnPermission[150004]">同步用户</a-button>
        </template>
        <template #column>
          <template v-for="item in tableKey.filter((n) => n.is_show)" :key="item.key">
            <vxe-column
              :show-overflow="false"
              :sortable="item.is_sort"
              :field="item.key"
              :title="item.name"
              :width="item.width"
              :fixed="item.freeze == 1 ? 'left' : item.freeze == 2 ? 'right' : ''"
              :tree-node="item.treeNode"
            >
              <template v-if="item.key == 'operate'" #default="{ row }">
                <div class="btnBox">
                  <a-button @click="tapManipulate('view', row)" class="btn" v-if="btnPermission[150001]">查看</a-button>
                </div>
              </template>

              <template v-if="item.key == 'status'" #default="{ row }">
                <span>{{ statusMap[row.status] }}</span>
              </template>
              <template v-if="item.key == 'enterprise'" #default="{ row }">
                <span>{{ row.enterprise?.name }}</span>
              </template>
              <template v-if="item.key == 'company'" #default="{ row }">
                <span>{{ row.company?.name }}</span>
              </template>
              <template v-if="item.key == 'department'" #default="{ row }">
                <span>{{ row.department?.name }}</span>
              </template>
              <template v-if="item.key == 'permissions_role'" #default="{ row }">
                <a-tag v-for="item2 in row['permissions_role'].split(',')" :key="item2">{{ item2 }}</a-tag>
              </template>
            </vxe-column>
          </template>
        </template>
      </BaseTable>
    </div>
    <UserInfoDrawer ref="userInfoDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { GetInnerList, GetCompanyTreeOption, RoleSelect, Sync } from '@/servers/UserManager'
import { message } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import { cloneDeep } from '@/utils'
import { DoubleRightOutlined } from '@ant-design/icons-vue'
import UserInfoDrawer from './components/UserInfoDrawer.vue'

const { btnPermission } = usePermission()
const tableKey = computed(() => {
  if (!tableRef.value?.tableKey) return []
  return tableRef.value.tableKey
})

const userInfoDrawerRef = ref()

const headerOption = ref<any[]>([])
const memberSelectCompany = ref('')
const filterValue = ref<any>(null)
const queryParams = ref({
  org_id: '',
})
const deptAndCompanyTree = ref<any[]>([])
const deptTree = ref<any[]>([])
const enterpriseAccountOption = ref<any[]>([])
const selectedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const isAllExpanded = ref(true)

const treeProps = ref({
  children: 'children',
  title: 'name',
  key: 'id',
})

const loading = ref(false)
const expandLoading = ref(false)

const GetList = ref((obj) => {
  obj = { ...obj, ...queryParams.value }
  return GetInnerList(obj)
})

const visibleDataStr = () => ({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  content: '',
  okFn: () => {
    visibleData.value.isShow = false
  },
})
const visibleData: any = ref(visibleDataStr())

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

const supplierNameMap = ref({} as any)
// const departmentMap = ref<any>({})
const statusMap = ref({
  0: '停用',
  1: '启用',
  2: '已离职',
  3: '待邀请',
})
const dataFormat = (data) => {
  const department_id = [...new Set(data.map((v) => v.department_id).filter((v) => v))]
  if (department_id.length) {
    // GetUserDeptNameList({
    //   department_id,
    // }).then((res) => {
    //   res.data.forEach((v) => {
    //     departmentMap.value[v.department_id] = v.name
    //   })
    // })
  }
  return data
}

const filterHeaderOption = (input: string, option: any) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const formArr: any = ref([
  {
    label: '搜索真实姓名',
    value: null,
    type: 'input',
    selectArr: [],
    key: 'real_name',
  },
  {
    label: '搜索工号',
    value: null,
    type: 'input',
    selectArr: [],
    key: 'user_name',
  },
  {
    label: '账号状态',
    value: null,
    type: 'select',
    selectArr: [
      {
        label: '停用',
        value: 0,
      },
      {
        label: '启用',
        value: 1,
      },
      {
        label: '已离职',
        value: 2,
      },
      {
        label: '待邀请',
        value: 3,
      },
    ],
    key: 'status',
  },
  {
    label: '角色',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'permissions_role',
  },
])

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.USER_MANAGE) {
    const arr: any[] = []
    obj.USER_MANAGE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  getOptions()
  search()
  initScreening()
})

const getOptions = () => {
  RoleSelect({}).then((res) => {
    formArr.value.forEach((item) => {
      if (item.key === 'permissions_role') {
        item.selectArr = res.data.map((v) => {
          return {
            label: v.role_name,
            value: v.id,
          }
        })
      }
    })
  })
}

const syncLoading = ref(false)
const syncData = () => {
  syncLoading.value = true
  Sync({})
    .then((res) => {
      syncLoading.value = false
      if (res.success) {
        message.success('同步成功!')
        search()
      }
    })
    .catch(() => {
      syncLoading.value = false
    })
}

const tapManipulate = (type: string, row: any = '') => {
  visibleData.value = visibleDataStr()
  switch (type) {
    case 'view':
      userInfoDrawerRef.value.open(row, supplierNameMap.value)
      break
    default:
      break
  }
}

// 树形架构添加显示样式
const isEllipsis = (text: string) => {
  // 创建一个隐藏的 span 元素用于测量文本宽度
  const span = document.createElement('span')
  span.style.visibility = 'hidden'
  span.style.position = 'fixed'
  span.style.whiteSpace = 'nowrap'
  span.style.fontSize = '14px'
  span.style.fontFamily = 'inherit'
  span.innerText = text
  document.body.appendChild(span)
  const width = span.offsetWidth
  document.body.removeChild(span)
  // 120px 是你设置的最大宽度
  return width > 120
}

// 展开/收起全部
const toggleExpandAll = () => {
  if (isAllExpanded.value) {
    // 收起所有节点
    expandedKeys.value = []
  } else {
    expandLoading.value = true
    setTimeout(() => {
      expandLoading.value = false
    }, 1000)
    // 展开所有节点
    setTimeout(() => {
      const getAllKeys = (data: any[]): string[] => {
        let keys: string[] = []
        data.forEach((item) => {
          keys.push(item.id)
          if (item.children && item.children.length > 0) {
            keys = keys.concat(getAllKeys(item.children))
          }
        })
        return keys
      }
      expandedKeys.value = getAllKeys(deptTree.value)
    }, 0)
  }
  isAllExpanded.value = !isAllExpanded.value
}

const handleSelectDept = (selectedKeys: string[]) => {
  if (!selectedKeys.length) {
    return
  }
  queryParams.value.org_id = selectedKeys[0]
  console.log('selectedKeys[0]', selectedKeys[0])

  search()
}

const handleSelectTreeFilter = (newVal: string) => {
  if (newVal) {
    filterValue.value = null
    selectedKeys.value = [newVal]
    queryParams.value.org_id = newVal

    // 找到选中节点的所有父节点ID
    const findParentIds = (tree: any[], targetId: string, parentIds: string[] = []): string[] => {
      for (const node of tree) {
        if (node.id === targetId) {
          return parentIds
        }
        if (node.children && node.children.length) {
          const found = findParentIds(node.children, targetId, [...parentIds, node.id])
          if (found.length) return found
        }
      }
      return []
    }

    // 获取所有需要展开的节点ID
    const parentIds = findParentIds(deptTree.value, newVal)
    expandedKeys.value = [...new Set([...expandedKeys.value, ...parentIds, newVal])]

    nextTick(() => {
      search()
    })
  }
}

const changeDeptTree = () => {
  const findDept = (data: any[]): any => {
    for (const item of data) {
      if (item.id === memberSelectCompany.value) {
        return [item]
      }
      if (item.children) {
        const result = findDept(item.children)
        if (result) return result
      }
    }
    return null
  }
  const list = findDept(cloneDeep(enterpriseAccountOption.value)) || []

  list.map((item: any) => item)
  deptTree.value = list
}

const findHeaderOption = (id: string) => {
  const findNode = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      if (node.children && node.children.length) {
        const found = findNode(node.children)
        if (found) return found
      }
    }
    return null
  }

  const flattenNode = (node: any): any[] => {
    const result: any[] = []
    if (node) {
      result.push({
        id: node.id,
        name: node.name,
        company_name: node.company_name || '',
        type: node.type,
      })
      if (node.children && node.children.length) {
        node.children.forEach((child: any) => {
          result.push(...flattenNode(child))
        })
      }
    }
    return result
  }

  const foundNode = findNode(enterpriseAccountOption.value)
  console.log('foundNode', foundNode)

  if (foundNode) {
    headerOption.value = flattenNode(foundNode).filter((item) => ![1].includes(item.type))
  }
}

// 企业选择变化
const handleCompanyChange = () => {
  queryParams.value.org_id = memberSelectCompany.value
  findHeaderOption(memberSelectCompany.value)
  changeDeptTree()
  search()

  // 自动展开两层
  const tree = deptTree.value

  const expanded: string[] = []
  if (tree && tree.length > 0) {
    expanded.push(tree[0].id)
    if (tree[0].children && tree[0].children.length > 0) {
      expanded.push(tree[0].children[0].id)
    }
    // 只选中第一个节点
    selectedKeys.value = [tree[0].id]
  } else {
    selectedKeys.value = []
  }
  expandedKeys.value = expanded
}

const filterNodes = (nodes: any[]) => {
  return nodes.filter((node) => {
    if (node.type === 1) {
      if (node.children && node.children.length) {
        node.children = filterNodes(node.children)
      }
      return true
    }
    return false
  })
}

// 获取所有部门
const getAllDept = async (type?: string) => {
  loading.value = true
  try {
    const res = (await GetCompanyTreeOption({})) as any
    loading.value = false
    const setLevel = (data: any[], level = 0) => {
      data.forEach((item) => {
        item.level = level
        item.class = `level-${level}`
        if (item.children && item.children.length) {
          setLevel(item.children, level + 1)
        }
      })
      return data
    }

    enterpriseAccountOption.value = setLevel(res.data)

    deptAndCompanyTree.value = filterNodes(cloneDeep(enterpriseAccountOption.value))
    if (res.data.length && type === 'init') {
      queryParams.value.org_id = res.data[0].id
      memberSelectCompany.value = res.data[0].id
      handleCompanyChange()
    }

    // type为'init'时，自动展开两层并选中
    if (type === 'init') {
      nextTick(() => {
        const tree = deptTree.value
        const expanded: string[] = []
        if (tree && tree.length > 0) {
          expanded.push(tree[0].id)
          if (tree[0].children && tree[0].children.length > 0) {
            expanded.push(tree[0].children[0].id)
          }
          // 只选中第一个节点
          selectedKeys.value = [tree[0].id]
        } else {
          selectedKeys.value = []
        }
        expandedKeys.value = expanded
      })
    }
  } catch (error) {
    loading.value = false
  }
}

onActivated(async () => {
  await getAllDept('init')
})
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: row !important;

  .main-right {
    box-sizing: border-box;
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
  }
}

.userRoleBox {
  padding-right: 40px;

  .li {
    display: flex;
    align-items: center;
    margin-top: 24px;
    font-size: 16px;
    color: #000;

    .label {
      width: 120px;
      margin-right: 30px;
      text-align: right;

      .text {
        position: relative;

        .icon_i {
          position: absolute;
          top: 50%;
          left: -15px;
          padding-top: 7px;
          font-size: 12px;
          color: red;
          transform: translateY(-50%);
        }
      }
    }

    .input {
      flex: 1;
    }

    .checkedBox {
      flex: 1;
    }

    .select {
      flex: 1;
    }

    .treeSelect {
      flex: 1;
    }
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

.department-left {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-top: 8px;
  overflow-x: hidden;
  border: 1px solid #d9d9d9;
  border-radius: 4px;

  :deep(.ant-tree-treenode) {
    display: flex;
    align-items: center;
    width: 204px;
    height: 32px;
    color: #666;

    &:hover {
      color: #409eff;
      background-color: #eef2fa;

      .action-icons {
        opacity: 1;
      }
    }

    .ant-tree-node-content-wrapper {
      display: inline-block;
      flex: 1;

      &:hover {
        background-color: #eef2fa;
      }
    }
  }

  :deep(.ant-tree-switcher) {
    line-height: 32px;
  }

  :deep(.ant-tree-treenode-selected) {
    color: #409eff;
    background-color: #eef2fa;
  }

  :deep(.ant-tree-node-selected) {
    background-color: #eef2fa;
  }

  :deep(.anticon-caret-down) {
    color: #c0c0c0;
  }

  :deep(.action-icons) {
    opacity: 0;
    transition: opacity 0.2s;
  }
}

.tree-bottom {
  color: #333;
  border-top: 1px solid #d9d9d9;
}

.arch-left-list {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgb(0 0 0 / 30%);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

// 使用 SCSS 循环生成15级缩进样式
@for $i from 1 through 15 {
  :deep(.level-#{$i}) {
    .ant-tree-indent {
      width: $i * 8px;
    }
  }
}

.expand-icon {
  margin-right: 4px;
  transform: rotate(90deg);

  &.expanded {
    transform: rotate(-90deg);
  }
}
</style>

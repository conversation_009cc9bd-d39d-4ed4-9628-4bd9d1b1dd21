// 水印接口
import { request } from './request'
// 获取水印信息
export const GetWatermarkInfo = (data) => {
  return request({ url: '/api/System/Watermark/GetWatermarkInfo', data })
}
// 修改水印
export const SaveUIConfig = (data) => {
  return request({ url: '/api/System/Watermark/SaveUIConfig', data })
}
// 水印日志
export const GetOpLogInfos = (data) => {
  return request({ url: '/api/System/Watermark/GetOpLogInfos', data })
}
// 获取角色下拉框
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/System/Watermark/GetRoleSelectOption', data })
}
// 预览水印
export const PreviewWatermark = (data) => {
  return request({ url: '/api/System/Watermark/PreviewWatermark', data })
}

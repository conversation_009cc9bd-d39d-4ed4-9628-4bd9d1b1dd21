<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.ROLE_USERCONFIG" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.ROLE_USERCONFIG" :get-list="GetUserAssignList" :isIndex="true" :formFormat="formFormat" :isCheckbox="true">
      <template #left-btn>
        <a-button type="primary" @click="batchSwitch(true)">批量授权</a-button>
        <a-button type="primary" @click="batchSwitch(false)">批量取消</a-button>
      </template>
      <template #status="{ row }">
        <span>{{ row.status === 1 ? '启用' : '停用' }}</span>
      </template>
      <template #operate="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.is_assigned === true ? 1 : 0]" checked-children="授权" un-checked-children="取消" />
      </template>
    </BaseTable>
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { onMounted } from 'vue'
import { GetUserAssignList, UserAssign, UserRevoke, UserBatchAssign, UserBatchRevoke } from '@/servers/Role'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
// const { btnPermission } = usePermission()
const listId: any = ref(null)
const formFormat = (data) => {
  return {
    ...data,
    role_id: listId.value,
  }
}

const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})

const formArr: any = ref([
  {
    label: '搜索用户账号',
    value: null,
    type: 'input',
    key: 'account_name',
  },
  {
    label: '搜索用户工号',
    value: null,
    type: 'input',
    key: 'work_num',
  },
  {
    label: '搜索公司',
    value: null,
    type: 'input',
    key: 'company_name',
  },
  {
    label: '搜索部门',
    value: null,
    type: 'input',
    key: 'department_name',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
    key: 'status',
  },
])

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.ROLE_USERCONFIG) {
    const arr: any[] = []
    obj.ROLE_USERCONFIG.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  listId.value = router.currentRoute.value.params.id
  search()
  initScreening()
})

const tapSwitch = (e, row) => {
  visibleData.isShow = true
  visibleData.title = '授权'
  visibleData.content = `是否确认${row.is_assigned ? '取消授权' : '授权'}该用户？`
  visibleData.confirmBtnText = '确认'
  visibleData.okType = 'danger'
  visibleData.isCancelBtn = true
  visibleData.okFn = () => {
    const api = row.is_assigned ? UserRevoke : UserAssign
    api({ user_id: row.account_id, role_id: listId.value })
      .then((res) => {
        if (res.success) {
          visibleData.isShow = false
          message.success(res.message)
          tableRef.value.search()
        }
      })
      .catch((error) => {
        visibleData.isShow = false
        message.error(error)
      })
  }
}

const batchSwitch = (type) => {
  if (tableRef.value.checkItemsArr.length === 0) {
    message.info('请勾选需要操作的数据！')
    return
  }
  visibleData.isShow = true
  visibleData.title = '授权'
  visibleData.content = `是否确认${type ? '授权' : '取消授权'}该用户？`
  visibleData.confirmBtnText = '确认'
  visibleData.okType = 'danger'
  visibleData.isCancelBtn = true
  visibleData.okFn = () => {
    const api = type ? UserBatchAssign : UserBatchRevoke
    const userIds = tableRef.value.checkItemsArr.map((x) => x.account_id)
    api({ user_ids: userIds, role_id: listId.value })
      .then((res) => {
        if (res.success) {
          visibleData.isShow = false
          tableRef.value.search()
        }
      })
      .catch((error) => {
        visibleData.isShow = false
        message.error(error)
      })
  }
}

const tableRef = ref()
const search = () => tableRef.value.search()
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

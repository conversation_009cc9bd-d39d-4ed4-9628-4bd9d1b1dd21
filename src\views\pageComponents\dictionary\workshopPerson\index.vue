<template>
  <div class="main px-20 py-12">
    <Form v-model:form="formArr" :page-type="PageType.WORKSHOP_PERSON" :clearCb="handleReset" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.WORKSHOP_PERSON" :isIndex="true" :get-list="getWorkshopPersons">
      <template #right-btn>
        <a-button v-if="btnPermission[PERM_CODE.CREATE]" type="primary" :icon="h(PlusOutlined)" @click="handleUpdateCalendar('add')">新建生产实体人员</a-button>
      </template>
      <template #workshop_type="{ row }">
        <span>{{ getWorkshopType(row.workshop_type) || '--' }}</span>
      </template>
      <template #position="{ row }">
        <span>{{ row.user?.position_name || '--' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button v-if="btnPermission[PERM_CODE.VIEW]" @click="handleViewCalendar(row.id)" class="btn">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="btnPermission[PERM_CODE.EDIT]" @click="handleUpdateCalendar('edit', row.id)">编辑</a-menu-item>
                <a-menu-item v-if="btnPermission[PERM_CODE.DELETE]" @click="handleDelete(row.id)">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>
    <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, h, shallowRef, provide } from 'vue'
import { PageType } from '@/common/enum'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getWorkshopPersons, getSelectOptions, getPositionOptions } from '@/servers/workshopPerson'
import Form from '@/components/Form.vue'
import { customFilterOption } from '@/utils/index'
import { WorkshopTypeOptions, getWorkshopType, PERM_CODE } from './types'
import TimeViewDrawer from './components/WorkshopViewDrawer.vue'
import TimeUpdateDrawer from './components/WorkshopUpdateDrawer.vue'
import TimeDeleteModal from './components/WorkshopDeleteModal.vue'

const { btnPermission } = usePermission()
const productStepOptions = ref()
provide('productStepOptions', productStepOptions)

const tableRef = ref()
const search = () => tableRef.value.search()

// 查询表单
const formArr = ref<any[]>([
  {
    label: '请选择生产实体筛选',
    value: null,
    type: 'cascader',
    fieldNames: { label: 'name', value: 'id' },
    expandTrigger: 'hover',
    selectArr: null,
    changeOnSelect: true,
    key: 'workshop_id_filter',
    onChange: (value, selectedOptions) => {
      handleChangeWorkshopFilter(value, selectedOptions)
    },
  },
  {
    label: '请选择生产实体类型',
    value: null,
    type: 'select',
    selectArr: WorkshopTypeOptions,
    key: 'workshop_type',
    onChange: (value) => {
      handleChangeWorkshopType(value)
    },
  },
  {
    label: '请选择生产实体',
    value: null,
    type: 'select',
    selectArr: null,
    key: 'workshop_id',
    onChange: () => {
      handleChangeWorkshop()
    },
  },
  {
    label: '请选择工作人员',
    value: [],
    type: 'select',
    multiple: true,
    showSearch: true,
    filterOption: (input, option) => customFilterOption(input, option, 'real_name'),
    selectArr: null,
    key: 'account_ids',
    fieldNames: { label: 'real_name', value: 'account_id' },
  },
  {
    label: '请选择岗位',
    value: [],
    type: 'select',
    multiple: true,
    showSearch: true,
    filterOption: customFilterOption,
    selectArr: null,
    key: 'position_ids',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])

// 切换生产实体筛选
const handleChangeWorkshopFilter = (value, selectedOptions) => {
  // 如果值为空或未选择选项，直接返回
  if (!value || !selectedOptions || selectedOptions.length === 0) {
    formArr.value.forEach((item) => {
      if (item.key === 'workshop_type') {
        // 清空生产实体类型
        item.value = null
      } else if (item.key === 'workshop_id') {
        // 清空生产实体及其下拉选项
        item.selectArr = []
        item.value = null
      }
    })
    return
  }
  const options = selectedOptions?.length ? selectedOptions[selectedOptions.length - 1] : {}
  // 根据生产实体类型，获取生产实体
  getSelectOptionList(options.workshop_type)
  formArr.value.forEach((item) => {
    if (item.key === 'workshop_id_filter') {
      item.value = options.name
    } else if (item.key === 'workshop_type') {
      item.value = options.workshop_type
    } else if (item.key === 'workshop_id') {
      item.value = options.id
    }
  })
}

/** 切换生产实体类型时获取生产实体 */
const handleChangeWorkshopType = (workshopType: number) => {
  // 切换时需清空生产实体数据
  const target = formArr.value?.find((item) => item.key === 'workshop_id')
  target.selectArr = []
  target.value = null
  getSelectOptionList(workshopType)
}

/** 切换生产实体：需要清空生产实体筛选值 */
const handleChangeWorkshop = () => {
  formArr.value.find((item) => item.key === 'workshop_id_filter').value = null
}

/** 重置表单:需要额外清空生产实体下拉选项 */
const handleReset = () => {
  formArr.value.find((item) => item.key === 'workshop_id').selectArr = []
}

// component
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  id: null,
})
const refresh = () => {
  tableRef.value.refresh()
}

// 新增/编辑工厂日历
const handleUpdateCalendar = (type: string, id?: string) => {
  component.value = TimeUpdateDrawer
  open.value = true
  initValue.type = type
  initValue.id = id
}

// 查看工厂日历
const handleViewCalendar = (id: number) => {
  component.value = TimeViewDrawer
  open.value = true
  initValue.id = id
}

// 删除工厂日历
const handleDelete = (id: number) => {
  component.value = TimeDeleteModal
  open.value = true
  initValue.id = id
}

// 获取生产实体筛选、工作人员、岗位下拉列表
const workshopFilterOptions = ref()
const workshopOptions = ref()
const userOptions = ref()
provide('workshopFilterOptions', workshopFilterOptions)

const getSelectOptionList = async (workshop_type?: number) => {
  const res = await getSelectOptions({ workshop_type })
  if (workshop_type) {
    workshopOptions.value = res.data?.workshop?.map((item) => ({ label: item.name, value: item.id }))
    formArr.value.find((item) => item.key === 'workshop_id').selectArr = workshopOptions.value
  } else {
    // 生产实体筛选
    workshopFilterOptions.value = res.data?.workshop_tree || []
    userOptions.value = res.data?.user || []
    // 查询表单添加 selectArr 选项
    formArr.value.forEach((item) => {
      if (item.key === 'workshop_id_filter') {
        item.selectArr = workshopFilterOptions.value
      } else if (item.key === 'account_ids') {
        item.selectArr = userOptions.value
      }
    })
  }
}

// 获取岗位下拉列表
const positionOptions = ref()
const getPositionList = async () => {
  const res = await getPositionOptions({})
  positionOptions.value = res.data?.list?.map((item) => ({ value: item.id, label: item.name })) || []
  formArr.value.find((item) => item.key === 'position_ids').selectArr = positionOptions.value
}

onMounted(() => {
  getSelectOptionList()
  getPositionList()
})
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}
</style>

<template>
  <a-tooltip :title="isOverflow ? content : null">
    <span ref="textRef" class="truncate-text">
      {{ content }}
    </span>
  </a-tooltip>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

defineProps<{
  /** 文本内容 */
  content: string
  /** 文本最大宽度 */
  width: number
}>()

const textRef = ref<any>(null)
const isOverflow = ref(false)

onMounted(() => {
  if (textRef.value) {
    isOverflow.value = textRef.value.scrollWidth > textRef.value.clientWidth
  }
})
</script>

<style lang="scss" scoped>
.truncate-text {
  display: inline-block;
  width: v-bind('`${width}px`');
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

<template>
  <a-drawer :footer="false" v-model:open="detailVisible" :width="'65vw'" title="查看派工单" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }" :bodyStyle="{ padding: '0' }">
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" header="" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">派工单号</p>
                <p class="value">{{ target.dispatch_order_number }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">物料编码</p>
                <p class="value">{{ target.material_number }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">物料名称</p>
                <p class="value">{{ target.material_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">客户</p>
                <p class="value">{{ target.customer_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">生产阶别</p>
                <p class="value">{{ target.production_stage_text }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">状态</p>
                <p class="value">{{ target.order_status_text }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">预计开工日期</p>
                <p class="value">{{ target.plan_start_time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">预计完工日期</p>
                <p class="value">{{ target.plan_end_time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">计划数量</p>
                <p class="value">{{ target.plan_nums }}</p>
              </a-col>

              <a-col class="gutter-row" :span="8">
                <p class="label">工艺流程</p>
                <p class="value">{{ target.process_flow_text }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">开始工序</p>
                <p class="value">{{ target.start_process }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">结束工序</p>
                <p class="value">{{ target.end_process }}</p>
              </a-col>

              <a-col class="gutter-row" :span="8">
                <p class="label">下达时间</p>
                <p class="value">{{ target.created_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">下达人</p>
                <p class="value">{{ target.creator?.real_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">备注</p>
                <div class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: '300px' }">
                    <template #title>
                      {{ target.remark }}
                    </template>
                    {{ target.remark }}
                  </a-tooltip>
                </div>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="工艺流程" :style="customStyle">
            <a-row :gutter="16">
              <div class="w-300px">
                <SimpleTable :tableKey="tableKey" :data="target.flow_maps"></SimpleTable>
              </div>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="3" header="其他信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">创建时间</p>
                <p class="value">{{ target.created_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="16">
                <p class="label">实际开工时间</p>
                <p class="value">{{ target.actual_start_time }}</p>
              </a-col>

              <a-col class="gutter-row" :span="24">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.creator?.real_name }}</div>
                      <div>所在部门：{{ target?.creator?.department_name ? target?.creator?.department_name : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.creator?.position_name ? target?.creator?.position_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.creator?.real_name ? target?.creator?.real_name : '--' }}</span>
                      <span v-if="target?.creator?.department_name || target?.creator?.position_name" class="detailValueDescription">
                        （
                        <span v-if="target?.creator?.position_name">{{ target?.creator?.position_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.creator?.department_name">
                          {{ target?.creator?.department_name.length > 10 ? target?.creator?.department_name.slice(0, 10) + '...' : target?.creator?.department_name }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target.updated_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="16">
                <p class="label">实际完工时间</p>
                <p class="value">{{ target.actual_end_time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="24">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.modifier?.real_name }}</div>
                      <div>所在部门：{{ target?.modifier?.department_name ? target?.modifier?.department_name : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.modifier?.position_name ? target?.modifier?.position_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.modifier?.real_name ? target?.modifier?.real_name : '--' }}</span>
                      <span v-if="target?.modifier?.department_name || target?.modifier?.position_name" class="detailValueDescription">
                        （
                        <span v-if="target?.modifier?.position_name">{{ target?.modifier?.position_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.modifier?.department_name">
                          {{ target?.modifier?.department_name.length > 10 ? target?.modifier?.department_name.slice(0, 10) + '...' : target?.modifier?.department_name }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { Details } from '@/servers/dispatchOrder'
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'

const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'
const activeKey = ref(['1', '2', '3'])
const detailVisible = ref(false)
const detailloading = ref(false)

const target = ref<any>(null)
const tableKey = [
  { title: '顺序', field: 'sequence', width: 80 },
  { title: '工序名称', field: 'process_name' },
]
const open = (id) => {
  target.value = null
  detailloading.value = true
  detailVisible.value = true

  Details({ id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
    })
    .catch(() => {
      detailloading.value = false
    })
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

.value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

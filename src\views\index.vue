<template>
  <div class="page">
    <div class="memuBox" :class="collapsed ? 'memuActive' : ''">
      <div class="titleBox">
        <div class="title">
          <img class="logo" src="../assets/image/logo-o.png" alt="" />
          <div class="text" v-show="!collapsed">MES系统</div>
        </div>

        <a-button class="toggleBtn" :class="collapsed ? 'active' : ''" type="primary" @click="toggleCollapsed">
          <MenuUnfoldOutlined v-if="collapsed" />
          <MenuFoldOutlined v-else />
        </a-button>
      </div>
      <div class="box">
        <a-menu mode="inline" theme="dark" :inline-collapsed="collapsed" v-model:selectedKeys="selectedMenuKeys" v-model:openKeys="openKeys">
          <template v-for="item in menuList" :key="item.path">
            <!-- 在左侧菜单不显示的页面 -->
            <template v-if="!item.children && item.show != false">
              <a-menu-item :key="item.path" @click="toPage(item)">
                <template #icon>
                  <i :class="`iconfont ${'icon-' + iconfontArr[item.path]}`"></i>
                </template>
                {{ item.title }}
              </a-menu-item>
            </template>
            <!-- 在左侧菜单显示的页面 -->
            <template v-if="item.children && item.show != false">
              <a-sub-menu :key="item.path">
                <!-- <template #icon>
                  <i :class="`iconfont ${'icon-' + iconfontArr[item.path]}`"></i>
                </template> -->

                <template #icon>
                  <a-badge :class="{ badgeBot: collapsed }" :dot="!!item.auditCount">
                    <i :class="`iconfont ${'icon-' + iconfontArr[item.path]}`"></i>
                  </a-badge>
                </template>

                <template #title>{{ item.title }}</template>
                <template v-for="item2 in item.children" :key="item2.path">
                  <a-menu-item v-if="!item2.children" @click="toPage(item2)" :key="item2.path">
                    <span>{{ item2.title }}</span>
                    <a-badge :count="item2.auditCount" style="margin-left: 3px" size="small" v-if="item2.auditCount > 0" />
                  </a-menu-item>
                  <a-sub-menu v-if="item2.children" :key="item2.path">
                    <template #title>
                      <a-badge :class="{ badgeBot: collapsed }" :dot="!!item2.auditCount" :offset="[12, 6]">
                        <span style="color: rgb(255 255 255 / 65%)">{{ item2.title }}</span>
                      </a-badge>
                    </template>
                    <template v-for="item3 in item2.children" :key="item3.path">
                      <a-menu-item @click="toPage(item3)">
                        <span>{{ item3.title }}</span>
                        <a-badge :count="item3.auditCount" style="margin-left: 3px" size="small" v-if="item3.auditCount > 0" />
                      </a-menu-item>
                    </template>
                  </a-sub-menu>
                </template>
              </a-sub-menu>
            </template>
          </template>
        </a-menu>
      </div>
      <div class="version">
        <InfoCircleOutlined class="icon" />
        <!-- 版本号：{{ version ? version : '--' }} -->
        版本号：{{ version ? version : 'MES v1.0' }}
      </div>
    </div>
    <div class="mainBox" :class="collapsed ? 'active' : ''">
      <div class="topNav">
        <div class="navBox">
          <a-tabs :tabBarGutter="0" v-model:activeKey="navPagPath" tab-position="top" type="editable-card" size="small" hideAdd @edit="handleClose" @tabClick="tapNavTabItem">
            <a-tab-pane v-for="item in navPageArr" :key="item.path" :tab="item.name" :closable="navPageArr.length !== 1"></a-tab-pane>
          </a-tabs>
          <!-- <div class="item" :class="navPagPath == item.path ? 'active' : ''" v-for="(item, i) in navPageArr" :key="item.path" @click="tapNavTabItem(item)">
            <div class="text">{{ item.name }}</div>
            <div class="icon" @click.stop="handleClose(i)" v-if="navPageArr.length > 1">
              <CloseOutlined />
            </div>
          </div> -->
        </div>
        <div class="btnBox">
          <a-dropdown @openChange="(val) => (dropDownVisible = val)">
            <template #overlay>
              <div @click="dropDownVisible = false" class="conternBox">
                <div class="nameBox">
                  <div class="nameImg"><img src="@/assets/image/name.png" /></div>
                  <div class="nameText">
                    <div class="textTop">{{ account?.real_name || '' }}</div>
                    <div class="textBot" v-show="account?.jobtitlename">
                      <div class="label" style="display: flex">
                        岗
                        <div style="width: 24px"></div>
                        位：
                      </div>
                      <span>{{ account?.jobtitlename }}</span>
                    </div>
                    <div class="textBot" v-show="account?.department">
                      <span class="label">所在部门：</span>
                      <span>{{ account?.department }}</span>
                    </div>
                    <div class="textBot" v-show="account?.company">
                      <span class="label">所属公司：</span>
                      <span>{{ account?.company }}</span>
                    </div>
                  </div>
                </div>

                <a-popover placement="leftBottom" overlayClassName="header-popover">
                  <template #content>
                    <div class="scroll-bar max-h-[230px] overflow-y-auto">
                      <div v-for="item in accountList" :key="item?.account_id" class="gap-12px p-16px header-account flex cursor-pointer items-center" @click="handleChangeAccount(item.account_id)">
                        <div class="user-type">
                          {{ (item?.unit || item?.company || '个人').slice(0, 1) }}
                        </div>
                        <div class="flex flex-col">
                          <div class="c-#333 text-14px w-250px truncate">
                            {{ item?.account_type === 0 ? item?.unit || item?.company : '个人' }}
                          </div>
                          <div class="c-#666 text-12px">
                            {{ item?.account_type == 0 ? item?.real_name : item?.account_name }}
                          </div>
                        </div>
                        <CheckOutlined v-if="item.account_id == userInfo.id" class="text-primary" />
                      </div>
                    </div>
                    <div class="text-primary text-14px py-16px cursor-pointer text-center" @click="handleNavigateCenter">个人中心（账号关联/绑定/修改密码）></div>
                  </template>
                  <div class="header-user-select">
                    <SwapOutlined class="mr-12px" />
                    切换账号
                  </div>
                </a-popover>

                <div class="logout" @click="handleLogout">
                  <i class="iconfont icon-logout"></i>
                  退出登录
                </div>
              </div>
            </template>

            <div :style="dropDownVisible ? 'color: #409EFF;' : ''" class="btnBoxMainText">
              {{ account?.real_name || '' }}
              <span class="btnBoxsubText" v-if="account || account?.jobtitlename">
                (
                <!-- 内部 -->
                <span>
                  <span v-if="account">{{ account?.jobtitlename }}</span>
                  <span v-if="account?.jobtitlename && account?.department">|</span>
                  <span v-if="account?.department">{{ account?.department.length > 10 ? account?.department.slice(0, 16) + '...' : account?.department }}</span>
                </span>
                )
              </span>
            </div>
          </a-dropdown>
        </div>
      </div>
      <!-- <router-view></router-view> -->

      <router-view v-slot="{ Component }">
        <keep-alive class="boxStr">
          <component :is="Component" v-if="route.meta.KeepAlive" :key="route.path" />
        </keep-alive>
        <component v-if="!route.meta.KeepAlive" :is="Component" :key="route.path"></component>
      </router-view>
    </div>
    <a-modal v-model:open="isKeyDown" width="600px" title="批量输入">
      <a-textarea v-model:value="commodityCode" placeholder="多个商品编码，以逗号分隔或每行一个商品编码" :rows="4" />
      <template #footer>
        <a-button key="back" style="float: left">清空</a-button>
        <a-button key="back" @click="isKeyDown = false">取消</a-button>
        <a-button key="submit" type="primary" @click="isKeyDown = false">确认</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { MenuFoldOutlined, MenuUnfoldOutlined, InfoCircleOutlined, SwapOutlined, CheckOutlined } from '@ant-design/icons-vue'
import { beforLogout } from '@/utils/index'
import { UserInfo, GetUserCenterUrl, GetAccountList, Logout, SwitchAccount, GetUserInfoByToken } from '@/servers/User'
import globalStore from '@/store/globalStore'
import EventBus from '@/utils/EventBus'
// import { Info } from '@/servers/Common'
const account = ref()
const router = useRouter()
const route = useRoute()
const menuList: any = ref([])
const userData: any = ref({})
const commodityCode = ref(null)
const isKeyDown = ref(false)
const navPagPath = ref('/')
const navPageArr: any = ref([])
const collapsed = ref(false)
const selectedMenuKeys = ref(['/'])
const openKeys: any = ref()
const version = ref('')
const iconfontArr = {
  '/settingCenter': 'xitongguanli',
  '/manufacturingCente': 'dianpuguanli',
  '/warehouseManagement': 'tongzhiguanli',
  '/dataCenter': 'ziliaoguanli',
  '/reportCenter': 'gonggaofabu',
}
const dropDownVisible = ref(false)
const userInfo = JSON.parse(localStorage.getItem('userData') || '{}')
const accountList = ref<any>([{ account_id: userInfo.id }])

const store = globalStore()
// 不显示的页面
const otherMenu = ref([{ path: '/dictionarySetting', name: '配置字典管理', show: false }])

const checkRouter = (path: string) => {
  const fn = (arr, parentKeys: any) => {
    for (const item of arr) {
      if (item.path == path) {
        navPagPath.value = path
        selectedMenuKeys.value = [path]
        openKeys.value = [...parentKeys, path]
      }
      if (item?.children?.length) fn(item.children, [...parentKeys, item.path])
    }
  }
  fn(menuList.value, [])
}

// 路由和测导航栏
const initialization = () => {
  const path = router.currentRoute.value.path
  const userDataStr = localStorage.getItem('userData') || ''
  const navPageArrStr = localStorage.getItem('navPageArr') || ''
  if (userDataStr) {
    userData.value = JSON.parse(userDataStr)
    // 手动拼接不显示的页面
    menuList.value = userData.value.permissions_infos_v2.concat(otherMenu.value)
    // menuList.value = mockdata.value.concat(otherMenu.value)
    if (path != menuList.value[0].path) {
      checkRouter(
        menuList.value[0]?.children[0]?.children?.length > 0
          ? menuList.value[0].children[0].children[0].path
          : menuList.value[0]?.children?.length > 0
            ? menuList.value[0].children[0].path
            : menuList.value[0].path,
      )
      router.push(
        menuList.value[0]?.children[0]?.children?.length > 0
          ? menuList.value[0].children[0].children[0].path
          : menuList.value[0]?.children?.length > 0
            ? menuList.value[0].children[0].path
            : menuList.value[0].path,
      )
    }
    if (navPageArrStr) {
      navPageArr.value = JSON.parse(navPageArrStr)
      if (path != '/') {
        checkRouter(path)
        router.push(path)
      }
    } else {
      navPageArr.value.push(
        menuList.value[0]?.children[0]?.children?.length > 0 ? menuList.value[0].children[0].children[0] : menuList.value[0]?.children?.length > 0 ? menuList.value[0].children[0] : menuList.value[0],
      )
      checkRouter(
        menuList.value[0]?.children[0]?.children?.length > 0
          ? menuList.value[0].children[0].children[0].path
          : menuList.value[0]?.children?.length > 0
            ? menuList.value[0].children[0].path
            : menuList.value[0].path,
      )
    }
    localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
  } else {
    localStorage.clear()
    navPageArr.value.push(menuList.value[1])
  }
}
// 获取用户信息
const getUserData = () => {
  UserInfo().then((res) => {
    account.value = res.data
    userInfo.id = res.data.id
    store.setUserInfo(res.data)
  })
}

const getUserAccountList = () => {
  GetAccountList().then((res) => {
    accountList.value = res.data
  })
}

// 根据 path查找menuList
const findMenuList = (path: string, first: any) => {
  let val = null
  menuList.value.forEach((item) => {
    if (item.path == path) {
      val = item
      return
    }
    if (item.children)
      item.children.forEach((c) => {
        if (c.path === path) {
          val = c
        }
      })
  })
  if (!val && first) {
    val = first
  }
  return val
}

watch(
  () => router.currentRoute.value.path,
  (newValue) => {
    checkRouter(newValue)
  },
  { immediate: true },
)

const toPage = (val) => {
  let isPresence = true
  navPageArr.value.forEach((item) => {
    if (item.path == val.path) {
      isPresence = false
    }
  })
  if (isPresence) {
    navPageArr.value.push(val)
  }
  localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
  if (val.callBack) {
    val.callBack()
  }
  router.push({
    path: val.path,
  })
  navPagPath.value = val.path
}

// 退出登录
const handleLogout = () => {
  Logout().then((res) => {
    window.location.href = res?.data
  })
}
// 点击菜单
// const tapMenu = (e: any) => {
//   const fn = (item, path) => {
//     if (item.path == path) {
//       let isPresence = true
//       navPageArr.value.forEach((item2) => {
//         if (item2.path == path) {
//           isPresence = false
//         }
//       })
//       if (isPresence) {
//         navPageArr.value.push(item)
//       }
//     }
//   }
//   menuList.value.forEach((item) => {
//     if (e.keyPath.length > 1) {
//       if (e.keyPath[0] == item.path) {
//         item.children.forEach((item2) => {
//           fn(item2, e.key)
//         })
//       }
//     } else {
//       fn(item, e.key)
//     }
//   })
//   localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
//   router.push(e.key)
// }

// 点击标签跳转子路由
const tapNavTabItem = (path) => {
  router.push(path)
  setTimeout(() => {
    navPagPath.value = path
  }, 0)
}
// 关闭标签子路由
const handleClose = (path) => {
  if (path === navPagPath.value) {
    const prePath = navPageArr.value[navPageArr.value.findIndex((e) => e.path === path) - 1]?.path
    const nextPath = navPageArr.value[navPageArr.value.findIndex((e) => e.path === path) + 1]?.path
    if (prePath) {
      checkRouter(prePath)
      router.push(prePath)
      navPagPath.value = prePath
    } else {
      checkRouter(nextPath)
      router.push(nextPath)
      navPagPath.value = nextPath
    }
  }
  navPageArr.value = navPageArr.value.filter((item) => item.path !== path)
  localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
}
// 菜单导航栏切换大小
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}
// 获取版本号
// const checkEdition = () => {
//   Info().then((res) => {
//     version.value = res.data.version
//     const localVersion = localStorage.getItem('version')
//     if (!localVersion || localVersion === '') {
//       localStorage.setItem('version', res.data.version)
//     } else {
//       if (res.data.version != localVersion) {
//         localStorage.setItem('version', res.data.version)
//         setTimeout(() => {
//           window.location.reload()
//         }, 100)
//       }
//     }
//   })
// }

let timer

const handleNavigateCenter = () => {
  GetUserCenterUrl().then((res) => {
    window.location.href = res?.data
  })
}

const handleChangeAccount = (accountId: string) => {
  if (accountId == userInfo.id) {
    return
  }
  const login_token = JSON.parse(localStorage.getItem('userData') as any).login_token
  SwitchAccount({ account_id: accountId })
    .then((res) => {
      if (!res.data) return
      GetUserInfoByToken().then((res) => {
        res.data.login_token = login_token
        localStorage.setItem('userData', JSON.stringify(res.data))
        window.location.href = '/'
      })
    })
    .catch((err) => {
      if (err.response.data.code == 21008) {
        // 没账号权限的情况，没有账号信息返回，自己重组
        beforLogout()
        account.value = accountList.value.find((n) => n.account_id == accountId)
        account.value.id = accountId
        localStorage.setItem(
          'userData',
          JSON.stringify({
            ...account.value,
            login_token,
            id: accountId,
            permissions_infos: null,
          }),
        )
        userInfo.id = accountId
        router.push({ path: '/404' }).then(() => {
          window.location.reload()
        })
      }
    })
}

onMounted(() => {
  initialization()
  getUserData()
  EventBus.on('custom-event', toPage)
  // checkEdition()
  // setInterval(
  //   () => {
  //     checkEdition()
  //   },
  //   1000 * 60 * 5,
  // )
  getUserAccountList()
})

onUnmounted(() => {
  if (timer) clearTimeout(timer)
})

watch(
  () => router.currentRoute.value.path,
  (newValue) => {
    try {
      const arr = JSON.parse(localStorage.getItem('userData') || '').permissions_infos
      let item: any = null
      arr.forEach((x) => {
        if (x.path === newValue) item = x
        if (x.children.length != 0) {
          x.children.forEach((y) => {
            if (y.path === newValue) {
              item = y
            }
            if (y.children && y.children.length != 0) {
              y.children.forEach((z) => {
                if (z.path === newValue) {
                  item = z
                }
              })
            }
          })
        }
      })
      if (item) {
        if (navPageArr.value.findIndex((e) => e.path === item.path) === -1) navPageArr.value.push(item)
        localStorage.setItem('navPageArr', JSON.stringify(navPageArr.value))
        checkRouter(newValue)
      }
    } catch (error) {}
  },
  { immediate: true },
)
</script>
<style lang="scss" scoped>
.page {
  box-sizing: border-box;
  display: flex;
  min-width: 1200px;
  min-height: 100vh;

  :deep(.ant-menu-dark) {
    background-color: rgb(0 0 0 / 0%);

    .ant-menu-item {
      border-left: 3px solid transparent;
    }
  }

  :deep(.ant-menu-dark.ant-menu-inline .ant-menu-sub.ant-menu-inline) {
    background-color: rgb(0 0 0 / 10%);
  }

  :deep(.ant-menu-dark .ant-menu-item-selected) {
    color: #7ebcfc;
    background-color: rgb(64 158 255 / 15%);
    border-left: 3px solid #7ebcfc;
  }

  :deep(.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-submenu-title),
  :deep(.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected)) {
    &:hover,
    &:active {
      color: #7ebcfc;
      background-color: rgb(64 158 255 / 15%);
    }
  }

  :deep(.ant-menu-inline .ant-menu-item),
  :deep(.ant-menu-vertical .ant-menu-item),
  :deep(.ant-menu-inline .ant-menu-submenu-title),
  :deep(.ant-menu-vertical .ant-menu-submenu-title) {
    width: 100%;
    margin-inline: 0;
    border-radius: 0;
  }

  :deep(.ant-menu-inline-collapsed) {
    width: 62px;
  }

  :deep(.ant-menu-inline > .ant-menu-item),
  :deep(.ant-menu-vertical > .ant-menu-item),
  :deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title),
  :deep(.ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title) {
    height: 40px;
    line-height: 40px;
  }

  .memuBox {
    width: 180px;
    background-color: #1f1f3f;

    // flex-shrink: 0;
    // transition: all 1s;
    &.memuActive {
      width: 62px;
    }

    .titleBox {
      position: relative;
      min-width: 100%;
      height: 50px;
      background-color: #1f1f3f;

      .title {
        display: flex;
        justify-content: center;

        // padding: 10px 0 10px 14px;
        padding-block: 10px;
        overflow: hidden;
        font-size: 18px;
        font-weight: bold;
        color: #fff;

        .logo {
          width: 30px;
          height: 30px;
        }

        .text {
          margin-left: 10px;
          line-height: 30px;
          text-wrap: nowrap;
        }
      }

      .toggleBtn {
        position: absolute;
        top: 0;
        left: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 35px;
        height: 40px;
        padding: 0;
        color: #999;
        cursor: pointer;
        background-color: rgb(235 235 235);
        border-radius: 0;
      }
    }

    .version {
      height: 34px;
      font-size: 11px;
      line-height: 34px;
      color: #999;
      text-align: center;
      user-select: none;
      background-color: #1f1f3f;

      .icon {
        margin-right: 4px;
      }
    }

    .box {
      flex: 1;
      scrollbar-width: none;
      height: calc(100vh - 85px);
      overflow-y: scroll;
      -ms-overflow-style: none;

      .iconfont {
        font-size: 20px;
        color: rgb(255 255 255 / 65%);
      }

      &::-webkit-scrollbar {
        width: 0;
        height: 0;
      }
    }
  }

  .mainBox {
    display: flex;
    flex-direction: column;
    width: calc(100% - 180px);

    // transition: all 1s;
    height: 100vh;

    &.active {
      width: calc(100% - 62px);
    }

    .topNav {
      display: flex;
      align-items: center;
      justify-content: end;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      padding: 0 20px 0 35px;
      background: rgb(235 235 235);

      .navBox {
        position: relative;
        display: flex;
        flex: 1;
        align-items: flex-end;
        height: 100%;
        margin-right: 24px;
        overflow: hidden;

        :deep(.ant-tabs) {
          width: 100%;
          line-height: 14px;
        }

        :deep(.ant-tabs-nav) {
          margin-bottom: 0;

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-nav-list) {
          background: linear-gradient(to bottom, rgb(235 235 235) 50%, white 50%);
        }

        :deep(.ant-tabs-tab) {
          position: relative;
          height: 32px;
          padding: 0 10px;
          font-size: 12px;
          color: #999;
          background-color: rgb(235 235 235);
          border: none;
          border-radius: 8px 8px 0 0;

          &::before {
            position: absolute;
            left: 0;
            width: 1px;
            height: 10px;
            content: '';
            background-color: #d9d9d9;
          }

          // top: -1px;
          .ant-tabs-tab-remove {
            padding: 0;
            padding-right: 4px;
            color: #999 !important;
          }

          .ant-tabs-tab-btn {
            transition: none;
          }
        }

        :deep(.ant-tabs-tab-with-remove) {
          .ant-tabs-tab-remove {
            color: white;
          }
        }

        :deep(.ant-tabs-tab-active) {
          position: relative;
          top: 0;
          color: #000;
          background-color: #fff;

          .ant-tabs-tab-remove {
            color: #000;
          }

          .ant-tabs-tab-btn {
            color: #000;
            text-shadow: none;
          }

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-tab-active) + .ant-tabs-tab {
          border-bottom-left-radius: 8px;

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-tab:has(+ .ant-tabs-tab-active)) {
          border-bottom-right-radius: 8px;
        }

        :deep(.anticon-ellipsis) {
          color: #fff;
        }

        :deep(.ant-tabs-nav-more) {
          position: relative;
          top: -1px;
          padding: 6px 8px;
          background: rgb(235 235 235);

          .anticon-ellipsis {
            color: #999;
          }
        }
      }

      .btnBox {
        display: flex;
        align-items: center;

        .btnBoxMainText {
          padding: 12px;

          // font-size: @font-size-base;
          color: #333;
          text-wrap: nowrap;
          cursor: pointer;
          transition: color 0.3s;

          .btnBoxsubText {
            padding-left: 8px;
            font-size: 12px;
          }
        }

        .btn {
          margin-left: 10px;
        }
      }
    }

    .msgAlert {
      padding: 4px 12px;
      overflow: hidden; /* 隐藏溢出内容 */
      font-size: 14px;
      white-space: nowrap; /* 不换行 */
      border: 1px solid #ffe58f;

      ::v-deep(.ant-alert-content) {
        position: relative;
        height: 20px;
        overflow: hidden;
      }

      ::v-deep(.ant-alert-message) {
        position: absolute;
        display: inline-block;
        animation: scroll var(--duration) linear infinite; /* 动画效果，56秒完成一次循环，速度更慢 */
      }
    }

    @keyframes scroll {
      0% {
        right: 0%; /* 开始位置在右侧 */
        transform: translateX(100%);
      }

      100% {
        right: 100%; /* 结束位置在左侧 */
      }
    }

    .boxStr {
      box-sizing: border-box;
      display: flex;
      flex: 1;
      flex-direction: column;
      width: 100%;
      padding: 12px 20px;
      overflow: hidden scroll;

      &::-webkit-scrollbar {
        display: none;
        width: 0 !important;
        height: 0 !important;
        appearance: none;
        background: transparent;
      }
    }
  }
}

.conternBox {
  box-sizing: border-box;
  width: 330px;
  margin-bottom: 10px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 6px;
  box-shadow:
    0 3px 6px -4px #0000001f,
    0 6px 16px #00000014,
    0 9px 28px 8px #0000000d;

  .nameBox {
    box-sizing: border-box;
    display: flex;
    padding: 16px 20px;
    background: #fff;

    .nameImg {
      width: 44px;
      min-width: 44px;
      height: 44px;
      min-height: 44px;
      margin-right: 12px;
      overflow: hidden;

      // background-color: #000;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .nameText {
      .textTop {
        font-size: 14px;
        font-weight: 500;
        line-height: 25px;
        color: #111;
      }

      .textBot {
        display: flex;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        color: #999;

        .label {
          white-space: nowrap;
        }
      }
    }
  }

  .logout {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    padding-left: 24px;
    color: #666;
    cursor: pointer;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    transition: all 0.3s;

    i {
      margin-right: 12px;
    }
  }

  .logout:hover {
    color: #409eff;
    background-color: #eef2fa;
  }

  .editInfoBtn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    padding-left: 24px;
    color: #666;
    cursor: pointer;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    transition: all 0.3s;

    i {
      margin-right: 12px;
    }
  }

  .editInfoBtn:hover {
    color: #409eff;
    background: #eef2fa;
  }
}
</style>

<style>
.ant-menu-dark.ant-menu-submenu > .ant-menu {
  margin-left: -12px;
  background-color: #2a2a48;
}

.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active,
.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover {
  color: #7ebcfc;
  background-color: rgb(64 158 255 / 10%);
}

.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-item,
.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-submenu-title {
  width: 100%;
  margin-inline: 0;
  border-radius: 0;
}

.ant-menu-dark .ant-menu-item-selected {
  color: #7ebcfc;
  background-color: rgb(64 158 255 / 10%);
  border-left: 3px solid #7ebcfc;
}

.ant-alert-message {
  font-size: 12px;
}
</style>
<style lang="scss" scoped>
.header-account {
  border-bottom: 1px solid #eef2fa;
}

.user-type {
  width: 42px;
  height: 42px;
  margin-right: 4px;
  font-size: 18px;
  line-height: 42px;
  color: #fff;
  text-align: center;
  background: linear-gradient(316deg, #1664ff 0%, #409eff 50%);
  border-radius: 8px;
}

.header-user {
  width: 318px;
  background-color: #fff;
  box-shadow: 0 0 8px 0 rgb(0 0 0 / 15%);

  &-avatar {
    width: 44px;
    height: 44px;
    margin-right: 10px;
    border-radius: 50%;
  }

  &-text {
    word-break: break-all;

    .label-text {
      display: inline-block;
      width: 16px;
      text-align: right;
    }
  }

  &-select {
    display: flex;
    align-items: center;
    height: 52px;
    padding-left: 24px;
    color: #666;
    cursor: pointer;
    border-top: 1px solid #f0f0f0;
    transition: all 0.3s;

    &:hover {
      color: #1890ff;
      background-color: #eef2fa;
    }
  }
}

.text-primary {
  color: #409eff;
}
</style>
<style lang="scss">
.header-popover {
  .ant-popover-content {
    .ant-popover-inner {
      padding: 0 !important;
    }
  }
}
</style>

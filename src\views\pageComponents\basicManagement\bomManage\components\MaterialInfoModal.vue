<template>
  <a-modal v-model:open="openModal" title="物料信息" @ok="showModal" centered @cancel="handleCancel" width="1200px">
    <template #footer>
      <div class="flex justify-end">
        <a-button type="primary" @click="handleSubmit">确定</a-button>
        <a-button type="default" @click="handleCancel">取消</a-button>
      </div>
    </template>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="all" tab="全部" />
    </a-tabs>
    <div class="search-form flex gap-8 mb-8">
      <div class="form-input flex w-100% gap-8">
        <a-input class="flex-1" v-model:value="formState.styleCode" allow-clear placeholder="搜索款式编码" />
        <a-input class="flex-1" v-model:value="formState.materialCode" allow-clear placeholder="搜索物料编码" />
        <a-input class="flex-1" v-model:value="formState.materialName" allow-clear placeholder="搜索物料名称" />
        <a-select class="flex-1" v-model:value="formState.materialCategory" show-search placeholder="物料分类" :options="departmentOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.materialGroup" show-search placeholder="物料分组" :options="positionOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.basicUnit" show-search placeholder="基本单位" :options="basicUnitOptions" :filter-option="customFilterOption" />
        <a-input class="flex-1" v-model:value="formState.specification" allow-clear placeholder="搜索规格型号" />
        <a-select class="flex-1" v-model:value="formState.versionNumber" show-search placeholder="版本号" :options="versionNumberOptions" :filter-option="customFilterOption" />
        <a-button class="btn" type="primary" @click="handleSearchMaterial" style="margin-right: 10px">查询</a-button>
        <a-button class="btn" @click="handleReset">重置</a-button>
      </div>
    </div>
    <vxe-table :data="tableData" border stripe ref="tableRef" :sort-config="{ trigger: 'cell', remote: false }" height="400" show-overflow>
      <vxe-column v-if="selectType === 'radio'" type="radio" width="60" align="center"></vxe-column>
      <vxe-column v-else-if="selectType === 'checkbox'" type="checkbox" width="60" align="center"></vxe-column>
      <vxe-column field="styleCode" title="款式编码" width="120" sortable />
      <vxe-column field="materialCode" title="物料编码" width="120" sortable />
      <vxe-column field="materialName" title="物料名称" width="120" />
      <vxe-column field="materialCategory" title="物料分类" width="120" sortable />
      <vxe-column field="materialGroup" title="物料分组" width="120" sortable />
      <vxe-column field="basicUnit" title="基本单位" width="120" sortable />
      <vxe-column field="specification" title="规格型号" width="120" sortable />
      <vxe-column field="productLabel" title="商品标签" width="120" sortable />
      <vxe-column field="versionNumber" title="版本号" width="120" sortable />
    </vxe-table>
    <div class="flex items-center my-8 flex-justify-end">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="tableParams.total"
          show-size-changer
          v-model:current="tableParams.page"
          v-model:pageSize="tableParams.pageSize"
          :page-size-options="pageSizeOptions"
          @change="handleChangePage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ tableParams.total }}</span>
        </span>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { customFilterOption } from '@/utils/index'
import type { VxeTableInstance } from 'vxe-table'
import { message } from 'ant-design-vue'

const emit = defineEmits(['onSelectMaterial'])
// 表单数据
const formState = ref({
  styleCode: '', // 款式编码
  materialCode: '', // 物料编码
  materialName: '', // 物料名称
  materialCategory: undefined, // 物料分类
  materialGroup: undefined, // 物料分组
  basicUnit: undefined, // 基本单位
  specification: '', // 规格型号
  productLabel: '', // 商品标签
  versionNumber: undefined, // 版本号
})
// 全部表格分页参数
const tableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 500,
})
const pageSizeOptions = ['20', '50', '100', '250']

// 物料分类Option
const departmentOptions = ref([])
// 物料分组Option
const positionOptions = ref([])
// 基本单位Option
const basicUnitOptions = ref([])
// 版本号Option
const versionNumberOptions = ref([])

// 表格引用
const tableRef = ref<VxeTableInstance>()

// 表格数据
const tableData = ref([
  {
    styleCode: 'ST001',
    materialCode: 'MT001',
    materialName: '纯棉面料1',
    materialCategory: '原材料',
    materialGroup: '天然纤维1',
    basicUnit: '米1',
    specification: '150cm宽1',
    productLabel: '环保1',
    versionNumber: 'V1.01',
  },
  {
    styleCode: 'ST002',
    materialCode: 'MT002',
    materialName: '纯棉面料2',
    materialCategory: '原材料',
    materialGroup: '天然纤维2',
    basicUnit: '米2',
    specification: '150cm宽2',
    productLabel: '环保2',
    versionNumber: 'V1.02',
  },
  {
    styleCode: 'ST003',
    materialCode: 'MT003',
    materialName: '纯棉面料3',
    materialCategory: '原材料',
    materialGroup: '天然纤维3',
    basicUnit: '米3',
    specification: '150cm宽3',
    productLabel: '环保3',
    versionNumber: 'V1.03',
  },
  {
    styleCode: 'ST004',
    materialCode: 'MT004',
    materialName: '纯棉面料4',
    materialCategory: '原材料',
    materialGroup: '天然纤维4',
    basicUnit: '米4',
    specification: '150cm宽4',
    productLabel: '环保4',
    versionNumber: 'V1.04',
  },
  {
    styleCode: 'ST005',
    materialCode: 'MT005',
    materialName: '纯棉面料5',
    materialCategory: '原材料',
    materialGroup: '天然纤维5',
    basicUnit: '米5',
    specification: '150cm宽5',
    productLabel: '环保5',
    versionNumber: 'V1.05',
  },
  {
    styleCode: 'ST006',
    materialCode: 'MT006',
    materialName: '纯棉面料6',
    materialCategory: '面料6',
    materialGroup: '天然纤维6',
    basicUnit: '米6',
    specification: '150cm宽6',
    productLabel: '环保6',
    versionNumber: 'V1.06',
  },
  {
    styleCode: 'ST007',
    materialCode: 'MT007',
    materialName: '聚酯纤维7',
    materialCategory: '面料7',
    materialGroup: '化学纤维7',
    basicUnit: '米7',
    specification: '120cm宽7',
    productLabel: '防皱7',
    versionNumber: 'V1.17',
  },
  {
    styleCode: 'ST008',
    materialCode: 'MT008',
    materialName: '拉链',
    materialCategory: '辅料',
    materialGroup: '五金配件',
    basicUnit: '条',
    specification: '20cm',
    productLabel: '金属',
    versionNumber: 'V1.0',
  },
  {
    styleCode: 'ST004',
    materialCode: 'MT009',
    materialName: '纽扣',
    materialCategory: '辅料',
    materialGroup: '装饰配件',
    basicUnit: '个',
    specification: '直径15mm',
    productLabel: '塑料',
    versionNumber: 'V1.2',
  },
  {
    styleCode: 'ST005',
    materialCode: 'MT010',
    materialName: '缝纫线',
    materialCategory: '辅料',
    materialGroup: '缝制材料',
    basicUnit: '卷',
    specification: '40支',
    productLabel: '高强度',
    versionNumber: 'V1.0',
  },
])
// 选择类型
const selectType = ref('radio')
// 标签页key值
const activeKey = ref('all')
// 是否显示弹窗
const openModal = ref(false)

// 显示弹窗
const showModal = (type: string = 'radio') => {
  selectType.value = type
  openModal.value = true
  nextTick(() => {
    if (tableRef.value) {
      if (type === 'radio') {
        tableRef.value.clearRadioRow()
      } else if (type === 'checkbox') {
        tableRef.value.clearCheckboxRow()
      }
    }
  })
}

// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}

// 切换分页
const handleChangePage = () => {}
// 确定
const handleSubmit = () => {
  let selectedData

  if (selectType.value === 'radio') {
    // 单选模式
    selectedData = tableRef.value?.getRadioRecord()
    if (!selectedData) {
      message.warning('请选择一条物料数据')
      return
    }
  } else if (selectType.value === 'checkbox') {
    // 多选模式
    selectedData = tableRef.value?.getCheckboxRecords()
    if (!selectedData || selectedData.length === 0) {
      message.warning('请选择至少一条物料数据')
      return
    }
  }

  // 发送选中的物料数据给父组件
  emit('onSelectMaterial', selectedData)
  openModal.value = false
}

// 查询用户
const handleSearchMaterial = () => {
  console.log('查询物料', formState)
}

// 重置表单
const handleReset = () => {
  Object.keys(formState).forEach((key) => {
    if (typeof formState[key] === 'string') {
      formState[key] = ''
    } else {
      formState[key] = undefined
    }
  })
}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>

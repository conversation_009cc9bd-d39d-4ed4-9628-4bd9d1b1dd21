<template>
  <a-drawer title="查看车间" width="650px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" v-bind="formItemLayout">
      <a-form-item label="车间编号" name="code">
        {{ formState.code || '--' }}
      </a-form-item>
      <a-form-item label="车间名称" name="name">
        {{ formState.name || '--' }}
      </a-form-item>
      <a-form-item label="状态" name="status">
        {{ formState.status ? '启用' : '停用' }}
      </a-form-item>
      <a-form-item label="备注" name="remark">
        {{ formState.remark || '--' }}
      </a-form-item>
    </a-form>
    <div class="drawer-title mt-32">其他信息</div>
    <a-form :model="formState" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
      <a-form-item label="创建时间" name="created_at">
        <span>{{ formState.created_at }}</span>
      </a-form-item>
      <a-form-item label="创建人" name="creator">
        {{ formState.creator?.real_name }}
        <span class="user-info">{{ `（ ${formState.creator?.position_name || '--'} | ${formState.creator?.department_name || '--'} ）` }}</span>
      </a-form-item>
      <a-form-item label="最后修改时间" name="updated_at">
        {{ formState.updated_at }}
      </a-form-item>
      <a-form-item label="最后修改人" name="modifier">
        {{ formState.modifier?.real_name }}
        <span class="user-info">{{ `（ ${formState.modifier?.position_name || '--'} | ${formState.modifier?.department_name || '--'} ）` }}</span>
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { viewWorkshop } from '@/servers/workshop'

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    workshop_id: string
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
}

const formState = reactive({
  id: null,
  code: '',
  name: '',
  status: 0,
  remark: '',
  created_at: '',
  creator: { real_name: '', position_name: '', department_name: '' },
  updated_at: '',
  modifier: { real_name: '', position_name: '', department_name: '' },
})

// 关闭drawer
const onClose = () => {
  emits('update:open', false)
}

// 获取车间详情
const getWorkshopDetail = async () => {
  const res = await viewWorkshop(props.initValue.workshop_id)
  Object.assign(formState, res.data)
}

onMounted(() => {
  getWorkshopDetail()
})
</script>

<style lang="scss" scoped>
.user-info {
  color: #8a8a8a;
}
</style>

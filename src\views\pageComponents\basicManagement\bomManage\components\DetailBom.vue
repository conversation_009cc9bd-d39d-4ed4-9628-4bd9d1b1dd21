<template>
  <a-drawer width="1200" @close="handleClose" v-model:open="openDrawer">
    <template #title>
      <span>查看产品BOM</span>
    </template>
    <a-form ref="formRef" :model="formState" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="产品编码" name="productCode">
            <span>11</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品名称" name="productName">
            <span>11</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品版本" name="productVersion">
            <span>11</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" name="remark">
            <span>11</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="状态" name="status">
            <a-switch v-model:checked="formState.status" :checkedValue="1" :unCheckedValue="0" checked-children="启用" un-checked-children="停用"></a-switch>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item name="materialControl">
            <template #label>
              <span>用料阶别管控</span>
            </template>
            <a-checkbox v-model:checked="formState.materialControl"></a-checkbox>
          </a-form-item>
        </a-col>
      </a-row>
      <a-flex justify="space-between" class="mb-8 flex items-center ml-5 mt-40">
        <span class="c-#02a7f0">物料列表</span>
        <span>
          <a-input v-model:value="materialCodeSearch" style="width: 280px" placeholder="输入物料编码搜索" :style="{ width: '200px' }" allow-clear />
        </span>
      </a-flex>
    </a-form>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 表单数据
const formState = ref({
  productCode: '',
  productName: '',
  productVersion: '',
  remark: '',
  status: 1,
  materialControl: false,
})

// 抽屉是否打开
const openDrawer = ref(false)
// 显示抽屉
const showDrawer = () => {
  openDrawer.value = true
}
// 关闭抽屉
const handleClose = () => {
  openDrawer.value = false
}

defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped></style>

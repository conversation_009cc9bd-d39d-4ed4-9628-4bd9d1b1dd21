<template>
  <a-drawer width="1200" @close="handleClose" v-model:open="openDrawer">
    <template #title>
      <span>查看产品BOM</span>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
// 抽屉是否打开
const openDrawer = ref(false)
// 显示抽屉
const showDrawer = () => {
  openDrawer.value = true
}
// 关闭抽屉
const handleClose = () => {
  openDrawer.value = false
}
defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped></style>

<template>
  <a-modal :open="open" title="选择产品信息" width="840px" :maskClosable="false" :footer="null" @cancel="handleCancel">
    <a-tabs v-model:activeKey="activeKey" @change="handleChangeTab">
      <a-tab-pane key="all" tab="全部" />
      <a-tab-pane key="related" tab="已关联" force-render />
    </a-tabs>
    <div class="search-form">
      <div class="form-input">
        <a-input class="w-150" v-model:value="formState.goods_code" allow-clear placeholder="搜索产品编码" />
        <a-input class="w-150" v-model:value="formState.goods_name" allow-clear placeholder="搜索产品名称" />
        <a-select class="w-150" v-model:value="formState.goods_category_id" allow-clear placeholder="商品分类" :options="goodsCategoryOptions" />
        <a-select class="w-150" v-model:value="formState.goods_product_name" allow-clear placeholder="产品分组" :options="goodsProductNameOptions" />
        <a-input class="w-150" v-model:value="formState.spec" allow-clear placeholder="搜索规格型号" />
        <a-input class="w-150" v-model:value="formState.style_code" allow-clear placeholder="搜索款式编码" />
        <a-select class="w-150" v-model:value="formState.goods_label" allow-clear placeholder="商品标签" :options="goodsLabelOptions" />
        <a-select class="w-150" v-model:value="formState.version" allow-clear placeholder="版本号" :options="versionOptions" />
        <div class="w-310 flex justify-end">
          <a-button class="btn" type="primary" @click="handleQueryGoods" style="margin-right: 10px">查询</a-button>
          <a-button class="btn" @click="handleReset">重置</a-button>
        </div>
      </div>
    </div>
    <vxe-table
      :data="activeKey === 'all' ? allGoodsList : relatedGoodsList"
      border
      align="center"
      stripe
      ref="tableRef"
      max-height="500px"
      min-height="0"
      size="mini"
      show-overflow
      :sort-config="{ remote: true }"
      @sort-change="handleSortField"
    >
      <vxe-column field="goods_code" title="产品编码" width="120" sortable />
      <vxe-column field="goods_name" title="产品名称" minWidth="140" sortable />
      <vxe-column field="goods_category_name" title="商品分类" minWidth="140" />
      <vxe-column field="goods_product_name" title="产品分组" minWidth="140" sortable />
      <vxe-column field="spec" title="规格型号" minWidth="160" sortable />
      <vxe-column field="style_code" title="款式编码" minWidth="140" sortable />
      <vxe-column field="goods_label" title="商品标签" minWidth="120" sortable />
      <vxe-column field="version" title="版本号" minWidth="140" sortable />
      <vxe-column title="操作" fixed="right" width="100">
        <template #default="{ row }">
          <a-switch :checked="row.related_status" :checkedValue="1" :unCheckedValue="0" checked-children="关联" un-checked-children="取消" @change="handleSwitch(row)" />
        </template>
      </vxe-column>
    </vxe-table>

    <!-- 全部数据分页 -->
    <div class="flex items-center my-8">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="tableParams.total"
          show-size-changer
          v-model:current="tableParams.page"
          v-model:pageSize="tableParams.pageSize"
          :page-size-options="pageSizeOptions"
          size="small"
          @change="handleChangePage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ tableParams.total }}</span>
        </span>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { getRelateGoodsList, getRelateGoodsOptions, relateGoods } from '@/servers/processFlow'
import { cloneDeep } from '@/utils'

const props = defineProps<{
  open: boolean
  initValue: {
    process_flows_id: number
    process_flows_name: string
  }
}>()

const activeKey = ref('all')
const isAllGoods = computed(() => activeKey.value === 'all')
const queryStatus = computed(() => (isAllGoods.value ? 0 : 1)) // 查询状态：0-全部，1-已关联产品
const tableParams = computed(() => (isAllGoods.value ? allTableParams : relatedTableParams))

/** 切换tab页 */
const handleChangeTab = () => {
  getGoodsList()
}

const goodsCategoryOptions = ref() // 商品分类下拉列表
const goodsLabelOptions = ref() // 商品标签下拉列表
const versionOptions = ref() // 版本号下拉列表
const goodsProductNameOptions = ref() // 版本号下拉列表

const initQueryForm = {
  process_flows_id: props.initValue.process_flows_id,
  goods_code: '',
  goods_name: '',
  goods_category_id: [],
  goods_product_name: [],
  spec: '',
  style_code: '',
  goods_label: null,
  version: null,
}
const formStateMap = reactive({
  all: cloneDeep(initQueryForm),
  related: cloneDeep(initQueryForm),
})
const formState = computed(() => formStateMap[activeKey.value])

/** 表单查询 */
const handleQueryGoods = () => {
  if (isAllGoods.value) {
    allTableParams.page = 1
  } else {
    relatedTableParams.page = 1
  }
  getGoodsList()
}

/** 重置表单查询 */
const handleReset = () => {
  Object.assign(formStateMap[activeKey.value], initQueryForm)
  handleQueryGoods()
}

const tableRef = ref()
const pageSizeOptions = ['20', '50', '100', '250']

const allGoodsList = ref<any>([]) // 全部数据-分页
/** 全部表格分页参数 */
const allTableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})

const relatedGoodsList = ref() // 已选数据-分页
/** 已选表格分页参数 */
const relatedTableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})

/** 全部/已关联-切换分页 */
const handleChangePage = () => {
  getGoodsList()
}

/** 处理排序问题 */
const handleSortField = ({ field, order }) => {
  getGoodsList(field, order)
}

/** 关联操作 */
const handleSwitch = async (row) => {
  const title = row.related_status ? '取消关联产品' : '关联产品'
  const content = row.related_status
    ? `将解除产品${row.goods_name}与工艺流程${props.initValue.process_flows_name}的关联，影响后续生产调用，是否取消关联？`
    : `产品${row.goods_name}将关联至工艺流程${props.initValue.process_flows_name}，是否关联？`
  Modal.confirm({
    title,
    content,
    async onOk() {
      try {
        const params = {
          process_flows_id: props.initValue.process_flows_id,
          material_id: row.id,
          status: row.related_status ? 0 : 1,
        }
        const res = await relateGoods(params)
        message.success(res.message)
        getGoodsList()
      } catch (error) {
        console.error(error)
      }
    },
    onCancel() {},
  })
}

const emits = defineEmits(['update:open'])
const handleCancel = () => {
  emits('update:open', false)
}

/** 获取关联商品列表 */
const getGoodsList = async (field: string = '', order: 'desc' | 'asc' = 'asc') => {
  const formQueryParams = formStateMap[activeKey.value]
  const params = {
    ...formQueryParams,
    field,
    sort: order,
    status: queryStatus.value,
    page: tableParams.value.page,
    pageSize: tableParams.value.pageSize,
  }
  const res = await getRelateGoodsList(params)
  if (activeKey.value === 'all') {
    allGoodsList.value = res.data?.list || []
    allTableParams.total = allGoodsList.value.length
  } else {
    relatedGoodsList.value = res.data?.list || []
    relatedTableParams.total = relatedGoodsList.value.length
  }
}

/** 获取关联商品列表下拉选项 */
const getGoodsOptions = async () => {
  const res = await getRelateGoodsOptions({})
  goodsCategoryOptions.value = res.data?.goods_category || []
  goodsLabelOptions.value = res.data?.goods_label?.map((item) => ({ value: item, label: item })) || []
  versionOptions.value = res.data?.version?.map((item) => ({ value: item, label: item })) || []
  goodsProductNameOptions.value = res.data?.goods_product_name?.map((item) => ({ value: item, label: item }))
}

onMounted(() => {
  getGoodsList()
  getGoodsOptions()
})
</script>

<style lang="scss" scoped>
.page-number {
  color: #409eff;
}

.form-input {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 8px;
}
</style>

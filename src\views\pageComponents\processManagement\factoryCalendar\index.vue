<template>
  <div class="main px-20 py-12">
    <Form v-model:form="formArr" :page-type="PageType.FACTORY_CALENDAR" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.FACTORY_CALENDAR" :isIndex="true" :get-list="getFactoryCalendars">
      <template #right-btn>
        <a-button v-if="btnPermission[PERM_CODE.CREATE]" type="primary" :icon="h(PlusOutlined)" @click="handleUpdateCalendar('add')">新建工厂日历</a-button>
      </template>
      <template #company_name="{ row }">
        <span>{{ row.company?.name || '--' }}</span>
      </template>
      <template #start_week="{ row }">
        <span>{{ WEEK_MAP?.find((item) => item.value === row.start_week)?.label || '--' }}</span>
      </template>
      <template #start_month="{ row }">
        <span>{{ `${row?.start_month}月` || '--' }}</span>
      </template>
      <template #end_month="{ row }">
        <span>{{ `${row?.end_month}月` || '--' }}</span>
      </template>
      <template #is_saturday_work="{ row }">
        <a-switch
          v-if="btnPermission[PERM_CODE.EDIT]"
          v-model:checked="row.is_saturday_work"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="是"
          un-checked-children="否"
          @change="handleSwitch('is_saturday_work', row)"
        />
        <span v-else>{{ row.is_saturday_work ? '是' : '否' }}</span>
      </template>
      <template #is_sunday_work="{ row }">
        <a-switch
          v-if="btnPermission[PERM_CODE.EDIT]"
          v-model:checked="row.is_sunday_work"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="是"
          un-checked-children="否"
          @change="handleSwitch('is_sunday_work', row)"
        />
        <span v-else>{{ row.is_sunday_work ? '是' : '否' }}</span>
      </template>
      <template #is_default="{ row }">
        <a-switch
          v-if="btnPermission[PERM_CODE.EDIT]"
          v-model:checked="row.is_default"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="是"
          un-checked-children="否"
          @change="handleSwitch('is_default', row)"
        />
        <span v-else>{{ row.is_default ? '是' : '否' }}</span>
      </template>
      <template #creator="{ row }">
        <span>{{ row.creator?.real_name || '--' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button v-if="btnPermission[PERM_CODE.VIEW]" @click="handleViewCalendar(row.id)" class="btn">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="btnPermission[PERM_CODE.EDIT]" @click="handleUpdateCalendar('edit', row.id)">编辑</a-menu-item>
                <a-menu-item v-if="btnPermission[PERM_CODE.DELETE]" @click="handleDelete(row.id)">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>
    <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, h, shallowRef, provide } from 'vue'
import { PageType } from '@/common/enum'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getFactoryCalendars, updateCalendarStatus, getSelectOptions } from '@/servers/factoryCalendar'
import Form from '@/components/Form.vue'
import CalendarViewDrawer from './components/CalendarViewDrawer.vue'
import CalendarUpdateDrawer from './components/CalendarUpdateDrawer.vue'
import CalendarDeleteModal from './components/CalendarDeleteModal.vue'
import { WEEK_MAP, PERM_CODE } from './types'

const { btnPermission } = usePermission()
const classOptions = ref()
const timePeriodOptions = ref()
provide('classOptions', classOptions)
provide('timePeriodOptions', timePeriodOptions)

const tableRef = ref()
const search = () => tableRef.value.search()
// 查询表单
const formArr = ref<any[]>([
  {
    label: '请输入日历名称',
    value: null,
    type: 'input',
    key: 'name',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'updated_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])

// component
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  id: null,
})
// 弹窗提交后更新表格数据
const refresh = () => {
  tableRef.value.refresh()
}

// 切换时间
const handleSwitch = async (code: string, row: any) => {
  const params = { [code]: row[code] }
  const res = await updateCalendarStatus(row.id, params)
  message.success(res.message)
  refresh()
}

// 新增/编辑工厂日历
const handleUpdateCalendar = (type: string, id?: string) => {
  component.value = CalendarUpdateDrawer
  open.value = true
  initValue.type = type
  initValue.id = id
}

// 查看工厂日历
const handleViewCalendar = (id: number) => {
  component.value = CalendarViewDrawer
  open.value = true
  initValue.id = id
}

// 删除工厂日历
const handleDelete = (id: number) => {
  component.value = CalendarDeleteModal
  open.value = true
  initValue.id = id
}

// 获取班别及时段下拉列表
const getClassAndTimeOptions = async () => {
  const res = await getSelectOptions()
  classOptions.value = res.data?.class?.map((item) => ({ label: item.key, value: item.id }))
  timePeriodOptions.value = res.data?.time_period?.map((item) => ({ label: item.key, value: item.id }))
}

onMounted(() => {
  search()
  getClassAndTimeOptions()
})
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}
</style>

import { request } from './request'

// 获取公司列表
export const getCompanyList = (data) => request({ url: '/api/businessBase/workshop/getCompany', data })
// 获取左侧选择器下拉选项
export const getLeftQueryOptions = (data) => request({ url: '/api/businessBase/workshop/leftStrucSearch', data })
// 获取车间树形数据
export const getWorkshopTree = (data) => request({ url: '/api/businessBase/workshop/leftStructureColumn', data })
// 获取车间列表
export const getWorkshops = (data) => request({ url: '/api/businessBase/workshop/index', data })
// 新建车间
export const addWorkshop = (data) => request({ url: '/api/businessBase/workshop/store', data })
// 更新车间
export const updateWorkshop = (id, data) => request({ url: `/api/businessBase/workshop/update/${id}`, data })
// 更新车间状态
export const updateWorkshopStatus = (id, data) => request({ url: `/api/businessBase/workshop/updateStatus/${id}`, data })
// 查看车间详情
export const viewWorkshop = (id) => request({ url: `/api/businessBase/workshop/show/${id}` })
// 删除车间
export const deleteWorkshop = (id) => request({ url: `/api/businessBase/workshop/destroy/${id}` })
// 获取列表页筛选栏名称
export const getWorkshopNames = (data) => request({ url: '/api/businessBase/workshop/nameSearchInit', data })

<template>
  <div class="flex flex-col w-230 h-full mr-12">
    <a-select v-model:value="company_id" placeholder="选择公司" :options="companyList" :fieldNames="{ label: 'name', value: 'umc_id' }" class="w-full" @change="handleChangeCompany" />
    <div class="organization-tree">
      <div class="bg-#FAFAFB p-8px">
        <a-select
          class="w-full"
          v-model:value="workshop_id"
          :placeholder="`请输入${PAGE_TYPE_MAP[type].name}名称`"
          :options="leftQueryOptions"
          show-search
          allowClear
          :filter-option="(input, option) => customFilterOption(input, option, 'name')"
          @change="handleQueryWorkshop"
        >
          <template #option="{ id, parent_id, name, parent_name }">
            <div @click="scrollTo(`${parent_id}-${id}`)">
              <div class="truncate">{{ name }}</div>
              <span class="text-#999 truncate text-10">{{ `${parent_name}>${name}` }}</span>
            </div>
          </template>
        </a-select>
      </div>
      <div class="p-12px scroll-bar relative box-border flex-1 overflow-y-auto overflow-x-hidden">
        <a-tree
          class="absolute w-full"
          :fieldNames="{
            title: 'name',
            key: 'uniqueKey',
          }"
          :tree-data="workshop_tree"
          v-model:expandedKeys="expandedKeys"
          :autoExpandParent="autoExpandParent"
          :selectedKeys="selectedKeys"
          @select="handleSelectNode"
          @expand="handleExpand"
        >
          <template #title="item">
            <div class="custom-tree-node">
              <LongTextTooltip :content="item.name" :width="130" />
              <a-dropdown
                v-if="CAN_OPERATE_NODE[type].includes(item.workshop_type)"
                v-model:open="dropdownVisible[item.name]"
                :trigger="['hover']"
                @openChange="(val) => handleDropdownChange(item?.workshop_type, val)"
              >
                <span class="tree-icon" @click.stop>
                  <MoreOutlined />
                </span>
                <template #overlay>
                  <a-menu @click="({ key: menuKey }) => handleMenuClick(menuKey, item.uniqueKey, item.id)">
                    <a-menu-item v-for="item in menuList" :key="item.key">{{ item.title }}</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
        </a-tree>
      </div>

      <div class="h-30 flex tree-bottom-btn">
        <a-button v-if="isExpanded" class="flex-1 m-0 h-full" type="link" @click="collapseAll">全部收起</a-button>
        <a-button v-else class="flex-1 m-0 h-full" type="link" @click="expandAll">全部展开</a-button>
        <a-button v-if="canCreateItem" class="flex-1 m-0 h-full last-btn" type="link" :icon="h(PlusOutlined)" @click="handleAddItem(BOTTOM_ADD_ITEM[type].menuKey)">
          {{ BOTTOM_ADD_ITEM[type].menuName }}
        </a-button>
      </div>
    </div>
  </div>
  <!-- 动态操作面板 -->
  <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
</template>

<script lang="ts" setup>
import { onMounted, h, computed } from 'vue'
import { PlusOutlined, MoreOutlined } from '@ant-design/icons-vue'
import { getCompanyList, getWorkshopTree, getLeftQueryOptions } from '@/servers/workshop'
import { getWorkshopAreaTree } from '@/servers/workshopArea'
import { getWorkshopCenterTree } from '@/servers/workshopCenter'
import { cloneDeep, customFilterOption } from '@/utils'
import { useTreeExpand } from '@/utils/useTreeExpand'
import LongTextTooltip from '@/components/LongTextTooltip.vue'
import { MENU_MAP, BOTTOM_ADD_ITEM, WORKSHOP_TYPE_MAP, CAN_EDIT_BOTTOM_BTN, ModuleType, ActionType, ModuleActionType, PAGE_TYPE_MAP, CAN_OPERATE_NODE } from './types'
import { getPagePermissionId, addTreeKeys, registerWorkshopActions, getActionHandler, treeAllKeys } from './utils'

const props = defineProps<{
  type: ModuleType
}>()

const emits = defineEmits(['update'])
const selectedKeys = ref<Array<string>>([])
const selectedNode = ref()
const canCreateItem = ref(false) // 根据当前选中节点workshop_type判断底部按钮是否可操作

// 公司
const company_id = ref()
const companyList = ref()
const handleChangeCompany = () => {
  localStorage.setItem('tree_company', company_id.value)
}

// 车间
const workshop_id = ref()
const leftQueryOptions = ref() // 左侧生产实体下拉选项

// 车间树形数据
const workshop_tree = ref()

const { init, expandedKeys, autoExpandParent, expandAll, collapseAll, handleExpand } = useTreeExpand(workshop_tree.value, {
  keyField: 'uniqueKey', // 自定义key字段
  defaultExpandAll: true,
})

const menuList = ref()
const isExpanded = computed(() => !!expandedKeys.value?.length) // 树节点是否为展开状态（全部展开/收起）

/** components组件，更多操作抽屉弹窗 */
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  id: null,
  company_id: '',
  workshop_id: '',
})
const refresh = () => {
  getTreeData() // 更新树形数据
  emits('update', 'tree') // 更新父组件列表数据
}

/** 选择树节点 */
const handleSelectNode = (newSelectedKeys, { node }) => {
  selectedKeys.value = newSelectedKeys
  selectedNode.value = newSelectedKeys.length > 0 ? node : []
  // 未选中节点时，默认无法添加操作
  canCreateItem.value = newSelectedKeys.length > 0 ? CAN_EDIT_BOTTOM_BTN[props.type] === node.workshop_type : false
}

/** 锚点到选中点 */
const scrollTo = (key: string) => {
  expandAll()
  // 工作中心属于第三层级，uniqueKey包含了祖父节点id，获取比较麻烦
  // 可以根据一个中心只能归属于一个区域，一个区域归属于一个车间的特性，即区域-工作中心的key值唯一，直接查找该节点的uniqueKey
  if (props.type === 'workcenter') {
    const uniqueKey = treeAllKeys?.find((item) => item.includes(`-${key}`)) || ''
    selectedKeys.value = [uniqueKey]
  } else {
    selectedKeys.value = [key]
  }
  nextTick(() => {
    const target = document.getElementsByClassName('ant-tree-treenode-selected')[0]
    target?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  })
}

/** 新建车间/区域/工作中心 */
const handleAddItem = (actionKey: ActionType) => {
  const item = getActionHandler(`${actionKey}-${props.type}`)
  nextTick(() => {
    component.value = item?.component
    initValue.company_id = company_id.value // 必传
    initValue.type = actionKey
    open.value = true
  })
}

// 控制每个节点的下拉框显示状态
const dropdownVisible = ref({})

/** 点击图标时控制下拉框 */
const handleDropdownChange = (workshop_type: number = 0, visible) => {
  let key = WORKSHOP_TYPE_MAP[workshop_type]
  if (props.type !== 'workshop' && workshop_type === 1) {
    key = 'area_workshop'
  }
  menuList.value = cloneDeep(MENU_MAP[key]) || []
  dropdownVisible.value = { ...dropdownVisible.value, key: visible }
}

/**
 * 处理菜单点击
 * @param menuKey 菜单key值，如：add-workshop
 * @param uniqueKey 当前节点的唯一键，可获取当前节点及其父节点的id值
 * @param id 当前节点的id值
 */
const handleMenuClick = (menuKey: ModuleActionType, uniqueKey: string, id?: number) => {
  dropdownVisible.value[uniqueKey] = false // 选中菜单子项后，需要关闭下拉菜单
  const item = getActionHandler(menuKey)
  if (!item?.component) return
  const parent_id = uniqueKey?.split('-')?.[0]
  const [actionType, pageType] = menuKey.split('-') // actionType：操作类型；pageType: 页面类型

  // 根据页面类型设置不同参数
  const setPageSpecificParams = () => {
    switch (pageType) {
      case 'workshop':
        initValue.workshop_id = id // 编辑/查看/删除需要
        break
      case 'area':
        if (actionType === 'add') {
          // 新建
          initValue.workshop_id = id
        } else {
          // 编辑/查看/删除
          initValue.workshop_id = parent_id
          initValue.workshop_area_id = id
        }
        break
      case 'workcenter':
        // 工作中心只有新建操作
        initValue.workshop_id = parent_id
        initValue.workshop_area_id = id
        break
      default:
        break
    }
  }
  nextTick(() => {
    component.value = item?.component
    initValue.company_id = company_id.value // 每个页面必传company_id
    initValue.type = actionType // 页面操作类型：add/view/edit/delete
    setPageSpecificParams()
    open.value = true
  })
}

/** 获取公司列表 */
const getCompanyData = async () => {
  const pageIndexCode = PAGE_TYPE_MAP[props.type].pageIndexCode
  const permission_id = getPagePermissionId(pageIndexCode) // 权限id
  const res = await getCompanyList({ permission_id })
  companyList.value = res.data?.list || []
  company_id.value = companyList.value?.find((item) => item.default === 1)?.umc_id
  localStorage.setItem('tree_company', company_id.value)
  nextTick(() => {
    getTreeData() // 获取树形结构数据
  })
}

/** 根据车间查询具体树结构数据 */
const handleQueryWorkshop = (name: string) => {
  workshop_id.value = name
  selectedKeys.value = [name]
}

/** 获取左侧选择器下拉选项 */
const getLeftQueryOptionList = async () => {
  const params = {
    type: props.type,
    company_id: company_id.value,
  }
  const res = await getLeftQueryOptions(params)
  leftQueryOptions.value = res.data || []
}

/** 获取树形数据 */
const getTreeData = async () => {
  switch (props.type) {
    case 'workshop':
      await getWorkshopTreeData()
      break
    case 'area':
      await getAreaTreeData()
      break
    case 'workcenter':
      await getCenterTreeData()
      break
    default:
      break
  }
  // selectedKeys：若原先有选中值，则保持原选中值，没有则默认树节点第一项；selectedNode同理
  selectedKeys.value = selectedKeys.value.length ? selectedKeys.value : [workshop_tree.value?.[0]?.uniqueKey]
  selectedNode.value = selectedNode.value || workshop_tree.value?.[0]
  // canCreateItem：判断当前选中节点是否能进行新增操作
  canCreateItem.value = CAN_EDIT_BOTTOM_BTN[props.type] === selectedNode.value?.workshop_type
  init(workshop_tree.value) // 更新树结构数据
  getLeftQueryOptionList() // 更新树结构数据的同时，需要同时更新左侧查询选择器下拉选项
}

/** 车间-获取树形数据 */
const getWorkshopTreeData = async (name?: string) => {
  const res = await getWorkshopTree({ company_id: company_id.value, name })
  workshop_tree.value = [{ id: res.data.umc_id, name: res.data.name, workshop_type: 0, children: res.data?.children }]
  workshop_tree.value = addTreeKeys(cloneDeep([...workshop_tree.value]))
}

/** 区域-获取树形数据 */
const getAreaTreeData = async (name?: string) => {
  const res = await getWorkshopAreaTree({ company_id: company_id.value, name })
  workshop_tree.value = addTreeKeys(cloneDeep([...res.data]))
}

/** 工作中心-获取树形数据 */
const getCenterTreeData = async (name?: string) => {
  const res = await getWorkshopCenterTree({ company_id: company_id.value, name })
  workshop_tree.value = addTreeKeys(cloneDeep([...res.data]))
}

onMounted(() => {
  getCompanyData()
  registerWorkshopActions()
})

defineExpose({
  company_id,
  workshop_id,
  canCreateItem,
  selectedNode,
  getTreeData,
})
</script>

<style lang="scss" scoped>
.organization-tree {
  @apply mt-8px b-#D9D9D9 b-solid b-1px flex flex-1 flex-col;

  :deep(.ant-tree-treenode) {
    display: flex;
    align-items: center;
    width: 204px;
    height: 32px;
    color: #666;

    &:hover {
      color: #409eff;
      background-color: #eef2fa;

      .action-icons {
        opacity: 1;
      }
    }

    .ant-tree-node-content-wrapper {
      display: inline-block;
      flex: 1;

      &:hover {
        background-color: #eef2fa;
      }
    }
  }

  :deep(.ant-tree-switcher) {
    line-height: 32px;
  }

  :deep(.ant-tree-treenode-selected) {
    color: #409eff;
    background-color: #eef2fa;
  }

  :deep(.ant-tree-node-selected) {
    background-color: #eef2fa;
  }

  :deep(.anticon-caret-down) {
    color: #c0c0c0;
  }

  :deep(.ant-tree .ant-tree-node-content-wrapper) {
    padding: 0;
  }

  :deep(.action-icons) {
    opacity: 0;
    transition: opacity 0.2s;
  }

  // 使用 SCSS 循环生成15级缩进样式
  @for $i from 1 through 10 {
    :deep(.level-#{$i}) {
      .ant-tree-indent {
        width: $i * 8px;
      }
    }
  }
}
/* 自定义节点样式 */
.custom-tree-node {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}
/* 操作图标样式 */
.tree-icon {
  padding: 0;
  color: #666;
  cursor: pointer;
}

.tree-icon:hover {
  color: #1890ff;
}

.scroll-bar {
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgb(0 0 0 / 30%);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.tree-bottom-btn {
  border-top: 1px #d3d3d3 solid;

  :deep(.ant-btn) {
    border-radius: 0;
  }

  .last-btn {
    border-left: 1px #d3d3d3 solid;
  }
}

:deep(.ant-tree .ant-tree-indent-unit) {
  width: 14px;
}

:deep(.ant-tree .ant-tree-switcher) {
  width: 14px;
}
</style>

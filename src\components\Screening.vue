<template>
  <a-drawer v-model:open="isScreening" width="380px" title="筛选分类" placement="right" :maskClosable="false">
    <div class="screeningBox">
      <div class="box">
        <div class="titleBox">
          <div class="text">不显示</div>
          <div class="text">显示</div>
        </div>
        <div class="contBox">
          <div class="list1Box" ref="screeningList1Ref" :id="`invisibleDrap${time}`">
            <div class="invisibleItem" v-for="item in invisibleList" :key="item.label" v-show="item.listshow != 1">
              {{ item.label }}
            </div>
          </div>
          <div class="list2Box" ref="screeningList2Ref" :id="`visibleDrap${time}`">
            <div class="visibleItem" v-for="item in visibleList" :key="item.label" v-show="item.listshow != 1">
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>
      <div class="btnBox">
        <a-button type="primary" @click="tapScreeningConfirmed">确定</a-button>
        <a-button type="primary" @click="tapCloture" style="margin-left: 0.52vw">取消</a-button>
      </div>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import Sortable from 'sortablejs'

const emit = defineEmits(['screeningConfirmed'])
const isScreening = ref(false)
const visibleList: any = ref([])
const invisibleList: any = ref([])
const formArr = ref([] as any)
const time = ref(null as any)
const tapCloture = () => {
  isScreening.value = false
}
const init = (obj: any = {}) => {
  time.value = Date.now()
  formArr.value = obj.formArr
  isScreening.value = true
  visibleList.value = [...formArr.value].filter((e: any) => e.isShow)
  invisibleList.value = [...formArr.value].filter((e: any) => !e.isShow)
  setTimeout(() => {
    onDrop()
  }, 0)
}

// 主要的拖拽方法
const onDrop = () => {
  new Sortable(document.getElementById(`visibleDrap${time.value}`), {
    animation: 300,
    handle: '.visibleItem',
    delay: 10,
    group: 'shared',
    // draggable: '.screeningItem',
    onEnd(evt) {
      // 获取拖动后的排序
      const itemToAdd = visibleList.value.splice(evt.oldIndex, 1)[0]
      if (evt.to.id === `invisibleDrap${time.value}`) {
        const itemNew = { ...itemToAdd, isShow: false } // 移动过后记得修改的值isShow
        invisibleList.value.splice(evt.newIndex, 0, itemNew)
      } else {
        visibleList.value.splice(evt.newIndex, 0, itemToAdd)
      }
    },
  })
  new Sortable(document.getElementById(`invisibleDrap${time.value}`), {
    animation: 300,
    handle: '.invisibleItem',
    delay: 10,
    group: 'shared',
    // draggable: '.screeningItem',
    onEnd(evt) {
      const itemToAdd = invisibleList.value.splice(evt.oldIndex, 1)[0]
      if (evt.to.id === `visibleDrap${time.value}`) {
        const itemNew = { ...itemToAdd, isShow: true } // 移动过后记得修改的值isShow
        visibleList.value.splice(evt.newIndex, 0, itemNew)
      } else {
        invisibleList.value.splice(evt.newIndex, 0, itemToAdd)
      }
    },
  })
}
// 确认筛选的分类展示
const tapScreeningConfirmed = () => {
  formArr.value = [...visibleList.value, ...invisibleList.value]
  isScreening.value = false
  emit('screeningConfirmed', formArr.value)
}

defineExpose({
  init,
})
</script>
<style lang="scss" scoped>
.screeningBox {
  .box {
    .titleBox {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;
      margin-bottom: 20px;
      font-size: 20px;
      font-weight: bold;
      color: #000;
      border-bottom: 1px solid #ccc;

      .text {
        width: 200px;
        text-align: center;
      }
    }

    .contBox {
      display: flex;
      justify-content: space-between;

      .list1Box,
      .list2Box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 200px;
        min-height: 500px;

        .invisibleItem,
        .visibleItem {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 150px;
          padding: 2px;
          margin-bottom: 8px;
          font-size: 14px;
          color: #fff;
          cursor: pointer;
          user-select: none;
          background: #409eff;
          border-radius: 10px;
        }

        .invisibleItem {
          color: #000;
          background: #fff;
          border: 1px solid #ccc;
        }

        .visibleItem {
        }
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>

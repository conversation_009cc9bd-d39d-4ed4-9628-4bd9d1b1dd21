// 公共接口
import { request } from './request'
// 获取列表动态字段（列名、字段、排序、固定类型）
export const GetTableConfig = (data) => {
  return request({ url: '/api/System/Common/GetViewFiledMapsV2', data })
}
export const SetTableConfig = (data) => {
  return request({ url: '/api/System/Common/SaveViewFiledMapsV2', data })
}
// 获取所有枚举
export const GetEnum = () => {
  return request({ url: '/api/System/Common/GetEnum' })
}
// 上传文件
export const FileUpload = (data) => {
  return request({ url: '/api/File/Upload', data, isFormData: true })
}
// 获取文件预览
export const FilePreview = (data) => {
  return request({ url: '/api/File/Preview', data }, 'GET')
}

export const GetFileUrl = (data) => {
  return request({ url: '/api/File/GetFileUrl', data }, 'GET')
}

export const Info = () => {
  return request({ url: '/api/Info' }, 'GET')
}
// 获取水印信息
export const GetSystemWatermark = (data) => {
  return request({ url: '/api/System/Common/GetSystemWatermark', data })
}

// 获取快捷查询信息
export const GetQuickQuery = (data) => {
  return request({ url: '/api/System/Common/GetViewQuickQuery', data })
}

// 保存快捷查询信息
export const SaveQuickQuery = (data) => {
  return request({ url: '/api/System/Common/SaveViewQuickQuery', data })
}
// 用户日志
export const GetUserLog = (data) => {
  return request({ url: '/api/System/BusinessLog/GetUserLog', data })
}

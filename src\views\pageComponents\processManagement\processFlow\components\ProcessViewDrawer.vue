<template>
  <a-drawer title="查看工艺流程" :width="720" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" layout="inline" class="form-container" v-bind="formItemLayout">
      <a-col :span="12">
        <a-form-item label="工艺编码" name="craft_code" :rules="[{ required: true, message: '请输入工艺编码' }]">
          {{ formState.craft_code }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="工艺名称" name="craft_name" :rules="[{ required: true, message: '请输入工艺名称' }]">
          {{ formState.craft_name }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="工艺类别" name="craft_category_key" :rules="[{ required: true, message: '请选择工艺类别' }]">
          {{ formState.craft_category_key }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="生产阶别" name="stage_key" :rules="[{ required: true, message: '请选择生产阶别' }]">
          {{ formState.stage_key }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="前置工艺" name="pre_craft_name">
          {{ formState.pre_craft_name || '--' }}
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item label="状态" name="status" v-bind="switchFormLayout" :rules="[{ required: true, message: '请选择状态' }]">
          {{ formState.status ? '启用' : '停用' }}
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item label="是否默认" name="default" v-bind="switchFormLayout" :rules="[{ required: true, message: '请选择是否默认' }]">
          {{ formState.default ? '是' : '否' }}
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" name="remark" :label-col="{ span: 2 }" :wrapper-col="{ span: 21 }" class="remark-item">
          {{ formState.remark || '--' }}
        </a-form-item>
      </a-col>
    </a-form>

    <div class="box-border mb-8 mx-18">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="flowInfo" tab="流程信息">
          <p class="text-[#409eff] mb-8">工艺流程</p>
          <vxe-table :data="flowTableData" border align="center" stripe ref="flowTableRef" max-height="320px" min-height="0" class="w-500" size="mini" show-overflow>
            <vxe-column field="sequence" title="顺序" width="60" />
            <vxe-column field="process_name" title="工序" width="220" />
            <vxe-column title="操作" fixed="right" width="220">
              <template #default="{ row }">
                <a-button type="link" @click="handleViewProcess(row)">查看</a-button>
              </template>
            </vxe-column>
          </vxe-table>
        </a-tab-pane>
        <a-tab-pane key="relateGoods" tab="关联商品">
          <vxe-table :data="goodsTableData" border align="center" stripe ref="goodsTableRef" max-height="320px" min-height="0" size="mini" show-overflow>
            <vxe-column field="goods_code" title="物料编码" width="140" />
            <vxe-column field="goods_name" title="物料名称" width="180" />
            <vxe-column field="goods_category_name" title="商品分类" width="140" />
            <vxe-column field="spec" title="规格型号" width="200" />
            <vxe-column field="style_code" title="款式编码" width="120" />
            <vxe-column field="texture" title="材质" width="140" />
            <vxe-column field="default_vendor" title="默认供应商" width="180" />
          </vxe-table>
        </a-tab-pane>
      </a-tabs>
    </div>

    <div class="drawer-title mt-32">其他信息</div>
    <a-form :model="formState" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
      <a-form-item label="创建时间" name="created_at">
        <span>{{ formState.created_at }}</span>
      </a-form-item>
      <a-form-item label="创建人" name="creator_user">
        {{ formState.creator?.real_name }}
        <span class="text-[#8a8a8a]">{{ `（ ${formState.creator?.position_name || '--'} | ${formState.creator?.department_name || '--'} ）` }}</span>
      </a-form-item>
      <a-form-item label="最后修改时间" name="updated_at">
        {{ formState.updated_at }}
      </a-form-item>
      <a-form-item label="最后修改人" name="modifier_user">
        {{ formState.modifier?.real_name }}
        <span class="text-[#8a8a8a]">{{ `（ ${formState.modifier?.position_name || '--'} | ${formState.modifier?.department_name || '--'} ）` }}</span>
      </a-form-item>
    </a-form>
  </a-drawer>

  <process-panel v-if="openModal" v-model:openModal="openModal" type="view" :currentProcess="currentProcess" />
</template>

<script lang="ts" setup>
import { VxeTableInstance } from 'vxe-table'
import { viewProcedure } from '@/servers/processFlow'
import ProcessPanel from './ProcessPanelModal.vue'

const flowTableRef = ref<VxeTableInstance<any>>()

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    process_flows_id: number
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const activeKey = ref('flowInfo')
const flowTableData = ref<any>([])
const goodsTableData = ref()

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
}
const switchFormLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 10 },
}
const formState = reactive({
  id: '',
  craft_code: '',
  craft_name: '',
  craft_category_key: '',
  stage_key: '',
  pre_craft_name: '',
  status: false,
  default: false,
  remark: '',
  created_at: '',
  creator: { real_name: '', position_name: '', department_name: '' },
  updated_at: '',
  modifier: { real_name: '', position_name: '', department_name: '' },
})

// 编辑工序
const openModal = ref(false) // 属性面板Modal
const currentProcess = ref() // 当前选择的工序
const handleViewProcess = (row) => {
  openModal.value = true
  currentProcess.value = row
}

// 关闭drawer
const onClose = () => {
  formRef.value.resetFields()
  emits('update:open', false)
}

const getProcedureDetail = async () => {
  const res = await viewProcedure({ id: props.initValue.process_flows_id })
  Object.assign(formState, res.data)
  flowTableData.value = res.data?.flow
  goodsTableData.value = res.data?.material
}

onMounted(() => {
  getProcedureDetail()
})
</script>

<style lang="scss" scoped>
.form-container {
  margin-bottom: 18px;

  :deep(.ant-col) {
    margin-bottom: 10px;
  }
}

.remark-item {
  :deep(.ant-form-item-label) {
    margin-left: 14px;
  }
}
</style>

<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.BOM_MANAGE" @search="search" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.PROCESS_FLOW" :isIndex="true" :get-list="Company">
      <template #right-btn>
        <a-button type="primary" @click="addBom" :icon="h(PlusOutlined)">新建产品BOM</a-button>
      </template>
      <template #operate="{ row }">
        <a-button @click="detailBom(row)" class="mr-5px">查看</a-button>
        <a-button @click="editBom(row)" class="mr-5px">编辑</a-button>
        <a-button @click="deleteBom(row.id)">删除</a-button>
      </template>
      <template #status="{ row }">
        <a-switch :checked="row.status === 1" checked-children="启用" un-checked-children="停用" @change="() => handleSwitch(row)"></a-switch>
      </template>
    </BaseTable>
    <AddBom ref="addBomRef" />
    <DetailBom ref="detailBomRef" />
    <EditBom ref="editBomRef" />
    <DeleteBom ref="deleteBomRef" />
    <EnableBom ref="enableBomRef" @success="search" />
  </div>
</template>
<script lang="ts" setup>
import { ref, h } from 'vue'
import { PageType } from '@/common/enum'
import Form from '@/components/Form.vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Company } from '@/servers/0rganization'
import AddBom from './components/AddBom.vue'
import DetailBom from './components/DetailBom.vue'
import EditBom from './components/EditBom.vue'
import DeleteBom from './components/DeleteBom.vue'
import EnableBom from './components/EnableBom.vue'

const formArr = ref<any[]>([
  {
    label: '搜索款式编码',
    value: null,
    type: 'inputDlg',
    key: 'goods_code',
  },
  {
    label: '搜索产品编码',
    value: null,
    type: 'inputDlg',
    key: 'product_code',
  },
  {
    label: '搜索产品名称',
    value: null,
    type: 'input',
    key: 'product_name',
  },
  {
    label: '请输入产品规格',
    value: null,
    type: 'input',
    key: 'product_spec',
  },
  {
    label: '请输入产品版本',
    value: null,
    type: 'input',
    key: 'product_version',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    key: 'status',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'modify_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const tableRef = ref()
const search = () => tableRef.value.search()

/** 新建BOM ref */
const addBomRef = ref()
/** 查看BOM ref */
const detailBomRef = ref()
/** 编辑BOM ref */
const editBomRef = ref()
/** 删除BOM ref */
const deleteBomRef = ref()
/** 启用停用BOM ref */
const enableBomRef = ref()
/** 新建产品BOM */
const addBom = () => {
  addBomRef.value.showDrawer()
}
/** 查看产品BOM */
const detailBom = (id) => {
  detailBomRef.value.showDrawer(id)
}
/** 编辑产品BOM */
const editBom = (id) => {
  editBomRef.value.showDrawer(id)
}
/** 删除产品BOM */
const deleteBom = (id) => {
  deleteBomRef.value.showModal(id)
}
/** 启用停用BOM */
const handleSwitch = (row) => {
  enableBomRef.value.showModal(row.id)
}
</script>
<style lang="scss" scoped></style>

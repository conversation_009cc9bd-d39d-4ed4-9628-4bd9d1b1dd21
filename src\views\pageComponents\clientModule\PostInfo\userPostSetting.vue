<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.USERPOST_SETTING" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.USERPOST_SETTING" :get-list="GetUserList" :isIndex="true" :formFormat="formFormat" :isCheckbox="true">
      <template #left-btn>
        <a-button type="primary" @click="batchSwitch(true)">批量授权</a-button>
        <a-button type="primary" @click="batchSwitch(false)">批量取消</a-button>
      </template>
      <template #status="{ row }">
        <span>{{ statusMaps[row.status] }}</span>
      </template>
      <template #enterprise="{ row }">
        <span>{{ row?.enterprise?.name || '/' }}</span>
      </template>
      <template #leader="{ row }">
        <span>{{ row?.leader?.real_name || '/' }}</span>
      </template>
      <template #operate="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.is_auth]" checked-children="授权" un-checked-children="取消" />
      </template>
    </BaseTable>

    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { GetUserList, AssignUserPosition, Select } from '@/servers/0rganization'

const router = useRouter()
// const { btnPermission } = usePermission()
const listId: any = ref(null)
const position_name: any = ref('')
const formFormat = (data) => {
  return {
    ...data,
    position_id: listId.value,
  }
}

const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索用户工号',
    value: null,
    type: 'input',
    key: 'work_num',
  },
  {
    label: '请选择公司',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'company_id',
    onChange: (val) => {
      formArr.value.forEach((item) => {
        if (item.key === 'department_id') {
          item.value = null
          const arr = departCache.value.filter((v) => v.sub_company_id === val)
          item.selectArr = arr.map((v) => {
            return {
              label: v.name,
              value: v.id,
            }
          })
        }
      })
    },
  },
  {
    label: '请选择部门',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'department_id',
  },
])
const statusMaps = ref({
  0: '停用',
  1: '启用',
  2: '离职',
})
const departCache = ref([])
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.USERPOST_SETTING) {
    const arr: any[] = []
    obj.USERPOST_SETTING.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  listId.value = Number(router.currentRoute.value.params.id)
  position_name.value = router.currentRoute.value.params.name
  getOptions()
  search()
  initScreening()
})

const getOptions = () => {
  Select({ type: 2 }).then((res) => {
    formArr.value.forEach((item) => {
      if (item.key === 'company_id') {
        item.selectArr = res.data.map((v) => {
          return {
            label: v.name,
            value: v.id,
          }
        })
      }
    })
  })
  Select({ type: 3 }).then((res) => {
    departCache.value = res.data
  })
}

const tapSwitch = (e, row) => {
  visibleData.isShow = true
  visibleData.title = '授权'
  visibleData.content = `是否确认${row.status ? '授权' : '取消授权'}`
  visibleData.confirmBtnText = '确认'
  visibleData.okType = 'danger'
  visibleData.isCancelBtn = true
  visibleData.okFn = () => {
    assignUserPosition({ account_ids: [row.account_id], status: e ? 1 : 0, position_name: position_name.value, position_id: listId.value })
  }
}

const assignUserPosition = (obj) => {
  AssignUserPosition(obj).then((res) => {
    if (!res.success) return message.error(res.message)
    message.success(res.message)
    tableRef.value.search()
    visibleData.isShow = false
  })
}

const batchSwitch = (type) => {
  if (tableRef.value.checkItemsArr.length === 0) {
    message.info('请勾选需要操作的数据！')
    return
  }
  visibleData.isShow = true
  visibleData.title = '授权'
  visibleData.content = `是否确认${type ? '授权' : '取消授权'}该用户？`
  visibleData.confirmBtnText = '确认'
  visibleData.okType = 'danger'
  visibleData.isCancelBtn = true
  visibleData.okFn = () => {
    const account_ids = tableRef.value.checkItemsArr.map((x) => x.account_id)
    AssignUserPosition({ account_ids, position_id: listId.value, position_name: position_name.value, status: type ? 1 : 0 })
      .then((res) => {
        if (res.success) {
          message.success(res.message)
          visibleData.isShow = false
          tableRef.value.search()
        }
      })
      .catch((error) => {
        visibleData.isShow = false
        message.error(error)
      })
  }
}

const tableRef = ref()
const search = () => tableRef.value.search()
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

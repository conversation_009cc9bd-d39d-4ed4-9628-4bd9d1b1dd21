<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.DICTIONARY_LIST" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.DICTIONARY_LIST" :get-list="GetDictList" :isIndex="true">
      <template #right-btn>
        <a-button type="primary" @click="tapAdd('add')" :icon="h(PlusOutlined)" v-if="btnPermission[230001]">新建字典</a-button>
      </template>

      <template #is_default="{ row }">
        <span class="text-blue">{{ row.is_default ? '是' : '否' }}</span>
      </template>
      <template #status="{ row }">
        <a-switch
          class="btn"
          @click="tapSwitch($event, row)"
          v-model:checked="[false, true][row.status]"
          checked-children="启用"
          un-checked-children="停用"
          :disabled="!btnPermission[230005] || row.is_default == 1"
        />
      </template>
      <template #company="{ row }">
        <span>{{ row?.company?.name || '系统默认' }}</span>
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row, rowIndex }">
        <div class="btnBox">
          <a-button @click="detail(row, rowIndex)" class="btn" v-if="btnPermission[230003]">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('setting', row)">配置字典</a-menu-item>
                <a-menu-item @click="tapAdd('compiler', row, rowIndex)" :disabled="row.is_default == 1" v-if="btnPermission[230002]">编辑</a-menu-item>
                <a-menu-item @click="tapAdd('removes', row)" :disabled="row.is_default == 1" v-if="btnPermission[230004]">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="520"
      :title="roleModuleType == 'add' ? '新建字典' : '编辑字典'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="序号" name="rowIndex" v-if="roleModuleType == 'compiler'">
          <span>{{ rowIdx }}</span>
        </a-form-item>
        <!-- <a-form-item label="组织机构" name="company">
          <a-input v-model:value="editForm.company" placeholder="请输入" readonly />
        </a-form-item> -->
        <a-form-item label="字典编码" name="code">
          <a-input v-model:value.trim="editForm.code" placeholder="请输入" :maxlength="30" />
        </a-form-item>
        <a-form-item label="字典名称" name="name">
          <a-input v-model:value.trim="editForm.name" placeholder="请输入" :maxlength="30" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-input v-model:value="editForm.remark" placeholder="请输入" :maxlength="200" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.status" placeholder="请选择" :options="statusArr"></a-select>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { h, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Add, Delete, GetDictList, Update, ChangeStatus } from '@/servers/dictionaryList'
import { validateStr, validCode } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { useRouter } from 'vue-router'
import DetailDrawer from './components/DetailDrawer.vue'

const router = useRouter()
const { btnPermission } = usePermission()

const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  code: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 30),
      message: '输入内容不可超过30字符',
    },
    {
      validator: (_rule, value) => validCode(_rule, value),
      message: '编码不能出现中文',
    },
  ],
  name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 30),
      message: '输入内容不可超过30字符',
    },
  ],
  status: [{ required: true, message: '请选择状态', trigger: ['change', 'blur'] }],
}
const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索字典编码',
    value: null,
    type: 'input',
    key: 'code',
  },
  {
    label: '请输入字典名称',
    value: null,
    type: 'input',
    key: 'name',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '停用', value: 0 },
    ],
    key: 'status',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const statusArr = ref([
  { label: '启用', value: 1 },
  { label: '停用', value: 0 },
])
const rowIdx = ref(0)
const editForm = reactive({
  id: null,
  // organization: '',
  code: '',
  name: '',
  remark: '',
  status: null,
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.DICTIONARY_LIST) {
    const arr: any[] = []
    obj.DICTIONARY_LIST.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  search()
  initScreening()
})

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapAdd = (type: string, row: any = '', rowIndex: number = 0) => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      // editForm.organization = ''
      editForm.id = null
      editForm.code = ''
      editForm.name = ''
      editForm.remark = ''
      editForm.status = null
      break
    case 'compiler':
      rowIdx.value = rowIndex + 1
      editForm.id = row.id
      // editForm.organization = row.organization
      editForm.code = row.code
      editForm.name = row.name
      editForm.remark = row.remark
      editForm.status = row.status
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除字典'
      visibleData.content = `是否确认删除字典？删除前，请先移除关联该字典的所有数据，否则会操作失败！`
      visibleData.confirmBtnText = '确认删除'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    case 'setting':
      openConfigList(row)
      break
    default:
      break
  }
}

const tapSwitch = (e, row) => {
  visibleData.isShow = true
  visibleData.title = '字典状态'
  visibleData.content = `是否确认${row.status ? '停用' : '启用'}该字典？`
  visibleData.confirmBtnText = '确认'
  visibleData.okType = 'danger'
  visibleData.isCancelBtn = true
  visibleData.okFn = () => {
    changeStatus({ id: row.id, status: e ? 1 : 0 })
  }
}

const changeStatus = (obj) => {
  ChangeStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}

// 详情
const detail = (item, idx) => {
  detailDrawerRef.value?.open(item.id, idx + 1)
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

// 新增
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Add(obj).then((res) => {
    if (res.success) {
      message.success('新增成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 编辑
const upRoleDate = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Update(obj).then((res) => {
    if (res.success) {
      message.success('修改成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  Delete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}

const openConfigList = (row) => {
  if (row.status === 0) {
    message.warning('字典已停用，无法配置字典！')
    return
  }
  const sessionPageStr = sessionStorage.getItem('sessionPage')
  let sessionPage = {}
  if (sessionPageStr) {
    sessionPage = JSON.parse(sessionPageStr)
    sessionPage[`dictionarySetting${row.id}`] = 'true'
  } else {
    sessionPage[`dictionarySetting${row.id}`] = 'true'
  }
  sessionStorage.setItem('sessionPage', JSON.stringify(sessionPage))
  router.push(`/dictionarySetting/${row.id}/${row?.company?.name || '系统默认'}/${row.code}`)
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

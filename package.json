{"name": "areateachplat", "private": true, "version": "*******", "type": "module", "scripts": {"dev": "vite --mode development", "pro": "vite --mode production", "build:test": "vite build --mode test", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:stage": "vite build --mode staging", "preview": "vite preview", "prepare": "husky install", "pre-commit": "lint-staged", "lint": "eslint --fix --ext .js,.tsx,.ts,.jsx src"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2": "^5.2.7", "address-parse": "^1.2.19", "ant-design-vue": "4.x", "axios": "^1.7.8", "dayjs": "^1.11.13", "gm-crypt": "^0.0.2", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^3.0.2", "pinyin-pro": "^3.26.0", "sass": "^1.82.0", "sortablejs": "^1.15.6", "unocss": "^0.65.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vxe-pc-ui": "^4.0.35", "vxe-table": "4.7.36", "watermark-js-plus": "^1.5.8"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/node": "^22.10.1", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@unocss/transformer-directives": "^0.65.1", "@vitejs/plugin-vue": "^5.2.1", "commitlint": "^18.4.3", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-vue": "^9.19.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "postcss": "^8.4.32", "postcss-html": "^1.5.0", "postcss-pxtorem": "^6.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.4.2", "stylelint": "^16.0.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.4.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^35.0.0", "stylelint-config-standard-scss": "^12.0.0", "terser": "^5.36.0", "typescript": "~5.6.2", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^6.0.1", "vite-plugin-zip-pack": "^1.2.4", "vue-tsc": "^2.1.10"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --fix"], "**/*.{vue,scss,html}": ["stylelint --fix"]}}
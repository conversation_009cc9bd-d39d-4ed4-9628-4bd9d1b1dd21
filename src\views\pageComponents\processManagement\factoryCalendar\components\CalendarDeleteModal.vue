<template>
  <a-modal :open="open" title="删除工厂日历" width="420px" @cancel="handleCancel">
    <span>{{ content }}</span>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" danger @click="handleSubmit">确认删除</a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { deleteFactoryCalendar } from '@/servers/factoryCalendar'
import { message } from 'ant-design-vue'

const props = defineProps<{
  open: boolean
  initValue: { id: number }
}>()

const emits = defineEmits(['update:open', 'update'])

const content = '是否确认删除工厂日历？删除前，请先删除关联该工厂日历的所有数据，否则会操作失败！'

const handleCancel = () => {
  emits('update:open', false)
}

const handleSubmit = async () => {
  const res = await deleteFactoryCalendar(props.initValue.id)
  message.success(res.message)
  emits('update:open', false)
  emits('update')
}
</script>

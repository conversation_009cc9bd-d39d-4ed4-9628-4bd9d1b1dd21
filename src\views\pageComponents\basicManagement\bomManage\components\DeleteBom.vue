<template>
  <a-modal v-model:open="openModal" title="删除产品BOM" @ok="showModal" centered @cancel="handleCancel">
    <template #footer>
      <a-button type="default" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit" danger>确定删除</a-button>
    </template>
    <span>确认删除产品BOM？</span>
    <br />
    <span>此操作将永久删除选中的产品BOM配置，且无法恢复</span>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// 是否显示弹窗
const openModal = ref(false)
// 显示弹窗
const showModal = () => {
  openModal.value = true
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}
// 确定
const handleSubmit = () => {
  openModal.value = false
}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>

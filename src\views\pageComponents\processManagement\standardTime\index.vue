<template>
  <div class="main px-20 py-12">
    <Form v-model:form="formArr" :page-type="PageType.STANDARD_TIME" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.STANDARD_TIME" :isIndex="true" :get-list="getStandardTimes">
      <template #right-btn>
        <a-button v-if="btnPermission[PERM_CODE.CREATE]" type="primary" :icon="h(PlusOutlined)" @click="handleUpdateTime('add')">新建标准工时</a-button>
      </template>
      <template #company_name="{ row }">
        <span>{{ row.company?.name || '--' }}</span>
      </template>
      <template #step_name="{ row }">
        <span>{{ row.data_dictionary_item?.key || '--' }}</span>
      </template>
      <template #goods_code="{ row }">
        <span>{{ row.material?.goods_code || '--' }}</span>
      </template>
      <template #goods_name="{ row }">
        <span>{{ row.material?.goods_name || '--' }}</span>
      </template>
      <template #spec="{ row }">
        <span>{{ row.material?.spec || '--' }}</span>
      </template>
      <template #creator="{ row }">
        <span>{{ row.creator?.real_name || '--' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button v-if="btnPermission[PERM_CODE.VIEW]" @click="handleViewTime(row.id)" class="btn">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="btnPermission[PERM_CODE.EDIT]" @click="handleUpdateTime('edit', row.id)">编辑</a-menu-item>
                <a-menu-item v-if="btnPermission[PERM_CODE.DELETE]" @click="handleDelete(row.id)">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>
    <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, h, shallowRef, provide } from 'vue'
import { PageType } from '@/common/enum'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getStandardTimes, getStepOptions } from '@/servers/standardTime'
import Form from '@/components/Form.vue'
import { PERM_CODE } from './types'
import TimeViewDrawer from './components/TimeViewDrawer.vue'
import TimeUpdateDrawer from './components/TimeUpdateDrawer.vue'
import TimeDeleteModal from './components/TimeDeleteModal.vue'

const { btnPermission } = usePermission()
const productStepOptions = ref()
provide('productStepOptions', productStepOptions)

const tableRef = ref()
const search = () => tableRef.value.search()

// 查询表单
const formArr = ref<any[]>([
  {
    label: '请输入物料编码',
    value: null,
    type: 'input',
    key: 'goods_code',
  },
  {
    label: '请输入物料名称',
    value: null,
    type: 'input',
    key: 'goods_name',
  },
  {
    label: '请选择生产阶别',
    value: null,
    type: 'select',
    selectArr: productStepOptions.value,
    key: 'data_dictionary_item_id',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])

// component
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  id: null,
})
const refresh = () => {
  tableRef.value.refresh()
}

// 新增/编辑标准工时
const handleUpdateTime = (type: string, id?: string) => {
  component.value = TimeUpdateDrawer
  open.value = true
  initValue.type = type
  initValue.id = id
}

// 查看标准工时
const handleViewTime = (id: number) => {
  component.value = TimeViewDrawer
  open.value = true
  initValue.id = id
}

// 删除标准工时
const handleDelete = (id: number) => {
  component.value = TimeDeleteModal
  open.value = true
  initValue.id = id
}

// 获取生产阶别下拉列表
const getProductStepOptions = async () => {
  const res = await getStepOptions()
  productStepOptions.value = res.data?.product_step?.map((item) => ({
    value: item.id,
    label: item.key,
  }))
  const target = formArr.value?.find((item) => item.key === 'data_dictionary_item_id')
  target.selectArr = productStepOptions.value
}

onMounted(() => {
  search()
  getProductStepOptions()
})
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}
</style>

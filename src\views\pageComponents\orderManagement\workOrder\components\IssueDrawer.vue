<template>
  <a-drawer v-model:open="visible" title="下达工单" cancelText="取消" placement="right" width="1050px" class="issue-dlg" @afterVisibleChange="formRef.clearValidate()">
    <div class="issueBox">
      <a-form ref="formRef" :model="formData">
        <a-row :gutter="24" class="mt-20px mb-20px">
          <a-col class="gutter-row" :span="8">
            <a-form-item label="工单号" name="order_number">
              <span>{{ formData.order_number || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="生产阶别" name="production_stage_text">
              <span>{{ formData.production_stage_text || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="计划数量" name="plan_nums">
              <span>{{ formData.plan_nums || '-' }}</span>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24" class="mb-20px">
          <a-col class="gutter-row" :span="8">
            <a-form-item label="客户" name="customer_name">
              <span>{{ formData.customer_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="物料名称" name="material_name">
              <span>{{ formData.material_name || '-' }}</span>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="" :name="['issue_details']">
          <a-table size="small" :pagination="false" :columns="columns" :data-source="formData.issue_details" bordered :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">
            <template #bodyCell="{ record, index, column }">
              <template v-if="column.key === 'index'">{{ index + 1 }}</template>
              <template v-if="column.key === 'production_stage'">{{ record.production_stage }}</template>
              <template v-if="column.key === 'dispatch_order_number'">{{ record.dispatch_order_number }}</template>
              <template v-if="column.key === 'plan_nums'">
                <a-form-item :id="`name${record.index}`" label="" :name="['issue_details', index, 'plan_nums']" :rules="[{ required: true, message: '输入计划数量' }]">
                  <a-input-number v-model:value="record.plan_nums" placeholder="请输入计划数量" allow-clear :min="0" :precision="0" class="w-full" :disabled="record.id > 0" />
                </a-form-item>
              </template>
              <template v-if="column.key === 'work_center_id'">
                <a-form-item :id="`name${record.index}`" label="" :name="['issue_details', index, 'work_center_id']" :rules="[{ required: true, message: '请选择工作中心' }]">
                  <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="record.work_center_id" placeholder="请选择" :options="workOptions" allow-clear></a-select>
                </a-form-item>
              </template>
              <template v-if="column.key === 'plan_start_time'">
                <a-form-item :id="`name${record.index}`" label="" :name="['issue_details', index, 'plan_start_time']" :rules="[{ required: true, message: '请选择预计开工日期' }]">
                  <a-date-picker show-time placeholder="请选择日期" v-model:value="record.plan_start_time" :valueFormat="'YYYY-MM-DD HH:mm:ss'" class="w188px" />
                </a-form-item>
              </template>

              <template v-if="column.key === 'operate'">
                <a-form-item :id="`operate${record.id}`" label="" :name="['issue_details', index, 'operate']">
                  <a-button size="small" @click="addBtn(index)">添加</a-button>
                  <a-popconfirm title="确认删除该行数据吗?" ok-text="确定" cancel-text="取消" @confirm="removeBtn(index)">
                    <a-button size="small" v-if="formData.issue_details.length > 1" class="ml-10px" :disabled="record.id > 0">删除</a-button>
                  </a-popconfirm>
                </a-form-item>
              </template>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button style="margin-right: 8px" type="primary" @click="tapSubmit">保 存</a-button>
      <a-button @click="visible = false">取 消</a-button>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { IssueDetails, Issue, GetWorkCenterList } from '@/servers/workOrder'

const emit = defineEmits(['cancel'])
const visible = ref(false)
const btnLoading = ref(false)
const formRef = ref()
const formData: any = ref({
  issue_details: [],
})

const columns = ref([
  { title: '', key: 'index' },
  { title: '生产阶别', key: 'production_stage' },
  { title: '派工单号', key: 'dispatch_order_number' },
  { title: '计划数量', key: 'plan_nums', width: 180 },
  { title: '工作中心', key: 'work_center_id', width: 180 },
  { title: '预计开工时间', key: 'plan_start_time', width: 180 },
  { title: '操作', key: 'operate', width: 150 },
])
const workOptions = ref([])
const addBtn = (index) => {
  const obj = {
    index: Math.random(),
    production_stage: formData.value.production_stage_text,
    dispatch_order_number: null,
    plan_nums: 0,
    work_center_id: null,
    plan_start_time: '',
  }
  formData.value.issue_details.splice(index + 1, 0, obj)
}
const removeBtn = (index) => {
  formData.value.issue_details.splice(index, 1)
  formRef.value?.clearValidate()
}

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    btnLoading.value = true
    const obj = {
      id: formData.value.id,
      issue_details: [...formData.value.issue_details].map((item) => ({
        dispatch_order_number: item.dispatch_order_number,
        plan_nums: item.plan_nums,
        work_center_id: item.work_center_id,
        plan_start_time: item.plan_start_time,
      })),
    }
    Issue(obj)
      .then((res) => {
        btnLoading.value = false
        if (res.success) {
          message.success('下达工单成功！')
          visible.value = false
          emit('cancel')
        }
      })
      .catch(() => {
        btnLoading.value = false
      })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const open = async (row) => {
  const optionRs = await GetWorkCenterList({} as any)
  workOptions.value = optionRs.data.map((item) => ({
    label: item.name,
    value: item.id,
  }))
  const res = await IssueDetails({ id: row.id })
  formData.value = res.data
  formData.value.issue_details = formData.value.work_dispatch_orders || []
  if (formData.value.issue_details?.length === 0) {
    const obj = {
      index: Math.random(),
      production_stage: formData.value.production_stage_text,
      dispatch_order_number: null,
      plan_nums: 0,
      work_center_id: null,
      plan_start_time: '',
    }
    formData.value.issue_details.push(obj)
  } else {
    formData.value.issue_details.forEach((item) => {
      item.production_stage = formData.value.production_stage_text
    })
  }
  console.log(formData.value)
  visible.value = true
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.issueBox {
  padding-bottom: 20px;

  .btn {
    margin: 16px 0;
  }

  :deep(.table-striped) td {
    background-color: #fafafa;
  }

  :deep(.ant-form-item) {
    margin-bottom: 0;
  }

  .ant-form-item-control-input-content {
    .anticon {
      font-size: 18px;
      color: #d9d9d9;
    }

    .anticon + .anticon {
      margin-left: 10px;
    }
  }
}

.topBtn2.ant-radio-group {
  position: relative;

  &::after {
    position: absolute;
    bottom: 0;
    left: -24px;
    width: 30vw;
    height: 1px;
    content: '';
    background-color: #f2f2f2;
  }

  &.ant-radio-group-solid :deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
    color: #409eff;
    background-color: transparent;
    border-bottom: 3px solid #409eff;
  }

  :deep(.ant-radio-button-wrapper) {
    height: 44px;
    padding-inline: 0;
    line-height: 44px;
    border: 0;
    border-radius: 0;
  }

  :deep(.ant-radio-button-wrapper + .ant-radio-button-wrapper) {
    margin-left: 24px;
  }

  :deep(.ant-radio-button-wrapper:not(:first-child))::before {
    width: 0;
  }
}
</style>
<style>
.ant-drawer .issue-dlg .ant-drawer-body {
  padding: 0 24px;
  overflow-x: hidden;
}
</style>

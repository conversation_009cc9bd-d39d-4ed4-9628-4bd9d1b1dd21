<template>
  <a-modal v-model:open="openModal" title="产品BOM状态" @ok="showModal" centered @cancel="handleCancel">
    <template #footer>
      <a-button type="default" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确认</a-button>
    </template>
    <div class="mb-30 mt-40 ml-90">是否确认停用/启用该产品BOM？</div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const emit = defineEmits(['success'])
const currentId = ref(null)
// 是否显示弹窗
const openModal = ref(false)
// 显示弹窗
const showModal = (id) => {
  openModal.value = true
  currentId.value = id
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}
// 确定
const handleSubmit = () => {
  openModal.value = false
  emit('success') // 通知父组件刷新
}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>

<template>
  <a-drawer :title="title" width="650px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" v-bind="formItemLayout">
      <a-form-item label="生产实体" name="workshop_name" :rules="[{ required: true, message: '请选择生产实体' }]">
        <a-cascader
          v-model:value="formState.workshop_name"
          :options="workshopFilterOptions"
          changeOnSelect
          expand-trigger="hover"
          :field-names="{ label: 'name', value: 'id' }"
          placeholder="请选择生产实体"
          @change="handleChangeWorkshop"
        />
      </a-form-item>
      <a-form-item label="生产实体类型" name="workshop_type" :rules="[{ required: true, message: '请选择生产实体类型' }]">
        <a-select v-model:value="formState.workshop_type" disabled :options="WorkshopTypeOptions" placeholder="请选择生产实体类型" />
      </a-form-item>
      <a-form-item label="工作人员" name="account_ids" :rules="[{ required: true, message: '请输入工作人员' }]">
        <span>
          <a-button type="primary" size="small" class="mr-4" :icon="h(PlusOutlined)" @click="handleSearchUser()">添加</a-button>
          <a-button type="primary" danger size="small" :disabled="!toDeleteUserList?.length" :icon="h(DeleteOutlined)" @click="handleDeleteUser()">删除</a-button>
        </span>
        <div v-if="!!allSelectedUserList?.length" class="relative mt-8 flex h-full flex-col">
          <vxe-table
            :data="selectedUserList"
            border
            stripe
            ref="tableRef"
            min-height="0"
            max-height="500px"
            class="table w-full"
            size="mini"
            show-overflow
            @checkbox-all="handleCheckboxChange"
            @checkbox-change="handleCheckboxChange"
          >
            <vxe-column type="checkbox" width="50" fixed="left" align="center" />
            <vxe-column field="real_name" width="100" title="真实姓名" />
            <vxe-column field="department_name" width="180" title="部门" />
            <vxe-column field="position_name" width="130" title="岗位" />
          </vxe-table>
          <div class="flex items-center mt-4">
            <a-pagination
              show-quick-jumper
              showSizeChanger
              :total="tableParams.total"
              v-model:current="tableParams.page"
              v-model:page-size="tableParams.pageSize"
              :page-size-options="pageSizeOptions"
              @change="handleChangePage"
              size="small"
            />
            <span class="ml-8 mt-2">
              总数:
              <span class="page-number">{{ tableParams.total }}</span>
            </span>
          </div>
        </div>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
  <user-search v-if="openModal" v-model:openModal="openModal" :type="userSelectMode" :data="allSelectedUserList" @getAccounts="getAccounts" />
</template>

<script lang="ts" setup>
import { watch, inject, h } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { viewWorkshopPerson, addWorkshopPerson, editWorkshopPerson } from '@/servers/workshopPerson'
import { WorkshopTypeOptions } from '../types'
import UserSearch from './UserSearchModal.vue'

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    id: string
  }
}>()

// 生产实体筛选下拉选项
const workshopFilterOptions = inject('workshopFilterOptions')
const emits = defineEmits(['update:open', 'update'])

const isEditMode = computed(() => props.initValue.type === 'edit') // 是否编辑模式
const userSelectMode = computed(() => (isEditMode.value ? 'single' : 'multiple')) // 用户选择模式：新建-多选，编辑-单选

const TYPE_MAP = {
  add: { title: '新建', api: addWorkshopPerson },
  edit: { title: '编辑', api: editWorkshopPerson },
}
const title = computed(() => `${TYPE_MAP[props.initValue.type]?.title}生产实体人员`)

const openModal = ref(false)
// 点击出现弹窗
const handleSearchUser = () => {
  openModal.value = true
}

// 切换生产实体
const handleChangeWorkshop = (_, selectedOptions) => {
  const options = selectedOptions?.length ? selectedOptions[selectedOptions.length - 1] : {}
  formState.workshop_type = options.workshop_type
  formState.workshop_id = options.id
  formState.workshop_name = options.name
  formRef.value?.validateFields(['workshop_type']) // 更新生产实体类型校验状态
}

// 工作人员列表
const tableRef = ref()
const selectedUserList = ref()
const allSelectedUserList = ref()
const tableParams = reactive({
  pageSize: 20,
  page: 1,
  total: 0,
})
const pageSizeOptions = ref(['10', '20', '30', '40', '50'])
// 假分页，此时只是用户暂时选中工作人员，不直接调接口
const handleChangePage = () => {
  selectedUserList.value = allSelectedUserList.value?.slice((tableParams.page - 1) * tableParams.pageSize, tableParams.page * tableParams.pageSize)
  formRef.value?.validateFields(['account_ids']) // 更新工作人员表单校验状态
}
// 获取选中的用户信息
const getAccounts = (accountList) => {
  allSelectedUserList.value = accountList
  tableParams.total = allSelectedUserList.value?.length || 0
  // 将工作人员表格数据赋值给formState.account_ids
  formState.account_ids = allSelectedUserList.value?.map((item) => item.account_id)
  handleChangePage()
}

const toDeleteUserList = ref()
/** 获取待删除工作人员 */
const handleCheckboxChange = () => {
  const $table = tableRef.value
  if ($table) {
    const currentSelectedRows = $table.getCheckboxRecords()
    const otherSelectedRows = $table.getCheckboxReserveRecords()
    toDeleteUserList.value = [...currentSelectedRows, ...otherSelectedRows]
  }
}
/** 删除工作人员 */
const handleDeleteUser = () => {
  const deleteUsers = new Set(toDeleteUserList.value?.map((item) => item.account_id))
  allSelectedUserList.value = [...allSelectedUserList.value]?.filter((item) => !deleteUsers.has(item.account_id))
  tableParams.total = allSelectedUserList.value?.length || 0
  toDeleteUserList.value = [] // 重置待删除人员列表
  // 将工作人员表格数据赋值给formState.account_ids
  formState.account_ids = allSelectedUserList.value?.map((item) => item.account_id)
  handleChangePage()
}

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
}
const formState = reactive({
  ids: [], // 值不变，传原先personnel的id值，对应生产实体id
  workshop_name: '', // 展示最后一层级
  workshop_id: '',
  workshop_type: null,
  account_ids: [],
})

// 关闭drawer
const onClose = () => {
  formRef.value.resetFields()
  emits('update:open', false)
}

// 提交表单
const onSubmit = () => {
  // 表单检验
  formRef.value
    .validate()
    .then(async () => {
      const apiFn = TYPE_MAP[props.initValue.type]?.api
      try {
        let params: any = {
          workshop_id: formState.workshop_id,
          workshop_type: formState.workshop_type,
          account_ids: formState.account_ids, // 根据最终的工作人员动态生成account_ids
        }
        if (isEditMode.value) {
          // ids：该生产实体原先具备的personnel里的id集合
          params = { ...params, ids: formState.ids }
        }

        await apiFn(params)
        message.success(`${TYPE_MAP[props.initValue.type]?.title}生产实体成功！`)
        emits('update:open', false)
        emits('update')
      } catch (error) {
        console.error(error)
      }
    })
    .catch((error: Error) => {
      console.error(error)
    })
}

watch(
  isEditMode,
  async () => {
    if (isEditMode.value) {
      const res = await viewWorkshopPerson({ id: props.initValue.id })
      Object.assign(formState, {
        ...res.data,
        ids: res.data.personnel?.map((item) => item.id),
      })
      allSelectedUserList.value = res.data.personnel?.map((item) => item.user)
      formState.account_ids = allSelectedUserList.value?.map((item) => item.account_id)
      tableParams.total = allSelectedUserList.value?.length || 0
      handleChangePage()
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<style lang="scss" scoped>
.page-number {
  color: #409eff;
}

:deep(.ant-pagination-options .ant-select.ant-select-in-form-item) {
  width: auto !important;
}
</style>

import { request } from './request'

// 获取生产实体人员列表
export const getWorkshopPersons = (data) => request({ url: '/api/businessBase/workshopPersonnel/index', data })
// 新建生产实体人员
export const addWorkshopPerson = (data) => request({ url: '/api/businessBase/workshopPersonnel/create', data })
// 编辑生产实体人员
export const editWorkshopPerson = (data) => request({ url: '/api/businessBase/workshopPersonnel/update', data })
// 查看生产实体人员
export const viewWorkshopPerson = (data) => request({ url: '/api/businessBase/workshopPersonnel/show', data })
// 删除生产实体人员
export const deleteWorkshopPerson = (data) => request({ url: '/api/businessBase/workshopPersonnel/delete', data })
// 筛选
export const getSelectOptions = (data) => request({ url: '/api/businessBase/workshopPersonnel/filter', data })
// 获取工作人员列表
export const getAccounts = (data) => request({ url: '/api/personnel/user/companyUserSelect', data })
// 部门下拉列表
export const getDepartmentOptions = (data) => request({ url: '/api/personnel/company/getCompanyDepartment', data })
// 岗位下拉列表
export const getPositionOptions = (data) => request({ url: '/api/personnel/position/positionSelect', data })

<template>
  <a-drawer :title="title" :width="840" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" layout="inline" class="form-container" v-bind="formItemLayout">
      <a-col :span="12">
        <a-form-item
          label="工艺编码"
          name="craft_code"
          :rules="[
            { required: true, message: '请输入工艺编码' },
            { pattern: /^[^\u4e00-\u9fa5]*$/, message: '不允许包含中文' },
          ]"
        >
          <a-input v-model:value="formState.craft_code" :maxlength="30" placeholder="请输入工艺编码" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="工艺名称" name="craft_name" :rules="[{ required: true, message: '请输入工艺名称' }]">
          <a-input v-model:value="formState.craft_name" :maxlength="50" placeholder="请输入工艺名称" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="工艺类别" name="craft_category_id" :rules="[{ required: true, message: '请选择工艺类别' }]">
          <a-select v-model:value="formState.craft_category_id" :options="processTypeOptions" :field-names="{ label: 'key', value: 'id' }" placeholder="请选择工艺类别" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="生产阶别" name="stage_id" :rules="[{ required: true, message: '请选择生产阶别' }]">
          <a-select v-model:value="formState.stage_id" :options="stageTypeOptions" :field-names="{ label: 'key', value: 'id' }" placeholder="请选择生产阶别" @change="handleChangeStage" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="前置工艺" name="pre_craft_id">
          <a-select v-model:value="formState.pre_craft_id" :options="preProcedureOptions" :field-names="{ label: 'craft_name', value: 'id' }" allow-clear placeholder="请输入前置工艺" />
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item label="状态" name="status" v-bind="switchFormLayout">
          <a-switch v-model:checked="formState.status" :checkedValue="1" :unCheckedValue="0" checked-children="启用" un-checked-children="停用" />
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item label="是否默认" name="default" v-bind="switchFormLayout">
          <a-switch v-model:checked="formState.default" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" name="remark" :label-col="{ span: 2 }" :wrapper-col="{ span: 21 }" class="remark-item">
          <a-input v-model:value="formState.remark" allow-clear :maxlength="200" placeholder="请输入备注" />
        </a-form-item>
      </a-col>
    </a-form>

    <div class="drawer-title">流程信息</div>
    <div>
      <a-flex justify="space-between" class="mb-8 flex items-center">
        <span class="text-[#409eff] ml-20 mb-4">工艺流程</span>
      </a-flex>
      <vxe-table :data="tableData" align="center" border stripe ref="tableRef" max-height="320px" min-height="0" class="table w-500 ml-20" size="mini" show-overflow>
        <vxe-column field="order" title="顺序" width="60">
          <template #default="{ rowIndex }">{{ rowIndex + 1 }}</template>
        </vxe-column>
        <vxe-column field="information_id" title="工序" width="220">
          <template #default="{ row }">
            <a-select v-model:value="row.information_id" :options="processInfoOptions" :field-names="{ label: 'process_name', value: 'information_id' }" class="w-180" placeholder="请选择工序" />
          </template>
        </vxe-column>
        <vxe-column title="操作" width="220" fixed="right">
          <template #default="{ row, rowIndex }">
            <a-space :gutter="8">
              <a-button type="primary" size="small" @click="handleAddProcess(rowIndex)">添加</a-button>
              <a-button v-if="tableData.length > 1" type="primary" size="small" danger @click="handleDeleteProcess(rowIndex)">删除</a-button>
              <a-button size="small" @click="handleEditProcess(row)">编辑</a-button>
            </a-space>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <template #footer>
      <a-space>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>

  <process-panel v-if="openModal" v-model:openModal="openModal" type="edit" :currentProcess="currentProcess" :parameterMap="parameterMap" @getFlowInfo="getFlowInfo" />
</template>

<script lang="ts" setup>
import { inject } from 'vue'
import { message } from 'ant-design-vue'
import { VxeTableInstance } from 'vxe-table'
import { addProcedure, editProcedure, viewProcedure, getCategoryByStage, getPreProcedureOptions } from '@/servers/processFlow'
import { cloneDeep } from '@/utils'
import { PeculiarityTypeMap } from '../types'
import ProcessPanel from './ProcessPanelModal.vue'

// 下拉选项
const processTypeOptions = inject('processTypeOptions') // 工艺类别
const stageTypeOptions = inject('stageTypeOptions') // 生产阶别
const addPreProcedures: any = inject('preProcedureOptions') // 生产阶别
const preProcedureOptions = ref() // 这里的前置工艺，需要过滤掉自己

const tableRef = ref<VxeTableInstance<any>>()

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    process_flows_id?: number
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const TYPE_MAP = {
  add: { title: '新建', api: addProcedure },
  edit: { title: '编辑', api: editProcedure },
  view: { title: '查看', api: viewProcedure },
}

const title = computed(() => `${TYPE_MAP[props.initValue.type]?.title}工艺流程`)
const isEditMode = computed(() => props.initValue.type === 'edit')

const tableData = ref()
const currentProcess = ref() // 当前选择的工序
const processInfoOptions = ref() // 工序信息下拉选项
const parameterMap = ref([]) // 工序参数表

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
}
const switchFormLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 10 },
}
const formState = reactive({
  id: '',
  craft_code: '',
  craft_name: '',
  craft_category_id: null,
  stage_id: null,
  pre_craft_id: null,
  status: 0,
  default: 0,
  remark: '',
})

const currentRowIndex = ref<number>(-1)

// 根据生产阶别带出工序信息
const handleChangeStage = async () => {
  await getCategoryInfo()
  tableData.value = cloneDeep(processInfoOptions.value)?.map((item) => ({
    ...item,
    parameter: cloneDeep(parameterMap.value),
  }))
}

/** 获取工序信息：工序信息及参数下拉列表 */
const getCategoryInfo = async () => {
  const res = await getCategoryByStage({ stage_id: formState.stage_id })
  const processOptions = res.data?.information?.map((item) => ({ information_id: item.id, process_name: item.process_name })) || []
  processInfoOptions.value = cloneDeep(processOptions) || []
  parameterMap.value = res.data?.parameters?.map((item) => ({ ...item, parameter_id: item.id })) || []
}

// 添加工序
const handleAddProcess = (rowIndex: number) => {
  const initTableData = {
    information_id: '',
    peculiarity_id: '',
    process_type_id: '',
    parameter: cloneDeep(parameterMap.value),
  }
  tableData.value.splice(rowIndex + 1, 0, cloneDeep(initTableData))
  console.log('handleAddProcess:', tableData.value)
}

// 删除工序
const handleDeleteProcess = (rowIndex: number) => {
  tableData.value.splice(rowIndex, 1)
}

// 编辑工序
const openModal = ref(false)
const handleEditProcess = (row: any) => {
  openModal.value = true
  // 根据工序信息id获取工序名称
  const process_name = processInfoOptions.value?.find((item) => item.information_id === row.information_id)?.process_name
  currentProcess.value = { ...row, process_name }
  currentRowIndex.value = row._X_ROW_KEY
}

/** 获取工序信息 */
const getFlowInfo = (info) => {
  const target = tableData.value?.find((item) => item._X_ROW_KEY === currentRowIndex.value)
  if (target) {
    Object.assign(target, { ...info })
  }
}

/** 编辑模式-获取前置工艺 */
const getFilterPreProcedures = async () => {
  const res = await getPreProcedureOptions({ id: props.initValue.process_flows_id })
  return res.data
}

// 关闭drawer
const onClose = () => {
  formRef.value.resetFields()
  emits('update:open', false)
}

/** 对工序信息内容进行校验 */
const checkProcess = (tableData) => {
  const first_peculiarity_id = tableData[0].peculiarity_id
  const end_peculiarity_id = tableData[tableData.length - 1].peculiarity_id
  if (first_peculiarity_id !== PeculiarityTypeMap.START) {
    message.error('第一道工序必须为开始工序')
    return false
  }
  if (end_peculiarity_id !== PeculiarityTypeMap.END) {
    message.error('最后一道工序必须为结束工序')
    return false
  }
  return true
}

// 提交表单
const onSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      const apiFn = TYPE_MAP[props.initValue.type]?.api
      const flowTable = [...tableData.value]?.map((item, index) => ({
        sequence: index + 1,
        information_id: item.information_id,
        peculiarity_id: item.peculiarity_id,
        process_type_id: item.process_type_id,
        parameter: item.parameter?.map((item) => {
          const { values, ...rest } = item
          return rest
        }),
      }))
      if (!checkProcess(flowTable)) return
      const params = { ...formState, flow: flowTable }
      try {
        const res = await apiFn(params)
        if (res.code === 0 && res.success) {
          message.success(`工艺流程${TYPE_MAP[props.initValue.type]?.title}成功！`)
          emits('update:open', false)
          emits('update')
        } else {
          message.error(res?.message)
        }
      } catch (error) {
        console.error(error)
      }
    })
    .catch((error: Error) => {
      console.error(error)
    })
}

watch(
  isEditMode,
  async () => {
    if (isEditMode.value) {
      const res = await viewProcedure({ id: props.initValue.process_flows_id })

      Object.assign(formState, {
        ...res.data,
        data_dictionary_item_id: res.data?.data_dictionary_item_id,
        goods_code: res.data?.material?.goods_code,
        goods_name: res.data?.material?.goods_name,
        spec: res.data?.material?.spec,
      })
      tableData.value = res.data?.flow?.sort((a, b) => a.sequence - b.sequence)
      getCategoryInfo() // 编辑模式下需要重新获取工序信息的下拉选项（工序信息和参数）
    }
    // 编辑模式下，前置工艺需要重新获取过滤后的前置工艺，不能取全部
    preProcedureOptions.value = isEditMode.value ? await getFilterPreProcedures() : addPreProcedures.value
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<style lang="scss" scoped>
.form-container {
  margin-bottom: 24px;

  :deep(.ant-col) {
    margin-bottom: 10px;
  }
}

.remark-item {
  :deep(.ant-form-item-label) {
    margin-left: 14px;
  }
}
</style>

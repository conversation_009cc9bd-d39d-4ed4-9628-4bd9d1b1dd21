<template>
  <a-modal :open="openModal" title="选择物料信息" width="840px" :maskClosable="false" @cancel="handleCancel">
    <div class="search-form flex flex-inline gap-8 my-16">
      <div class="form-input flex gap-8">
        <a-input v-model:value="formState.goods_code" allow-clear class="w-150" placeholder="请输入物料编码" />
        <a-input v-model:value="formState.goods_name" allow-clear class="w-150" placeholder="请输入物料名称" />
        <a-select v-model:value="formState.goods_category_id" allow-clear class="w-150" :options="categoryOptions" placeholder="请选择商品分类" />
        <a-input v-model:value="formState.spec" allow-clear class="w-150" placeholder="请输入型号规格" />
      </div>
      <div class="form-button flex-inline">
        <a-button class="btn" type="primary" @click="handleSearchMaterial" style="margin-right: 10px">查询</a-button>
        <a-button class="btn" @click="handleReset">重置</a-button>
      </div>
    </div>
    <vxe-table :data="tableData" border align="center" stripe ref="tableRef" max-height="320px" min-height="0" class="table w-full" size="mini" show-overflow @radio-change="handleRadioChange">
      <vxe-column type="radio" width="50" />
      <vxe-column field="goods_code" title="物料编码" width="120" />
      <vxe-column field="goods_name" title="物料名称" minWidth="200" />
      <vxe-column field="goods_category_name" title="商品分类" minWidth="120" />
      <vxe-column field="spec" title="型号规格" minWidth="200" />
    </vxe-table>

    <div class="flex items-center my-8">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="tableParams.total"
          show-size-changer
          v-model:current="tableParams.page"
          v-model:pageSize="tableParams.pageSize"
          :page-size-options="pageSizeOptions"
          size="small"
          @change="handleChangePage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ tableParams.total }}</span>
        </span>
      </div>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getMaterials, getMaterialOptions } from '@/servers/standardTime'

defineProps<{
  openModal: boolean
}>()

const formState = reactive({
  goods_code: '',
  goods_name: '',
  goods_category_id: null, // 商品分类
  spec: '',
})

const tableRef = ref()
const tableData = ref<any>([])
const tableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})
const pageSizeOptions = ['20', '50', '100', '250']
// 切换分页
const handleChangePage = () => {
  getMaterialList()
}
/** 表单查询 */
const handleSearchMaterial = () => {
  tableParams.page = 1
  getMaterialList()
}

const emits = defineEmits(['update:openModal', 'getMaterial'])

// 获取表格选中数据
const selectedMaterial = ref()
const handleRadioChange = ({ row }) => {
  selectedMaterial.value = row
}

// 重置
const handleReset = () => {
  formState.goods_name = ''
  formState.goods_code = ''
  formState.goods_category_id = null
  formState.spec = ''
  getMaterialList()
}

const handleCancel = () => {
  emits('update:openModal', false)
}

const handleSubmit = () => {
  if (!selectedMaterial.value) {
    message.error('请选择物料！')
    return
  }
  emits('getMaterial', selectedMaterial.value)
  emits('update:openModal', false)
}

// 获取物料信息列表
const getMaterialList = async () => {
  const { total, ...otherParams } = tableParams
  const res = await getMaterials({ ...formState, ...otherParams })
  tableData.value = res.data.list || []
  tableParams.total = res.data?.total || 0
}

const categoryOptions = ref()
// 获取物料下拉列表
const getMaterialOption = async () => {
  const res = await getMaterialOptions({})
  categoryOptions.value = res.data?.map((item) => {
    const [value, label] = Object.entries(item)[0]
    return { value: Number(value), label }
  })
}

onMounted(() => {
  getMaterialList()
  getMaterialOption()
})
</script>

<style lang="scss" scoped>
.page-number {
  color: #409eff;
}
</style>

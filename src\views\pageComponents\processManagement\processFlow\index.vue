<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.PROCESS_FLOW" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.PROCESS_FLOW" :isIndex="true" :get-list="getProcedureList">
      <template #right-btn>
        <a-button v-if="btnPermission[PERM_CODE.CREATE]" type="primary" :icon="h(PlusOutlined)" @click="handleUpdateFlow('add')">新建工艺流程</a-button>
      </template>
      <template #pre_craft_name="{ row }">
        <span>{{ row.pre_craft_name || '--' }}</span>
      </template>
      <template #status="{ row }">
        <a-switch
          v-if="btnPermission[PERM_CODE.UPDATE_STATUS]"
          :checked="row.status"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="启用"
          un-checked-children="停用"
          @change="handleSwitch('status', row)"
        />
        <span v-else>{{ row.status ? '启用' : '停用' }}</span>
      </template>
      <template #default="{ row }">
        <a-switch
          v-if="btnPermission[PERM_CODE.UPDATE_STATUS]"
          :checked="row.default"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="是"
          un-checked-children="否"
          @change="handleSwitch('default', row)"
        />
        <span v-else>{{ row.default ? '是' : '否' }}</span>
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at || '' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button v-if="btnPermission[PERM_CODE.VIEW]" @click="handleViewFlow(row.id)" class="btn">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="btnPermission[PERM_CODE.RELATE]" @click="handleRelate(row.id, row.craft_name)">关联产品</a-menu-item>
                <a-menu-item v-if="btnPermission[PERM_CODE.EDIT]" @click="handleUpdateFlow('edit', row.id)">编辑</a-menu-item>
                <a-menu-item v-if="btnPermission[PERM_CODE.DELETE]" @click="handleDelete(row.id)">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, h, shallowRef, provide } from 'vue'
import { PageType } from '@/common/enum'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getProcedureList, getOptions, getPreProcedureOptions, updateProcedureStatus } from '@/servers/processFlow'
import Form from '@/components/Form.vue'
import { message } from 'ant-design-vue'
import { PERM_CODE } from './types'
import ProcessAddDrawer from './components/ProcessAddDrawer.vue'
import ProcessViewDrawer from './components/ProcessViewDrawer.vue'
import ProcessDeleteModal from './components/ProcessDeleteModal.vue'
import GoodsRelateModal from './components/GoodsRelateModal.vue'

const { btnPermission } = usePermission()

// component
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  process_flows_id: -1,
})
const refresh = () => {
  tableRef.value.refresh()
  getPreProcedureList() // 需要更新前置工艺列表
}

// 新增/编辑工艺流程
const handleUpdateFlow = (type: string, id?: number) => {
  component.value = ProcessAddDrawer
  open.value = true
  initValue.type = type
  initValue.process_flows_id = id
}

// 查看工艺流程
const handleViewFlow = (id: number) => {
  component.value = ProcessViewDrawer
  open.value = true
  initValue.process_flows_id = id
}

// 删除工艺流程
const handleDelete = (id: number) => {
  component.value = ProcessDeleteModal
  open.value = true
  initValue.process_flows_id = id
}

// 切换
const handleSwitch = async (code: string, row: any) => {
  const params = { [code]: row[code] ? 0 : 1, id: row.id }
  try {
    await updateProcedureStatus(params)
    message.success('更新成功！')
    refresh()
  } catch (error) {
    console.error(error)
  }
}

// 关联商品
const handleRelate = (id: number, craft_name: string) => {
  component.value = GoodsRelateModal
  open.value = true
  initValue.process_flows_id = id
  initValue.process_flows_name = craft_name
}

const tableRef = ref()
const search = () => tableRef.value.search()
// 查询表单
const formArr = ref<any[]>([
  {
    label: '搜索工艺编码',
    value: null,
    type: 'input',
    key: 'craft_code',
  },
  {
    label: '请输入工艺名称',
    value: null,
    type: 'input',
    key: 'craft_name',
  },
  {
    label: '请选择前置工艺',
    value: null,
    type: 'select',
    selectArr: null,
    key: 'pre_craft_id',
    fieldNames: { label: 'craft_name', value: 'id' },
  },
  {
    label: '生产阶别',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: null,
    key: 'stage_id',
    fieldNames: { label: 'key', value: 'id' },
  },
  {
    label: '工艺类别',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: null,
    key: 'craft_category_id',
    fieldNames: { label: 'key', value: 'id' },
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'modify_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])

// 获取下拉选项
const stageTypeOptions = ref() // 生产阶别列表
provide('stageTypeOptions', stageTypeOptions)

const processTypeOptions = ref() // 工艺类别列表
provide('processTypeOptions', processTypeOptions)

const categoryTypeOptions = ref() // 工序类别列表
provide('categoryTypeOptions', categoryTypeOptions)

const peculiarityTypeOptions = ref() // 工序特性列表
provide('peculiarityTypeOptions', peculiarityTypeOptions)

const preProcedureOptions = ref() // 前置工艺下拉选项列表
provide('preProcedureOptions', preProcedureOptions)

const getSelectOptions = async () => {
  // 生产阶别、工艺类别、工序类别列表
  const res = await getOptions({})
  stageTypeOptions.value = res.data?.stage_type
  processTypeOptions.value = res.data?.process_type
  categoryTypeOptions.value = res.data?.category_type
  peculiarityTypeOptions.value = res.data?.peculiarity_type

  // 查询表单添加 selectArr 选项
  formArr.value.forEach((item) => {
    if (item.key === 'stage_id') {
      item.selectArr = stageTypeOptions.value
    } else if (item.key === 'craft_category_id') {
      item.selectArr = processTypeOptions.value
    }
  })
}

/** 获取前置工艺列表 */
const getPreProcedureList = async () => {
  const preCraftList = await getPreProcedureOptions({})
  preProcedureOptions.value = preCraftList.data || []
  const target = formArr.value?.find((item) => item.key === 'pre_craft_id')
  target.selectArr = preProcedureOptions.value
}

onMounted(() => {
  search()
  getSelectOptions()
  getPreProcedureList()
})
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}
</style>

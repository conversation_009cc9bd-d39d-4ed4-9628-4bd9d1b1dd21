const otherRouter = [
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
  },
  {
    path: '/systemManagement',
    title: '系统管理',
    name: '系统管理',
    children: [
      {
        path: '/roleManagement',
        title: '角色管理',
        name: '角色管理',
        component: () => import('@/views/pageComponents/systemManagement/roleManagement/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/roleUserConfig/:id',
        title: '分配用户1',
        name: '分配用户1',
        component: () => import('@/views/pageComponents/systemManagement/roleManagement/roleUserConfig.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/watermarkManagement',
        title: '通用设置',
        name: '通用设置',
        component: () => import('@/views/pageComponents/systemManagement/watermarkManagement/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/barcodeConfig',
        title: '条码生成配置',
        name: '条码生成配置',
        component: () => import('@/views/pageComponents/barcode/barcodeConfig/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/barcodeVariable',
        title: '条码生成变量',
        name: '条码生成变量',
        component: () => import('@/views/pageComponents/barcode/barcodeVariable/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/clientModule',
    title: '用户管理1',
    name: '用户管理1',
    children: [
      {
        path: '/userLists',
        title: '用户管理',
        name: '用户管理',
        component: () => import('@/views/pageComponents/clientModule/userList/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/enterprise',
        title: '企业信息',
        name: '企业信息',
        // component: () => import('@/views/pageComponents/clientModule/Enterprise/index.vue'),
        component: () => import('@/views/pageComponents/basicManagement/bomManage/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/company',
        title: '公司信息',
        name: '公司信息',
        component: () => import('@/views/pageComponents/clientModule/Company/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/department',
        title: '部门信息',
        name: '部门信息',
        component: () => import('@/views/pageComponents/clientModule/Department/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/postInfo',
        title: '岗位信息',
        name: '岗位信息',
        component: () => import('@/views/pageComponents/clientModule/PostInfo/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/userPostSetting/:id/:name',
        title: '分配用户',
        name: '分配用户',
        component: () => import('@/views/pageComponents/clientModule/PostInfo/userPostSetting.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/processManagement',
    title: '工艺管理',
    name: '工艺管理',
    children: [
      {
        path: '/processParamtters',
        title: '工序参数',
        name: '工序参数',
        component: () => import('@/views/pageComponents/processManagement/processParamtters/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/processInfo',
        title: '工序信息',
        name: '工序信息',
        component: () => import('@/views/pageComponents/processManagement/processInfo/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/processFlow',
        title: '工艺流程',
        name: '工艺流程',
        component: () => import('@/views/pageComponents/processManagement/processFlow/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/factoryCalendar',
        title: '工厂日历',
        name: '工厂日历',
        component: () => import('@/views/pageComponents/processManagement/factoryCalendar/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/standardTime',
        title: '标准工时',
        name: '标准工时',
        component: () => import('@/views/pageComponents/processManagement/standardTime/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/workshop',
        title: '车间管理',
        name: 'workshop',
        component: () => import('@/views/pageComponents/processManagement/workshopModel/workshop/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
      {
        path: '/workshopArea',
        title: '区域管理',
        name: 'workshopArea',
        component: () => import('@/views/pageComponents/processManagement/workshopModel/area/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
      {
        path: '/workshopWorkcenter',
        title: '工作中心',
        name: 'workshopWorkcenter',
        component: () => import('@/views/pageComponents/processManagement/workshopModel/center/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
    ],
  },
  // {
  //   path: '/basicConfig',
  //   title: '基础配置',
  //   name: '基础配置',
  //   children: [
  //     {
  //       path: '/bomManage',
  //       title: 'BOM管理',
  //       name: 'BOM管理',
  //       component: () => import('@/views/pageComponents/basicManagement/bomManage/index.vue'),
  //       meta: {
  //         KeepAlive: true,
  //       },
  //     },
  //   ],
  // },
  {
    path: '/orderManagement',
    title: '工单',
    name: '工单',
    children: [
      {
        path: '/workOrder',
        title: '工单管理',
        name: '工单管理',
        component: () => import('@/views/pageComponents/orderManagement/workOrder/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/workDispatchOrder',
        title: '派工单管理',
        name: '派工单管理',
        component: () => import('@/views/pageComponents/orderManagement/dispatchOrder/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/centerEnable',
        title: '中心启用',
        name: '中心启用',
        component: () => import('@/views/pageComponents/orderManagement/centerEnable/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/basicManagement',
    title: '基础信息',
    name: '基础信息',
    children: [
      {
        path: '/dictionaryList',
        title: '字典管理',
        name: '字典管理',
        component: () => import('@/views/pageComponents/dictionary/dictionaryList/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/dictionarySetting/:id/:company/:code',
        title: '配置字典管理',
        name: '配置字典管理',
        component: () => import('@/views/pageComponents/dictionary/dictionarySetting/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/paramttersList',
        title: '参数设置',
        name: '参数设置',
        component: () => import('@/views/pageComponents/dictionary/paramttersList/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/areaPerson',
        title: '区域人员',
        name: '区域人员',
        component: () => import('@/views/pageComponents/dictionary/areaPerson/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/workshopPerson',
        title: '生产实体人员',
        name: '生产实体人员',
        component: () => import('@/views/pageComponents/dictionary/workshopPerson/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
]
export default otherRouter

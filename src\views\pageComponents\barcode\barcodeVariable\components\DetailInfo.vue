<template>
  <a-card title="" class="w-full h-100% font-size-16 overflow-auto" v-if="showCard">
    <a-form ref="formRef" :model="editForm">
      <a-row>
        <a-col :span="24">
          <a-form-item label="类型" name="type">
            <span>{{ variableMap[editForm.type] }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="名称" name="name">
            <span>{{ editForm.name }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="editForm.type == 1">
          <a-form-item label="进制" name="radix">
            <span>{{ editForm.radix }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="editForm.type != 1">
          <a-form-item label="位数" name="radix">
            <span>{{ editForm.radix }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="editForm.type != 1">
          <a-form-item label="默认填充" name="default_fill">
            <span>{{ editForm.default_fill }}</span>
          </a-form-item>
        </a-col>
        <a-divider />
        <a-col :span="24">
          <div class="w-300px">
            <SimpleTable :tableKey="tableKey" :data="editForm.values"></SimpleTable>
          </div>
        </a-col>
      </a-row>
    </a-form>
  </a-card>
</template>

<script lang="ts" setup>
import { GetOptions, Show } from '@/servers/barcodeVariable'

const showCard = ref(false)
const editForm = ref({})
const variableMap = reactive({
  1: '流水号进制',
  2: '年',
  3: '月',
  4: '日',
  5: '周',
})
const titleMap = reactive({
  1: '原始值',
  2: '年份',
  3: '月份',
  4: '日期',
  5: '年周',
})
const tableKey = ref([
  { title: '', field: 'original' },
  { title: '条码值', field: 'barcode' },
])
const open = async (id) => {
  const res = await Show({ id })
  editForm.value = res.data
  tableKey.value[0].title = titleMap[res.data.type]
  if (res.data.type == 5) {
    editForm.value.values = res.data.values.map((item) => {
      return {
        ...item,
        original: `第${item.original}周`,
      }
    })
  }
  await getOptions()
  showCard.value = true
}
const getOptions = async () => {
  const res = await GetOptions({})
  console.log(res)
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped></style>

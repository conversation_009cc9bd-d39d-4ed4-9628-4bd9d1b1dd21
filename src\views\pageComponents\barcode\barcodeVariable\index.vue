<template>
  <div class="w-full text-black font-size-16 font-bold">
    <div class="w-100px h30px line-height-30px">条码生成变量</div>
    <a-row class="h-90%">
      <a-col :span="12" class="overflow-auto h-100%">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card title="流水号进制" class="w-full font-size-16">
              <!-- <template #extra>
                <a-button type="primary">新增</a-button>
              </template> -->
              <div class="h-110px overflow-auto">
                <div style="border-bottom: 1px solid #ccc" v-for="item in radixList" :key="item.id" class="flex cursor-pointer">
                  <div class="w-90% hover:text-blue" @click="detailBtn(item)">{{ item.name }}</div>
                  <!-- <div class="hover:text-blue mr-10px" @click="editBtn(item)"><EditOutlined /></div>
                  <div class="hover:text-blue" @click="delBtn(item)"><DeleteOutlined /></div> -->
                </div>
              </div>
            </a-card>
          </a-col>

          <a-col :span="12">
            <a-card title="年" class="w-full font-size-16">
              <!-- <template #extra>
                <a-button type="primary">新增</a-button>
              </template> -->
              <div class="h-110px overflow-auto">
                <div style="border-bottom: 1px solid #ccc" v-for="item in yearList" :key="item.id" class="flex cursor-pointer">
                  <div class="w-90% hover:text-blue" @click="detailBtn(item)">{{ item.name }}</div>
                  <!-- <div class="hover:text-blue mr-10px" @click="editBtn(item)"><EditOutlined /></div>
                  <div class="hover:text-blue" @click="delBtn(item)"><DeleteOutlined /></div> -->
                </div>
              </div>
            </a-card>
          </a-col>

          <a-col :span="12">
            <a-card title="月" class="w-full font-size-16">
              <!-- <template #extra>
                <a-button type="primary">新增</a-button>
              </template> -->
              <div class="h-110px overflow-auto">
                <div style="border-bottom: 1px solid #ccc" v-for="item in monthList" :key="item.id" class="flex cursor-pointer">
                  <div class="w-90% hover:text-blue" @click="detailBtn(item)">{{ item.name }}</div>
                  <!-- <div class="hover:text-blue mr-10px" @click="editBtn(item)"><EditOutlined /></div>
                  <div class="hover:text-blue" @click="delBtn(item)"><DeleteOutlined /></div> -->
                </div>
              </div>
            </a-card>
          </a-col>

          <a-col :span="12">
            <a-card title="日" class="w-full font-size-16">
              <!-- <template #extra>
                <a-button type="primary">新增</a-button>
              </template> -->
              <div class="h-110px overflow-auto">
                <div style="border-bottom: 1px solid #ccc" v-for="item in dayList" :key="item.id" class="flex cursor-pointer">
                  <div class="w-90% hover:text-blue" @click="detailBtn(item)">{{ item.name }}</div>
                  <!-- <div class="hover:text-blue mr-10px" @click="editBtn(item)"><EditOutlined /></div>
                  <div class="hover:text-blue" @click="delBtn(item)"><DeleteOutlined /></div> -->
                </div>
              </div>
            </a-card>
          </a-col>

          <a-col :span="12">
            <a-card title="周" class="w-full font-size-16">
              <!-- <template #extra>
                <a-button type="primary">新增</a-button>
              </template> -->
              <div class="h-110px overflow-auto">
                <div style="border-bottom: 1px solid #ccc" v-for="item in weekList" :key="item.id" class="flex cursor-pointer">
                  <div class="w-90% hover:text-blue" @click="detailBtn(item)">{{ item.name }}</div>
                  <!-- <div class="hover:text-blue mr-10px" @click="editBtn(item)"><EditOutlined /></div>
                  <div class="hover:text-blue" @click="delBtn(item)"><DeleteOutlined /></div> -->
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="12" class="overflow-auto h-100%">
        <detail-info ref="detailInfoRef" />
      </a-col>
    </a-row>
  </div>
</template>
<script lang="ts" setup>
// import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue'
import { GetValList } from '@/servers/barcodeVariable'
// import { Modal, message } from 'ant-design-vue'
import DetailInfo from './components/detailInfo.vue'

const radixList = ref([])
const yearList = ref([])
const monthList = ref([])
const dayList = ref([])
const weekList = ref([])
const saveId = ref(0)
const detailInfoRef = ref()
onMounted(() => {
  getValList()
})

const getValList = async () => {
  const res = await GetValList({})
  radixList.value = res.data.radix || []
  yearList.value = res.data.year || []
  monthList.value = res.data.month || []
  dayList.value = res.data.day || []
  weekList.value = res.data.week || []
}

const detailBtn = (item) => {
  detailInfoRef.value.open(item.id)
}
// const editBtn = (item) => {
//   console.log(item)
// }

// const delBtn = (item) => {
//   saveId.value = item.id
//   Modal.confirm({
//     title: '确定要删除吗?',
//     icon: () => {},
//     content: '',
//     async onOk() {
//       delData()
//     },
//     onCancel() {},
//   })
// }

// const delData = () => {
//   const obj = { id: saveId.value }
//   Delete(obj).then((res) => {
//     if (res.success) {
//       message.success('已删除')
//       getValList()
//     } else {
//       message.error(res.msg)
//     }
//   })
// }
</script>
<style lang="scss" scoped></style>

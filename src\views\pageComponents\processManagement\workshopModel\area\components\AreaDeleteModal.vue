<template>
  <a-modal :open="open" title="删除区域" width="420px" @cancel="handleCancel">
    <span>{{ content }}</span>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" danger @click="handleSubmit">确认删除</a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { deleteWorkshopArea } from '@/servers/workshopArea'

const props = defineProps<{
  open: boolean
  initValue: { workshop_area_id: number }
}>()

const emits = defineEmits(['update:open', 'update'])

const content = '是否确认删除区域？删除前，请先删除关联该区域的所有数据，否则会操作失败！'

const handleCancel = () => {
  emits('update:open', false)
}

const handleSubmit = async () => {
  const res = await deleteWorkshopArea(props.initValue.workshop_area_id)
  message.success(res.message)
  emits('update:open', false)
  emits('update')
}
</script>

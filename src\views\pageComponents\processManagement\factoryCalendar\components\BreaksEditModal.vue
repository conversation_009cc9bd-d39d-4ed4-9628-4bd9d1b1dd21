<template>
  <a-modal :open="openModal" title="休息时间" :confirm-loading="confirmLoading" @cancel="handleCancel" :footer="isEditMode ? undefined : null">
    <div class="mt-24 mb-32">
      <div v-if="isEditMode" class="flex float-right mb-8">
        <a-button type="primary" class="mr-4" :icon="h(PlusOutlined)" @click="handleAddBreak()">添加</a-button>
        <a-button type="primary" danger :icon="h(DeleteOutlined)" @click="handleDeleteBreak()">删除</a-button>
      </div>

      <vxe-table
        :data="tableData"
        border
        stripe
        ref="tableRef"
        max-height="320px"
        min-height="0"
        class="table w-full"
        size="mini"
        show-overflow
        :edit-config="{ mode: 'row', trigger: 'click', showIcon: false, beforeEditMethod: isRowEditable }"
        :checkbox-config="{ checkMethod: isRowEditable }"
        @checkbox-all="selectChangeEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column v-if="isEditMode" type="checkbox" width="60"></vxe-column>
        <vxe-column field="start_break" title="开始时间(>=)" :edit-render="{}">
          <template #edit="{ row }">
            <a-time-picker v-model:value="row.start_break" format="HH:mm" value-format="HH:mm" :allow-clear="false" @click.stop="() => {}" @mousedown.stop="() => {}" />
          </template>
        </vxe-column>
        <vxe-column field="end_break" title="结束时间(<)" :edit-render="{}">
          <template #edit="{ row }">
            <a-time-picker v-model:value="row.end_break" format="HH:mm" value-format="HH:mm" :allow-clear="false" @click.stop="() => {}" @mousedown.stop="() => {}" />
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { h } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { cloneDeep } from '@/utils'
import { message } from 'ant-design-vue'

const props = defineProps<{
  openModal: boolean
  rowId: string
  type: string
  breaks: any
}>()

const isEditMode = computed(() => props.type === 'edit')
const isRowEditable = ({ row }) => row.editable // 表格是否可编辑
const tableRef = ref()
const tableData = ref<any>([])
const emits = defineEmits(['update:openModal', 'getBreakTimes'])
const confirmLoading = ref<boolean>(false)

// 获取表格选中数据
const currentSelectedRows = ref()
const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  currentSelectedRows.value = $table?.getCheckboxRecords() || []
}

const initBreakData = {
  start_break: '00:00',
  end_break: '00:00',
  editable: true,
}

const handleAddBreak = () => {
  tableData.value = [...tableData.value, cloneDeep(initBreakData)]
}
const handleDeleteBreak = () => {
  const removeProcessList = new Set(currentSelectedRows.value?.map((item) => item._X_ROW_KEY))
  tableData.value = [...tableData.value].filter((item) => !removeProcessList.has(item._X_ROW_KEY))
}

const handleCancel = () => {
  emits('update:openModal', false)
}

const handleSubmit = () => {
  if (!tableData.value?.length) {
    message.error('请添加休息时间！')
    return
  }
  const breakTimes = [...tableData.value]?.map((item) => {
    const { _X_ROW_KEY, ...reset } = item
    return reset
  })
  emits('update:openModal', false)
  emits('getBreakTimes', breakTimes, props.rowId)
}

onMounted(() => {
  if (props.breaks) {
    tableData.value = props.breaks
  }
})
</script>

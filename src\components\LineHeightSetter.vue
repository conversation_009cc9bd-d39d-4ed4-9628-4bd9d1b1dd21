<template>
  <a-dropdown :trigger="['click']">
    <template #overlay>
      <a-menu @click="handleMenuClick">
        <a-menu-item :class="{ active: type == 1 }" :key="1">
          <CheckOutlined :style="type != 1 ? `visibility: hidden;` : ''" class="icon" />
          <span class="iconfont icon-hanggao-zhongdeng icon"></span>
          中等
        </a-menu-item>
        <a-menu-item :class="{ active: type == 2 }" :key="2">
          <CheckOutlined :style="type != 2 ? `visibility: hidden;` : ''" class="icon" />
          <span class="iconfont icon-hanggao-gao icon"></span>
          高
        </a-menu-item>
        <a-menu-item :class="{ active: type == 3 }" :key="3">
          <CheckOutlined :style="type != 3 ? `visibility: hidden;` : ''" class="icon" />
          <span class="iconfont icon-hanggao-chaogao icon"></span>
          超高
        </a-menu-item>
      </a-menu>
    </template>
    <a-button>
      <span v-show="type == 1" class="iconfont icon-hanggao-zhongdeng"></span>
      <span v-show="type == 2" class="iconfont icon-hanggao-gao"></span>
      <span v-show="type == 3" class="iconfont icon-hanggao-chaogao"></span>
    </a-button>
  </a-dropdown>
</template>

<script setup lang="ts">
import { CheckOutlined } from '@ant-design/icons-vue'

const emits = defineEmits(['update:type'])
defineProps({
  type: {
    type: Number,
    default: 1,
  },
})
const handleMenuClick = (e) => {
  emits('update:type', e.key)
}
</script>

<style lang="scss" scoped>
.icon {
  margin-right: 8px;
}

::v-deep(.active) {
  color: #1890ff !important;
}

.ant-dropdown-trigger {
  width: 28px;
  height: 28px;
  padding: 0;
}
</style>

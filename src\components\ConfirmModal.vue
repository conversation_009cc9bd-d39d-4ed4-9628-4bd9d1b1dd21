<template>
  <a-modal :zIndex="10000" style="top: 25vh" :width="width" :closable="false" class="confirmModal" :title="null" :footer="null" v-model:open="visible">
    <div class="contentBox">
      <div class="title">
        <CloseCircleFilled v-show="type === 'del'" class="icon dangerIcon" />
        <ExclamationCircleFilled v-show="type === 'danger'" class="icon dangerIcon" />
        <CheckCircleFilled v-show="type === 'success'" class="icon successIcon" />
        <ExclamationCircleFilled v-show="type === 'warning'" class="icon warningIcon" />
        <span class="text">{{ title }}</span>
      </div>
      <div class="content">
        <div v-for="(item, index) in content" :key="index" class="contentText">
          <div class="text">{{ item.text }}</div>
          <div class="subText" v-for="(subText, subTextIndex) in item.subText" :key="subTextIndex">
            <div class="point"></div>
            <span>{{ subText }}</span>
          </div>
          <br v-if="index != content.length - 1" />
        </div>
      </div>
    </div>
    <div class="footer">
      <a-button v-show="cancelBtn" :disabled="loading" @click="visible = false">{{ cancelText }}</a-button>
      <a-button v-show="confirmBtn" :danger="type == 'del'" :loading="loading" @click="okFn()" type="primary">{{ primaryText }}</a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { CloseCircleFilled, ExclamationCircleFilled, CheckCircleFilled } from '@ant-design/icons-vue'

const loading = ref(false)
const type = ref(null)
const title = ref(null)
const content = ref<any[]>([])
const primaryText = ref(null)
const cancelText = ref(null)
const visible = ref(false)
const onOk = ref<any>(null)
const width = ref(450)
const cancelBtn = ref(true)
const confirmBtn = ref(true)
const confirm = (obj) => {
  loading.value = false
  if (obj.width) {
    width.value = obj.width
  } else {
    width.value = 450
  }
  onOk.value = obj.onOk
  type.value = obj.type
  content.value = obj.content
  cancelBtn.value = obj.cancelBtn !== false
  confirmBtn.value = obj.confirmBtn !== false
  primaryText.value = obj.primaryText ? obj.primaryText : '确定'
  cancelText.value = obj.cancelText ? obj.cancelText : '取消'
  title.value = obj.title
  visible.value = true
}
const close = () => {
  loading.value = false
  visible.value = false
}
const okFn = () => {
  loading.value = true
  onOk.value(close)
}
defineExpose({
  confirm,
})
</script>

<style lang="scss" scoped>
.confirmModal {
  .contentBox {
    .title {
      display: flex;
      gap: 10px;
      align-items: center;

      .text {
        font-size: 14px;
        font-weight: bold;
      }

      .icon {
        font-size: 20px;
      }

      .dangerIcon {
        color: #ff4d4f;
      }

      .successIcon {
        color: rgb(82 196 26);
      }

      .warningIcon {
        color: #faad14;
      }
    }

    .content {
      padding: 20px 32px;

      .contentText {
        font-size: 12px;

        // color: rgb(0 0 0 / 70%);

        .text {
          line-height: 30px;
        }

        .subText {
          display: flex;
          gap: 8px;
          align-items: flex-start;
          line-height: 24px;
          color: rgb(0 0 0 / 70%);

          .point {
            width: 6px;
            height: 6px;
            margin-top: 10px;
            background-color: #1890ff;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
}
</style>

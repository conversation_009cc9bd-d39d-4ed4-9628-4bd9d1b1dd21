<template>
  <a-drawer v-model:open="openShow" width="40vw" title="用户信息" :maskClosable="false">
    <div class="userBox">
      <div class="formItem">
        <div class="itemtitle">账号</div>
        <div class="itemInput">{{ userData.user_name }}</div>
      </div>
      <div class="formItem">
        <div class="itemtitle">用户名称</div>
        <div class="itemInput">{{ userData.real_name }}</div>
      </div>
      <div class="formItem">
        <div class="itemtitle">所属公司</div>
        <div class="itemInput">{{ userData.company }}</div>
      </div>
      <div class="formItem">
        <div class="itemtitle">所在部门</div>
        <div class="itemInput">{{ userData.department }}</div>
      </div>
      <div class="formItem">
        <div class="itemtitle">岗位</div>
        <div class="itemInput">{{ userData.jobtitlename }}</div>
      </div>
      <div class="tipsBox">如需修改用户信息或密码，请登录 OA 系统进行修改。</div>
      <div class="externalBox" v-if="userData.external_system_account?.purple_bird_account?.length > 0">
        <div class="title">外部系统账号</div>
        <div class="urlBox">
          紫鸟
          <span @click="urlBtn">下载浏览器</span>
        </div>
        <div class="cartBox" v-for="(item, index) in userData.external_system_account.purple_bird_account" :key="index">
          <div class="itemBox">
            <div class="itemTitle">企业名称</div>
            <div class="itemText">{{ item.enterprise_name }}</div>
          </div>
          <div class="itemBox">
            <div class="itemTitle">用户名</div>
            <div class="itemText">{{ item.account_number }}</div>
          </div>
          <div class="itemBox">
            <div class="itemTitle">密码</div>
            <div class="itemText">
              <span v-if="item.status">{{ item.password }}</span>
              <span class="passBtn" @click="item.status = true" v-else>查看密码</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-drawer>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { UserInfo } from '@/servers/User'

const openShow = ref(false)
const userData: any = ref({})

const urlBtn = () => {
  window.open('https://www.superbrowser.com/download/')
}

const open = () => {
  UserInfo().then((res) => {
    userData.value = res.data
    openShow.value = true
  })
}

defineExpose({
  open,
})
</script>
<style scoped>
.userBox {
  .formItem {
    display: flex;
    height: 40px;
    margin: 10px 0;
    font-size: 14px;
    color: black;

    .itemtitle {
      width: 100px;
      margin-right: 10px;
      text-align: end;
    }
  }

  .tipsBox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-top: 10px;
    font-size: 14px;
    color: #6e6e6e;
  }

  .externalBox {
    margin-top: 10px;
    color: black;

    .title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      padding: 0 10px;
      font-size: 16px;
      background-color: #f4f7fe;
    }

    .urlBox {
      display: flex;
      align-items: center;
      margin: 10px;
      margin-bottom: 20px;
      font-size: 14px;

      span {
        margin-left: 10px;
        color: #015bea;
        cursor: pointer;
      }
    }
  }
}

.cartBox {
  margin-bottom: 20px;

  .itemBox {
    display: flex;
    align-items: center;
    margin: 10px;

    .itemTitle {
      width: 100px;
      margin-right: 10px;
    }
  }

  .passBtn {
    color: #015bea;
    cursor: pointer;
  }
}
/* ::v-deep(.ant-drawer-body) {
  padding: 0;
} */
</style>

<template>
  <a-drawer :footer="false" v-model:open="detailVisible" :width="'45vw'" title="查看字典" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }" :bodyStyle="{ padding: '0' }">
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" header="" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="24">
                <p class="label">序号</p>
                <p class="value">{{ dataIndex }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">组织机构</p>
                <p class="value">{{ target.company_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">字典编码</p>
                <p class="value">{{ target.dictionary_code }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">数据标签</p>
                <p class="value">{{ target.key }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">数据键值</p>
                <p class="value">{{ target.value }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">状态</p>
                <p class="value">{{ target.status ? '启用' : '停用' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="24">
                <p class="label">备注</p>
                <div class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: '300px' }">
                    <template #title>
                      {{ target.remark }}
                    </template>
                    {{ target.remark }}
                  </a-tooltip>
                </div>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="其他信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="12">
                <p class="label">创建时间</p>
                <p class="value">{{ target?.creator?.time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.creator?.real_name }}</div>
                      <div>所在部门：{{ target?.creator?.department_name ? target?.creator?.department_name : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.creator?.position_name ? target?.creator?.position_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.creator?.real_name ? target?.creator?.real_name : '--' }}</span>
                      <span v-if="target?.creator?.department_name || target?.creator?.position_name" class="detailValueDescription">
                        （
                        <span v-if="target?.creator?.position_name">{{ target?.creator?.position_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.creator?.department_name">
                          {{ target?.creator?.department_name.length > 10 ? target?.creator?.department_name.slice(0, 10) + '...' : target?.creator?.department_name }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target?.modifier?.time }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target?.modifier?.real_name }}</div>
                      <div>所在部门：{{ target?.modifier?.department_name ? target?.modifier?.department_name : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占1</span>
                        位：{{ target?.modifier?.position_name ? target?.modifier?.position_name : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target?.modifier?.real_name ? target?.modifier?.real_name : '--' }}</span>
                      <span v-if="target?.modifier?.department_name || target?.modifier?.position_name" class="detailValueDescription">
                        （
                        <span v-if="target?.modifier?.position_name">{{ target?.modifier?.position_name }}&nbsp;|&nbsp;</span>
                        <span v-if="target?.modifier?.department_name">
                          {{ target?.modifier?.department_name.length > 10 ? target?.modifier?.department_name.slice(0, 10) + '...' : target?.modifier?.department_name }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { Details } from '@/servers/dataDictionaryItem'
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'

const dataIndex = ref(0)
// const typeArr = ref([
//   { label: '布尔型', value: 1 },
//   { label: '数字', value: 2 },
// ])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'
const activeKey = ref(['1', '2'])
const detailVisible = ref(false)
const detailloading = ref(false)

const target = ref<any>(null)
const open = (id, idx) => {
  dataIndex.value = idx
  target.value = null
  detailloading.value = true
  detailVisible.value = true

  Details({ id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
    })
    .catch(() => {
      detailloading.value = false
    })
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

.value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

import axios from 'axios'
// import md5 from 'js-md5';
import router from '@/router'
import { message } from 'ant-design-vue'
import { beforLogout, jumpUMCForLogin } from '@/utils'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  // 锐旭 10.1.19.82:90
  // 胜祖 10.1.19.91:16760
  baseURL: import.meta.env.VITE_APP_ENV === 'development' ? '/api' : import.meta.env.VITE_APP_BASE_API,
  withCredentials: true,
  // 超时
  timeout: 60000,
})

class HttpRequest {
  baseOptions = (params?: any, method?: string, header?: any): Promise<any> => {
    if (!params) params = {}
    if (!method) method = 'POST'

    let userData = localStorage.getItem('userData') || ('' as any)
    let Authorization: any = ''
    if (userData) {
      userData = JSON.parse(userData) as any
      Authorization = `${userData.login_token || ''}`
    }
    const { url, data, responseType = '', isFormData = false } = params
    const headers = { 'X-Session-ID': Authorization, ...header }
    headers.ip = window.location.origin
    if (isFormData) {
      headers['Content-Type'] = 'multipart/form-data'
      // headers['Accept'] = 'text/plain';
      // 'Accept': 'text/plain'
    }
    const option = {
      url,
      data: method === 'POST' ? data : {},
      params: method === 'GET' ? data : {},
      method,
      responseType,
      headers,
    }

    return new Promise((resolve, reject) => {
      service(option)
        .then((res) => {
          if (responseType === 'blob') {
            return resolve(res)
          }

          const result = res.data || {}
          if ((result.code === 0 || result.code === 1) && result.success) {
            return resolve(result)
          }

          message.error({ content: result.message || '请求失败', key: 'msg' })
          return reject(result)
        })
        .catch((err) => {
          if (err?.response?.data?.code === 1001) {
            message.error({ content: err.response.data.message || '登录过期，请重新登录~', key: 'msg' })
            beforLogout()
            setTimeout(() => {
              jumpUMCForLogin()
            }, 1000)
            return
          }
          if (err?.response?.data?.code === 20002) {
            console.log('--------------err?.response?.data------------------', err?.response?.data)
            message.error('当前用户已被禁用，请联系系统管理员')
            setTimeout(() => {
              beforLogout()
              jumpUMCForLogin()
            }, 1000)
            return
          }
          if (err?.response?.data?.code === 21008) {
            // 用户未配置角色
            router.push({ path: '/404' })
            return reject(err)
          }
          message.error({ content: err?.response?.data?.message || '请求异常', key: 'msg' })
          return reject(err)
        })
    })
  }
}

export const request = new HttpRequest().baseOptions

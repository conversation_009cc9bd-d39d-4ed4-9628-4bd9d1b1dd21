<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.DEPARTMENT_INFO" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.DEPARTMENT_INFO" :get-list="Department" :isIndex="true">
      <template #right-btn>
        <a-button type="primary" @click="syncData" :loading="syncLoading" v-if="btnPermission[130003]">同步部门</a-button>
      </template>
      <template #source="{ row }">
        <span>{{ row.source || 'UMC' }}</span>
      </template>
      <template #status="{ row }">
        <span>{{ row.status == 1 ? '启用' : '停用' }}</span>
      </template>
      <template #sync_time="{ row }">
        <span>{{ row.sync_time ? row.sync_time.slice(0, 16) : '' }}</span>
      </template>
      <template #sub_company="{ row }">
        <span>{{ row.sub_company?.name || '/' }}</span>
      </template>
      <template #parent="{ row }">
        <span v-if="row.parent?.name && row.parent?.type == 3">{{ row.parent?.name }}</span>
        <span v-else>/</span>
      </template>
      <template #company="{ row }">
        <span>{{ row.company?.name || '/' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button @click="detail(row)" class="btn" v-if="btnPermission[130001]">查看</a-button>
        </div>
      </template>
    </BaseTable>

    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { onMounted } from 'vue'
import { Department, SelectDepartment, SyncDepartment } from '@/servers/0rganization'
import { message } from 'ant-design-vue'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()
// 查看
const detailDrawerRef = ref()
const formArr: any = ref([
  {
    label: '请选择公司',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'company_id',
    onChange: (val) => {
      formArr.value.forEach((item) => {
        if (item.key === 'p_id') {
          item.value = null
          const arr = departCache.value.filter((v) => v.company_id === val)
          item.selectArr = arr.map((v) => {
            return {
              label: v.name,
              value: v.id,
            }
          })
        }
      })
    },
  },
  {
    label: '请选择上级部门',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'p_id',
  },
  {
    label: '同步时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['start_time', 'end_time'],
    placeholder: ['同步开始时间', '同步结束时间'],
  },
])
const departCache = ref([])
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.DEPARTMENT_INFO) {
    const arr: any[] = []
    obj.DEPARTMENT_INFO.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  getOptions()
  search()
  initScreening()
})

const getOptions = () => {
  SelectDepartment({ type: 2 }).then((res) => {
    formArr.value.forEach((item) => {
      if (item.key === 'company_id') {
        item.selectArr = res.data.map((v) => {
          return {
            label: v.name,
            value: v.id,
          }
        })
      }
    })
  })
  SelectDepartment({ type: 3 }).then((res) => {
    departCache.value = res.data
    // formArr.value.forEach((item) => {
    //   if (item.key === 'p_id') {
    //     item.selectArr = res.data.map((v) => {
    //       return {
    //         label: v.name,
    //         value: v.id,
    //       }
    //     })
    //   }
    // })
  })
}

const syncLoading = ref(false)
const syncData = () => {
  syncLoading.value = true
  SyncDepartment({})
    .then((res) => {
      syncLoading.value = false
      if (res.success) {
        message.success('同步成功!')
        search()
      }
    })
    .catch(() => {
      syncLoading.value = false
    })
}

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id)
}

const tableRef = ref()
const search = () => tableRef.value.search()
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

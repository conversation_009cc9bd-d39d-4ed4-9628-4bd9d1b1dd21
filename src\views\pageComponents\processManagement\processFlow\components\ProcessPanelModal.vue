<template>
  <a-modal :open="openModal" title="属性面板" :confirm-loading="confirmLoading" @cancel="handleSubmit" :maskClosable="false" :footer="isEditable ? undefined : null">
    <div class="mt-24 mb-32">
      <p>工序属性</p>
      <a-form ref="formRef" :model="formState" class="form-container" v-bind="formItemLayout">
        <a-form-item label="当前工序" name="current_process">
          {{ formState.process_name }}
        </a-form-item>
        <a-form-item label="工序特性" name="peculiarity_id" :rules="[{ required: true, message: '请选择工序特性' }]">
          <a-select v-if="isEditable" v-model:value="formState.peculiarity_id" :options="peculiarityTypeOptions" :field-names="{ label: 'key', value: 'id' }" placeholder="请选择工序特性" />
          <span v-else>{{ formState.peculiarity_key }}</span>
        </a-form-item>
        <a-form-item label="工序类型" name="process_type_id" :rules="[{ required: true, message: '请选择工序类型' }]">
          <a-select v-if="isEditable" v-model:value="formState.process_type_id" :options="categoryTypeOptions" :field-names="{ label: 'key', value: 'id' }" placeholder="请选择工序类型" />
          <span v-else>{{ formState.process_type_key }}</span>
        </a-form-item>
      </a-form>
      <p>工序参数</p>
      <vxe-table :data="tableData" border align="center" stripe ref="tableRef" max-height="320px" min-height="0" class="w-420 ml-16" size="mini" show-overflow>
        <vxe-column title="参数名称" field="parameter_id" width="180">
          <template #default="{ row }">
            <span v-if="isEditable">{{ parameterMap?.find((item) => item.id === row.parameter_id)?.parameter_name }}</span>
            <span>{{ row.parameter_name }}</span>
          </template>
        </vxe-column>
        <vxe-column title="参数值" field="parameter_value_id" min-width="220">
          <template #default="{ row, rowIndex }">
            <a-select
              v-if="isEditable"
              v-model:value="row.parameter_value_id"
              :options="parameterMap[rowIndex].values"
              :field-names="{ label: 'value', value: 'id' }"
              class="w-180"
              placeholder="请选择参数值"
            />
            <span v-else>{{ row.parameter_value }}</span>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { onMounted, computed } from 'vue'

const props = defineProps<{
  openModal: boolean
  type: string
  currentProcess: any
  parameterMap?: any
}>()

const emits = defineEmits(['update:openModal', 'getFlowInfo'])
const confirmLoading = ref<boolean>(false)
const isEditable = computed(() => props.type === 'edit')
const peculiarityTypeOptions = inject('peculiarityTypeOptions') // 工序特性下拉列表
const categoryTypeOptions = inject('categoryTypeOptions') // 工序类别下拉列表

const formState = reactive({
  process_name: '',
  peculiarity_id: '', // 工序特性id
  peculiarity_key: '', // 工序特性名称
  process_type_id: '', // 工序类型id
  process_type_key: '', // 工序类型名称
})
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
}

const tableData = ref()

const handleCancel = () => {
  emits('update:openModal', false)
}

const handleSubmit = () => {
  const attributes = {
    peculiarity_id: formState.peculiarity_id,
    process_type_id: formState.process_type_id,
    parameter: tableData.value?.map((item) => ({ parameter_id: item.parameter_id, parameter_value_id: item.parameter_value_id, values: item.values })),
  }
  emits('getFlowInfo', attributes)
  emits('update:openModal', false)
}

onMounted(() => {
  Object.assign(formState, { ...props.currentProcess })
  tableData.value = props.currentProcess.parameter
  console.log('panel tableData:', tableData.value)
})
</script>

<style lang="scss" scoped>
.form-container {
  :deep(.ant-select.ant-select-in-form-item) {
    width: 340px;
  }
}
</style>

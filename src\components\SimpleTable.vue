<template>
  <vxe-table
    :loading="tableLoading"
    :border="true"
    ref="tableRef"
    size="mini"
    :row-config="{ keyField: keyField || '_X_ROW_KEY', isHover: true }"
    :cell-config="{ height: px2(rowHeight) || px2(40) }"
    :custom-config="{ mode: 'popup' }"
    :data="data"
    :show-overflow="true"
    :show-header-overflow="true"
    :show-footer-overflow="true"
    :column-config="{ resizable: true }"
    class="tableBoxwidth"
    :checkbox-config="{ reserve: true }"
    @checkbox-all="selectChangeEvent"
    @checkbox-change="selectChangeEvent"
    min-height="0"
    stripe
    v-bind="$attrs"
    align="center"
  >
    <slot name="column">
      <vxe-column v-if="isCheckbox" type="checkbox" field="checkbox" width="50" fixed="left"></vxe-column>
      <vxe-column v-if="isIndex" type="seq" title="序号" field="seq" width="50" fixed="left"></vxe-column>
      <slot name="append"></slot>
      <template v-for="i in tableKey" :key="i.field">
        <vxe-column v-bind="i">
          <template v-if="$slots[i.field + '-header']" #header="attr">
            <slot :name="i.field + '-header'" v-bind="attr" :item="i" />
          </template>
          <template v-else-if="i.required" #header>
            <span class="required-text">{{ i.title }}</span>
            <span class="required">*</span>
          </template>
          <template v-if="$slots[String(i.field)]" #default="attr">
            <slot :name="i.field" v-bind="attr" :item="i" />
          </template>
        </vxe-column>
      </template>
    </slot>
  </vxe-table>
</template>

<script setup lang="ts">
import { VxeColumnProps, VxeTableInstance } from 'vxe-table'

import { px2 } from '@/utils'

const tableLoading = ref(false)
const tableRef = ref<VxeTableInstance>()

const props = withDefaults(
  defineProps<{
    data: any[]
    tableKey: VxeColumnProps[] | any[]
    isCheckbox?: boolean
    isIndex?: boolean
    keyField?: string
    rowHeight?: number
    checkCb?: any
  }>(),
  {
    data: () => [],
    tableKey: () => [],
  },
)

onMounted(() => {
  window.addEventListener('resize', updateWindowWidth)
})
const windowScreenWidth = ref(window.innerWidth)
const updateWindowWidth = () => {
  windowScreenWidth.value = window.innerWidth
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

const checkItemsArr = ref([] as any[])
const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  checkItemsArr.value = [...currentSelectedRows, ...otherSelectedRows]
  if (props.checkCb) props.checkCb(checkItemsArr.value)
}

defineExpose({ checkItemsArr, tableRef })
</script>
<style lang="scss" scoped>
.tableBox {
  position: relative;
  flex: 1;
  border-bottom: 1px solid #ddd;

  .toolbarBtn {
    position: absolute;
    right: 0;
    bottom: 100%;
    padding: 0;
    padding-bottom: 0.6em;
    margin-block: -5px;
  }

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // overflow-y: scroll;
    .editbtn {
      color: #1890ff;
      cursor: pointer;
    }

    .movesea {
      margin-left: 20px;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .pagination {
  }

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #409eff;
    }
  }
}

.required {
  color: #f56c6c;
}

.required-text {
  margin-right: 4px;
}
</style>

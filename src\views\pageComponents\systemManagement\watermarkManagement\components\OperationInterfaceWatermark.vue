<template>
  <a-drawer :footerStyle="{ paddingLeft: '24px' }" destroy-on-close @close="closePreview()" v-model:open="drwaerVisible" width="960" title="操作界面水印" placement="right" :maskClosable="false">
    <div class="dragMainBox">
      <a-spin v-show="drawerLoading" />
      <a-form :colon="false" :label-col="{ style: { width: '110px', marginRight: '20px' } }" v-show="!drawerLoading" ref="formRef" :model="formData">
        <a-form-item label="启用水印">
          <a-switch id="enable" v-model:checked="formData.enable" checked-children="启用" un-checked-children="停用" />
        </a-form-item>
        <div class="detailFormTitle">水印设置</div>
        <a-form-item
          :class="{ removeMarinBottom: formData.enable }"
          label="水印信息"
          name="textInfo"
          :rules="[{ required: formData.enable, message: '请设置水印信息', validator: validateTableNotEmpty }]"
        >
          <div id="selectedTagBox" class="selectedTagBox">
            <a-button
              class="moveAbledTag"
              :disabled="!formData.enable"
              v-for="(item, index) in formData.textInfo"
              :key="index"
              type="text"
              :style="{ background: [1, 2, 3, 4, 5, 6, 7].indexOf(item.type) != -1 ? 'rgb(214, 242, 255)' : item.type == 8 || item.type == 9 ? 'rgb(238, 239, 242)' : 'rgb(211, 243, 226)' }"
              style="cursor: move"
              size="small"
            >
              {{ item.label }}
              <CloseOutlined @click="formData.textInfo.splice(index, 1)" v-show="formData.enable" class="icon" />
            </a-button>
          </div>
        </a-form-item>
        <a-form-item v-if="formData.enable" label=" ">
          <div class="tagBox">
            <a-button
              size="small"
              style="margin-right: 4px; margin-bottom: 4px"
              v-show="
                [1, 2, 3, 4, 5, 6, 7].indexOf(item.type) != -1
                  ? formData.textInfo.findIndex((e) => e.type === item.type) === -1
                  : [8, 9].indexOf(item.type) != -1
                    ? formData.textInfo.filter((e) => e.type === item.type).length < 6
                    : true
              "
              @click="addTag(item)"
              v-for="(item, index) in tagOptions"
              :key="index"
              type="text"
              :style="{ background: [1, 2, 3, 4, 5, 6, 7].indexOf(item.type) != -1 ? 'rgb(214, 242, 255)' : item.type == 8 || item.type == 9 ? 'rgb(238, 239, 242)' : 'rgb(211, 243, 226)' }"
            >
              {{ item.label }}
              <PlusOutlined class="icon" />
            </a-button>
          </div>
          <a-input
            style="width: 240px; margin-top: 4px"
            @pressEnter="addNewTag"
            v-show="formData.textInfo.filter((e) => e.type == 10).length < 3"
            :maxlength="10"
            show-count
            v-model:value="addTagValue1"
            class="input"
            placeholder="自定义文本"
          >
            <template #suffix>
              <CheckOutlined @click="addNewTag" class="inputIcon" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="信息预览">
          <div v-if="previewInfo" v-html="previewInfo.replaceAll('/n换行', '<br>')"></div>
          <span v-else>--</span>
        </a-form-item>
        <a-form-item label="水印颜色">
          <a-radio-group
            @change="hasPreview ? preview() : null"
            :disabled="!formData.enable"
            id="colorType"
            v-model:value="formData.colorType"
            :options="[
              { label: '浅色', value: 1 },
              { label: '标准', value: 2 },
              { label: '深色', value: 3 },
            ]"
          />
        </a-form-item>
        <a-form-item label="水印疏密">
          <a-radio-group
            @change="hasPreview ? preview() : null"
            :disabled="!formData.enable"
            id="denseType"
            v-model:value="formData.denseType"
            :options="[
              { label: '稀疏', value: 1 },
              { label: '标准', value: 2 },
              { label: '密集', value: 3 },
            ]"
          />
        </a-form-item>
        <a-form-item label="水印字号">
          <a-radio-group
            @change="hasPreview ? preview() : null"
            :disabled="!formData.enable"
            id="sizeType"
            v-model:value="formData.sizeType"
            :options="[
              { label: '小号', value: 1 },
              { label: '标准', value: 2 },
              { label: '大号', value: 3 },
            ]"
          />
        </a-form-item>
        <div class="detailFormTitle">
          启用角色
          <span class="detailTitleDescription">可针对角色设置是否启用水印</span>
        </div>
        <a-form-item label="选择角色">
          <Transfer v-model:value="formData.roleIds" :options="rolesoptions" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button :loading="submitLoading || drawerLoading" style="margin-right: 10px" @click="submit" type="primary">保存</a-button>
      <a-button
        style="margin-right: 10px"
        @click="
          () => {
            hasPreview ? closePreview() : preview()
          }
        "
      >
        {{ hasPreview ? '关闭预览' : '水印预览' }}
      </a-button>
      <a-button
        @click="
          () => {
            closePreview()
            drwaerVisible = false
          }
        "
      >
        取消
      </a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { SaveUIConfig, GetWatermarkInfo, PreviewWatermark } from '@/servers/WaterMark'
import { PlusOutlined, CloseOutlined, CheckOutlined } from '@ant-design/icons-vue'
import Transfer from '@/components/Transfer.vue'
import Sortable from 'sortablejs'
import { UserInfo } from '@/servers/User'
import { message } from 'ant-design-vue'

import { cloneDeep } from 'lodash'
import eventBus from '@/utils/eventBus'

defineProps({
  rolesoptions: {
    type: Array<any>,
    default: [],
  },
})
const formRef = ref<any>()
const drwaerVisible = ref(false)
const drawerLoading = ref(false)
const submitLoading = ref(false)
const hasPreview = ref(false)
const tagOptions = ref<any>([
  { label: '用户名称', type: 1 },
  { label: '账号', type: 2 },
  { label: '公司名称', type: 3 },
  { label: '所在部门', type: 4 },
  { label: '岗位', type: 5 },
  { label: '日期', type: 6 },
  { label: '时间', type: 7 },
  { label: '/n换行', type: 8 },
  { label: ' | ', type: 9 },
])
const addTagValue1 = ref<any>(null)
const userData = ref<any>(null)
const formData = ref<any>({
  textInfo: [],
  colorType: null,
  denseType: null,
  sizeType: null,
  enable: false,
  roleIds: [],
  genre: null,
})
const previewInfo = ref<any>(null)
const open = () => {
  tagOptions.value = cloneDeep(tagOptions.value.filter((e) => e.type != 10))
  previewInfo.value = null
  hasPreview.value = false
  addTagValue1.value = null
  drawerLoading.value = true
  drwaerVisible.value = true
  GetWatermarkInfo({})
    .then((res) => {
      console.log(res.data)
      formData.value = {
        textInfo: res.data.ui.textInfo ? res.data.ui.textInfo : [],
        colorType: res.data.ui.colorType,
        denseType: res.data.ui.denseType,
        sizeType: res.data.ui.sizeType,
        enable: res.data.ui.enable,
        roleIds: res.data.ui_role_ids,
      }
      // if (formData.value.textInfo && formData.value.textInfo.length != 0) {
      //   formData.value.textInfo.forEach(x => {
      //     if (x.type === 10 && tagOptions.value.findIndex(e => e.label === x.label) === -1) {
      //       tagOptions.value.push(x)
      //     }
      //   })
      // }
      setTimeout(() => {
        onDrop()
      }, 0)
      drawerLoading.value = false
    })
    .catch(() => {
      drawerLoading.value = false
    })

  UserInfo().then((res) => {
    userData.value = res.data
    userData.value.company_name = (res.data.company ? res.data.company : '') + (res.data.customer_name ? res.data.customer_name : '')
  })
}
const onDrop = () => {
  const el = document.getElementById('selectedTagBox')
  new Sortable(el, {
    animation: 300,
    handle: '.moveAbledTag',
    delay: 10,
    group: 'shared',
    onEnd: (item) => {
      const { oldIndex, newIndex } = item
      const arr = cloneDeep(formData.value.textInfo)
      formData.value.textInfo = []
      const currRow = arr.splice(oldIndex, 1)[0]
      arr.splice(newIndex, 0, currRow)
      setTimeout(() => {
        formData.value.textInfo = cloneDeep(arr)
      }, 0)
    },
  })
}
const addTag = (item) => {
  formData.value.textInfo.push(item)
}
const addNewTag = () => {
  if (!addTagValue1.value) return
  formData.value.textInfo.push({ label: addTagValue1.value, type: 10 })
  addTagValue1.value = null
}
const submit = async () => {
  submitLoading.value = true
  try {
    await formRef.value.validateFields()
    SaveUIConfig(formData.value)
      .then(() => {
        message.success('修改成功')
        drwaerVisible.value = false
        submitLoading.value = false
        eventBus.emit('setWatermark')
        hasPreview.value = false
      })
      .catch(() => {
        submitLoading.value = false
      })
  } catch (error) {
    console.log(error)
    submitLoading.value = false
  }
}
const validateTableNotEmpty = () => {
  if (!formData.value.enable) {
    return Promise.resolve()
  }
  if (formData.value.textInfo.length === 0) {
    return Promise.reject('请设置水印信息')
  }
  return Promise.resolve()
}
const preview = () => {
  PreviewWatermark({ textInfo: formData.value.textInfo }).then((res) => {
    eventBus.emit('setWatermark', { textInfo: res.data.textInfo, denseType: formData.value.denseType, sizeType: formData.value.sizeType, colorType: formData.value.colorType })
  })
  hasPreview.value = true
}
const getPreviewInfo = () => {
  PreviewWatermark({ textInfo: formData.value.textInfo }).then((res) => {
    previewInfo.value = res.data.textInfo ? res.data.textInfo : ''
    if (hasPreview.value) {
      preview()
    }
  })
}
const closePreview = () => {
  if (hasPreview.value) {
    eventBus.emit('setWatermark')
    hasPreview.value = false
  }
}
watch(
  () => formData.value.textInfo,
  () => {
    try {
      formRef.value.validateFields(['textInfo'])
      getPreviewInfo()
    } catch (error) {
      console.log(error)
    }
  },
  { deep: true },
)
watch(
  () => formData.value.enable,
  () => {
    try {
      formRef.value.validateFields(['textInfo'])
    } catch (error) {
      console.log(error)
    }
  },
)
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.selectedTagBox {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: cender;
  min-height: 32px;
  padding: 4px 11px;
  border: 1px solid #dcdcdc;
  border-radius: 2px;

  .icon {
    font-size: 10px;
    color: #000000d9;
    cursor: pointer;
  }
}

.ant-form-item-has-error {
  .selectedTagBox {
    border: 1px solid #ff4d4f;
  }
}

.tagBox {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 8px;

  .icon {
    font-size: 12px;
    color: #000000d9;
  }

  .input {
    .inputIcon {
      cursor: pointer;

      &:hover {
        color: #1890ff;
      }
    }
  }
}

.textBtn {
  display: inline-block;
  margin-top: 4px;
  font-size: 14px;
  font-weight: normal;
  color: rgb(0 0 0 / 70%);
  color: #1890ff;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: #40a9ff;
  }
}

.detailFormTitle {
  padding: 6px 20px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #333;
  background: #f4f7fe;
  border-radius: 4px;
}

.detailTitleDescription {
  padding-left: 20px;
  font-size: 12px;
  font-weight: normal;
  color: rgb(0 0 0 / 50%);
}

.removeMarinBottom {
  margin-bottom: 0;
}
</style>

import { request } from './request'
// 条码配置管理-列表
export const GetConfigList = (data) => request({ url: '/api/businessBase/barcodeConfig/index', data })
// 条码配置管理-创建
export const Create = (data) => request({ url: '/api/businessBase/barcodeConfig/create', data })
// 条码配置管理-修改
export const Update = (data) => request({ url: '/api/businessBase/barcodeConfig/update', data })
// 条码配置管理-删除
export const Delete = (data) => request({ url: '/api/businessBase/barcodeConfig/delete', data })
// 条码配置管理-详情
export const Show = (data) => request({ url: '/api/businessBase/barcodeConfig/show', data })
// 条码配置管理-默认
export const Enable = (data) => request({ url: '/api/businessBase/barcodeConfig/enable', data })
// 获取下拉选项
export const GetOptions = (data) => request({ url: '/api/businessBase/barcodeConfig/loadBarcodeMetaOptions', data }, 'GET')
// 条码配置管理-预览
export const Preview = (data) => request({ url: '/api/businessBase/barcodeConfig/preview', data })
// 条码配置管理-暂时预览
export const PreviewTemp = (data) => request({ url: '/api/businessBase/barcodeConfig/previewTemp', data })
// 条码配置管理-根据配置类型获取数据项下拉
export const DataItemFields = (data) => request({ url: '/api/businessBase/barcodeConfig/dataItemFields', data })

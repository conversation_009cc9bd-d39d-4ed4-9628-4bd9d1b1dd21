import { defineStore } from 'pinia'
import { GetUserPreference, SetUserPreference } from '@/servers/User'

const useAppStore = defineStore('app', {
  state: () => ({
    orgaInfo: {},
    refreshTime: new Date().getTime(), // 刷新时间，当时间改变时即触发刷新
    lockRefresh: false,
    isOpenLog: 0, // 0不展开 1展开
  }),
  actions: {
    setOrgaInfo(info) {
      this.orgaInfo = info
    },
    setRefreshTime() {
      this.refreshTime = new Date().getTime()
    },
    setLockRefresh(bol) {
      this.lockRefresh = bol
    },
    changeLogOpenVisible(value) {
      this.isOpenLog = value
      SetUserPreference({ is_open_log: value })
    },
    async getLogOpenVisible() {
      const res = await GetUserPreference()
      this.isOpenLog = res?.data?.is_open_log
    },
  },
})

export default useAppStore

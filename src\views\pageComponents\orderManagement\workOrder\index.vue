<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.WORK_ORDER" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.WORK_ORDER" :get-list="GetList" :isIndex="true" :isCheckbox="true">
      <template #left-btn>
        <a-button type="primary">同步ERP</a-button>
      </template>
      <template #right-btn>
        <a-button type="primary" @click="tapAdd('add')" :icon="h(PlusOutlined)" v-if="btnPermission[510001]">新建工单信息</a-button>
      </template>

      <template #operate="{ row }">
        <div class="btnBox">
          <a-button @click="detail(row)" class="btn" v-if="btnPermission[510003]">查看</a-button>
          <a-dropdown>
            <a-button v-if="btnPermission[510002] || btnPermission[510004] || btnPermission[510005] || btnPermission[510006] || btnPermission[510007]">更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('compiler', row)" v-if="btnPermission[510002] && row.issue_status_text == '未下达' && row.order_status_text !== '强制关结'">编辑</a-menu-item>
                <a-menu-item @click="tapAdd('delete', row)" v-if="btnPermission[510004]">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
                <a-menu-item @click="issue(row)" v-if="btnPermission[510007] && row.order_status_text !== '强制关结'">下达</a-menu-item>
                <a-menu-item @click="tapAdd('cancel', row)" v-if="btnPermission[510005] && row.order_status_text !== '强制关结'">取消</a-menu-item>
                <a-menu-item @click="tapAdd('closure', row)" v-if="btnPermission[510006] && row.order_status_text !== '强制关结'">强制关结</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="1050px"
      :title="roleModuleType == 'add' ? '新建工单信息' : '编辑工单信息'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-row :gutter="24">
          <a-col class="gutter-row" :span="8">
            <a-form-item label="工单号" name="order_number">
              <span>{{ editForm.order_number || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="外部系统工单单号" name="external_order_number">
              <span>{{ editForm.external_order_number || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="工单类型" name="order_type">
              <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.order_type" placeholder="请选择" :options="typeOptions"></a-select>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="物料编码" name="material_number">
              <a-input v-model:value="editForm.material_number" placeholder="请输入" :maxlength="50" @click="handleSearchMaterial" @change="handleChangeMaterial" allow-clear />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="物料名称" name="material_name">
              <span>{{ editForm.material_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="计划数量" name="plan_nums">
              <a-input-number v-model:value="editForm.plan_nums" placeholder="请输入" class="w188px" :min="0" :precision="0" />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="排产优先等级" name="priority_level">
              <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.priority_level" placeholder="请选择" :options="levelOptions"></a-select>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="生产阶别" name="production_stage">
              <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.production_stage" placeholder="请选择" :options="stageOptions"></a-select>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="客户" name="customer_id">
              <a-input v-model:value="editForm.customer_id" placeholder="请输入" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="计划交货日期" name="plan_delivery_time">
              <a-date-picker show-time placeholder="请选择日期" v-model:value="editForm.plan_delivery_time" :valueFormat="'YYYY-MM-DD HH:mm:ss'" class="w188px" />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="预计开工日期" name="plan_start_time">
              <a-date-picker show-time placeholder="请选择日期" v-model:value="editForm.plan_start_time" :valueFormat="'YYYY-MM-DD HH:mm:ss'" @change="checkDate('start')" class="w188px" />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="预计完工日期" name="plan_end_time">
              <a-date-picker show-time placeholder="请选择日期" v-model:value="editForm.plan_end_time" :valueFormat="'YYYY-MM-DD HH:mm:ss'" @change="checkDate('end')" class="w188px" />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="销售单号" name="sale_order_number">
              <a-input v-model:value="editForm.sale_order_number" placeholder="请输入" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="16">
            <a-form-item label="备注" name="remark">
              <a-input v-model:value="editForm.remark" placeholder="请输入" :maxlength="200" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <!-- 下达工单 -->
    <issue-drawer ref="issueDrawerRef" @cancel="search" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <a-form ref="remarkFormRef" :model="remarkForm" :rules="remarkRules">
        <a-form-item label="备注" name="closure_remark" v-if="roleModuleType == 'closure'" :label-col="{ style: { flex: '0 0 50px' } }">
          <a-textarea v-model:value="remarkForm.closure_remark" placeholder="请输入强制关结工单的原因" :rows="4" />
        </a-form-item>
        <a-form-item label="备注" name="cancel_remark" v-if="roleModuleType == 'cancel'" :label-col="{ style: { flex: '0 0 50px' } }">
          <a-textarea v-model:value="remarkForm.cancel_remark" placeholder="请输入取消工单的原因" :rows="4" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
  <material-search v-if="openModal" v-model:openModal="openModal" @getMaterial="getMaterial" />
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { h, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Add, Delete, GetList, Update, GetOptions, Closure, Cancel } from '@/servers/workOrder'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import MaterialSearch from '@/views/pageComponents/processManagement/standardTime/components/MaterialSearchModal.vue'
import DetailDrawer from './components/DetailDrawer.vue'
import IssueDrawer from './components/IssueDrawer.vue'

const { btnPermission } = usePermission()
const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  material_number: [
    { required: true, trigger: ['change', 'blur'], message: '请选择物料编码' },
    {
      validator: () => validMaterialId(),
    },
  ],
  plan_nums: [{ required: true, trigger: ['change', 'blur'] }],
  order_type: [{ required: true, message: '请选择工单类型', trigger: ['change', 'blur'] }],
  priority_level: [{ required: true, message: '请选择排产优先等级', trigger: ['change', 'blur'] }],
}

const validMaterialId = () => {
  if (!editForm.material_number) {
    return Promise.reject()
  }
  return Promise.resolve()
}

const remarkRules: Record<string, Rule[]> = {
  closure_remark: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 200),
      message: '输入内容不可超过200字符',
    },
  ],
  cancel_remark: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 200),
      message: '输入内容不可超过200字符',
    },
  ],
}
const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const issueDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索工单号',
    value: '',
    type: 'input',
    key: 'numbers',
  },
  {
    label: '搜索外部系统工单号',
    value: '',
    type: 'input',
    key: 'external_order_numbers',
  },
  {
    label: '工单类型',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'order_type',
    multiple: true,
  },
  {
    label: '生产阶别',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'production_stage',
    multiple: true,
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '请输入物料编码',
    value: '',
    type: 'input',
    key: 'material_numbers',
  },
  {
    label: '工单状态',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'order_status',
    multiple: true,
  },
  {
    label: '工单来源',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'order_source',
    multiple: true,
  },
  {
    label: '下达状态',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'issue_status',
    multiple: true,
  },
])
const stageOptions = ref([])
const typeOptions = ref([])
const statusOptions = ref([])
const sourceOptions = ref([])
const issueStatusOptions = ref([])
const levelOptions = ref([])
const editForm = reactive({
  id: null,
  order_number: '',
  external_order_number: '',
  production_stage: null,
  material_id: null,
  material_number: '',
  material_name: '',
  order_type: null,
  plan_nums: 0,
  customer_id: '',
  plan_delivery_time: '',
  plan_start_time: null,
  plan_end_time: null,
  sale_order_number: '',
  remark: '',
  priority_level: null,
})
const remarkForm = reactive({
  closure_remark: '',
  cancel_remark: '',
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.WORK_ORDER) {
    const arr: any[] = []
    obj.WORK_ORDER.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(async () => {
  await getOptions()
  console.log(formArr.value, 'form')
  search()
  initScreening()
})

const getOptions = () => {
  GetOptions({}).then((res) => {
    stageOptions.value = res.data.production_stage || []
    typeOptions.value = res.data.order_type || []
    statusOptions.value = res.data.order_status || []
    sourceOptions.value = res.data.order_source || []
    issueStatusOptions.value = res.data.issue_status || []
    levelOptions.value = res.data.priority_level || []
    formArr.value.forEach((item) => {
      if (item.key === 'order_type') {
        item.selectArr = typeOptions.value
      } else if (item.key === 'production_stage') {
        item.selectArr = stageOptions.value
      } else if (item.key === 'order_status') {
        item.selectArr = statusOptions.value
      } else if (item.key === 'order_source') {
        item.selectArr = sourceOptions.value
      } else if (item.key === 'issue_status') {
        item.selectArr = issueStatusOptions.value
      }
    })
  })
}

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const tapAdd = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      editForm.id = null
      editForm.order_number = ''
      editForm.external_order_number = ''
      editForm.production_stage = null
      editForm.material_id = null
      editForm.material_number = ''
      editForm.material_name = ''
      editForm.order_type = null
      editForm.plan_nums = 0
      editForm.customer_id = ''
      editForm.plan_delivery_time = ''
      editForm.plan_start_time = null
      editForm.plan_end_time = null
      editForm.sale_order_number = ''
      editForm.remark = ''
      editForm.priority_level = null
      break
    case 'compiler':
      editForm.id = row.id
      editForm.order_number = row.order_number
      editForm.external_order_number = row.external_order_number
      editForm.production_stage = row.production_stage
      editForm.material_id = row.material_id || null
      editForm.material_number = row.material_number
      editForm.material_name = row.material_name
      editForm.order_type = row.order_type
      editForm.plan_nums = row.plan_nums
      editForm.customer_id = row.customer_id
      editForm.plan_delivery_time = row.plan_delivery_time
      editForm.plan_start_time = row.plan_start_time
      editForm.plan_end_time = row.plan_end_time
      editForm.sale_order_number = row.sale_order_number
      editForm.remark = row.remark
      editForm.priority_level = row.priority_level
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'delete':
      roleModuleType.value = 'delete'
      visibleData.isShow = true
      visibleData.title = '删除工单'
      visibleData.content = `此工单关联到多项业务流程，如任务分配、进度跟踪等。删除后，这些关联信息将被清除，可能会影响相关工作的正常进行。您是否确认要删除？`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    case 'cancel':
      roleModuleType.value = 'cancel'
      visibleData.isShow = true
      visibleData.title = '取消工单确认'
      visibleData.content = `“您确定要取消该工单吗？当前工单处于未下达状态，取消后：
        1.工单将状态改为‘已取消’，无法恢复至可下达状态；
        2.已录入的任务信息（如需求描述、资源分配）将冻结，仅支持查看；
        3.系统将记录取消原因，便于后续追溯。”`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      remarkForm.cancel_remark = ''
      visibleData.okFn = () => {
        cancelRole(row.id)
      }
      break
    case 'closure':
      roleModuleType.value = 'closure'
      visibleData.isShow = true
      visibleData.title = '强制关结工单'
      visibleData.content = `确认要强制关结该工单吗？一旦关结，工单状态将变为强制关结，所有未处理完的任务将被视为已完成，如有未记录的工作内容，可能会丢失。请谨慎操作！`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      remarkForm.closure_remark = ''
      visibleData.okFn = () => {
        closureRole(row.id)
      }
      break
    // case 'batchclosure':
    //   if (tableRef.value.checkItemsArr.length === 0) {
    //     message.info('请勾选需要强制关结的工单！')
    //     return
    //   }
    //   roleModuleType.value = 'closure'
    //   visibleData.isShow = true
    //   visibleData.title = '强制关结工单'
    //   visibleData.content = `确认要强制关结该工单吗？一旦关结，工单状态将变为强制关结，所有未处理完的任务将被视为已完成，如有未记录的工作内容，可能会丢失。请谨慎操作！`
    //   visibleData.confirmBtnText = '确定'
    //   visibleData.isCancelBtn = true
    //   visibleData.okType = 'danger'
    //   remarkForm.closure_remark = ''
    //   visibleData.okFn = () => {
    //     batchClosureRole()
    //   }
    //   break
    default:
      break
  }
}
// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id)
}

const issue = (row) => {
  issueDrawerRef.value?.open(row)
}

const tableRef = ref()
const formRef = ref()
const remarkFormRef = ref()
const search = () => tableRef.value.search()

// 新增
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Add(obj).then((res) => {
    if (res.success) {
      message.success('新增成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 编辑
const upRoleDate = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Update(obj).then((res) => {
    if (res.success) {
      message.success('修改成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  Delete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}

// 强制关结工单
const closureRole = async (id) => {
  try {
    await remarkFormRef.value.validateFields()
    Closure({ id, closure_remark: remarkForm.closure_remark })
      .then((res) => {
        if (res.success) {
          visibleData.isShow = false
          message.success('强制关结工单成功')
          search()
        } else {
          message.error(res.message)
        }
      })
      .catch(() => {
        visibleData.isShow = false
      })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const cancelRole = async (id) => {
  try {
    await remarkFormRef.value.validateFields()
    Cancel({ id, cancel_remark: remarkForm.cancel_remark })
      .then((res) => {
        if (res.success) {
          visibleData.isShow = false
          message.success('取消工单成功')
          search()
        } else {
          message.error(res.message)
        }
      })
      .catch(() => {
        visibleData.isShow = false
      })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const openModal = ref(false)
// 点击出现弹窗
const handleSearchMaterial = () => {
  openModal.value = true
}

// 获取物料信息
const getMaterial = (material) => {
  editForm.material_number = material.goods_code // 接口实际传值
  editForm.material_id = material.id // 接口实际传值
  editForm.material_name = material.goods_name
  formRef.value.validateFields(['material_number'])
}
// 清空物料信息
const handleChangeMaterial = () => {
  if (!editForm.material_number) {
    editForm.material_number = ''
    editForm.material_id = null
    editForm.material_name = ''
  }
}

const checkDate = (type) => {
  if (editForm.plan_end_time && editForm.plan_start_time) {
    if (new Date(editForm.plan_end_time) < new Date(editForm.plan_start_time)) {
      if (type == 'start') {
        editForm.plan_start_time = null
      } else {
        editForm.plan_end_time = null
      }
      message.error('预计完工时间不能小于计划开工时间！')
    }
  }
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 0;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

<template>
  <a-drawer :title="title" width="650px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" v-bind="formItemLayout">
      <a-form-item label="生产阶别" name="data_dictionary_item_id" :rules="[{ required: true, message: '请选择生产阶别' }]">
        <a-select v-model:value="formState.data_dictionary_item_id" :options="productStepOptions" placeholder="请选择生产阶别" />
      </a-form-item>
      <a-form-item label="物料编码" name="goods_code" :rules="[{ required: true, message: '请输入物料编码' }]">
        <a-input v-model:value="formState.goods_code" allow-clear autocomplete="off" placeholder="请输入物料编码" @click="handleSearchMaterial" @change="handleChangeMaterial">
          <template #suffix>
            <SearchOutlined />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item label="物料名称" name="goods_name" :rules="[{ required: true, message: '请输入物料名称' }]">
        <a-input v-model:value="formState.goods_name" disabled placeholder="请输入物料名称" />
      </a-form-item>
      <a-form-item label="型号规格" name="spec" :rules="[{ required: true, message: '请输入型号规格' }]">
        <a-input v-model:value="formState.spec" disabled placeholder="请输入型号规格" />
      </a-form-item>
      <a-form-item label="标准工时" name="standard_time" :rules="[{ required: true, message: '请输入标准工时' }]">
        <a-space>
          <a-input-number v-model:value="formState.standard_time" class="w-360" placeholder="请输入标准工时" :min="0" :precision="0" />
          秒(s)
        </a-space>
      </a-form-item>
      <a-form-item label="前置时间" name="lead_time" :rules="[{ required: true, message: '请输入前置时间' }]">
        <a-space>
          <a-input-number v-model:value="formState.lead_time" class="w-360" placeholder="请输入前置时间" :min="0" :precision="0" />
          秒(s)
        </a-space>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-input v-model:value.trim="formState.remark" allow-clear :maxlength="200" placeholder="请输入备注" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
  <material-search v-if="openModal" v-model:openModal="openModal" @getMaterial="getMaterial" />
</template>

<script lang="ts" setup>
import { watch, inject } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { viewStandardTime, addStandardTime, editStandardTime } from '@/servers/standardTime'
import MaterialSearch from './MaterialSearchModal.vue'

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    id: string
  }
}>()

// 生产阶别下拉选项
const productStepOptions = inject('productStepOptions')
const emits = defineEmits(['update:open', 'update'])

const isEditMode = computed(() => props.initValue.type === 'edit') // 是否编辑模式

const TYPE_MAP = {
  add: { title: '新建', api: addStandardTime },
  edit: { title: '编辑', api: editStandardTime },
}
const title = computed(() => `${TYPE_MAP[props.initValue.type]?.title}标准工时`)

const openModal = ref(false)
// 点击出现弹窗
const handleSearchMaterial = () => {
  openModal.value = true
}

// 获取物料信息
const getMaterial = (material) => {
  formState.material_id = material.id // 接口实际传值
  formState.goods_code = material.goods_code
  formState.goods_name = material.goods_name
  formState.spec = material.spec
}
// 清空物料信息
const handleChangeMaterial = () => {
  if (formState.goods_code === '') {
    formState.material_id = ''
    formState.goods_name = ''
    formState.spec = ''
  }
}

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
}
const formState = reactive({
  id: null,
  data_dictionary_item_id: null,
  material_id: '', // 物料ID
  goods_code: '', // 物料编码
  goods_name: '', // 物料名称
  spec: '',
  standard_time: '',
  lead_time: '',
  remark: '',
})

// 关闭drawer
const onClose = () => {
  formRef.value.resetFields()
  emits('update:open', false)
}

// 提交表单
const onSubmit = () => {
  // 表单检验
  formRef.value
    .validate()
    .then(async () => {
      const apiFn = TYPE_MAP[props.initValue.type]?.api
      const params = {
        id: formState.id,
        data_dictionary_item_id: formState.data_dictionary_item_id,
        material_id: formState.material_id,
        standard_time: formState.standard_time,
        lead_time: formState.lead_time,
        remark: formState.remark,
      }
      try {
        const fn = isEditMode.value ? apiFn(props.initValue.id, params) : apiFn(params)
        await fn
        message.success(`${TYPE_MAP[props.initValue.type]?.title}标准工时成功！`)
        emits('update:open', false)
        emits('update')
      } catch (error) {
        console.error(error)
      }
    })
    .catch((error: Error) => {
      console.error(error)
    })
}

watch(
  isEditMode,
  async () => {
    if (isEditMode.value) {
      const res = await viewStandardTime(props.initValue.id)
      Object.assign(formState, {
        ...res.data,
        data_dictionary_item_id: res.data?.data_dictionary_item_id,
        goods_code: res.data?.material?.goods_code,
        goods_name: res.data?.material?.goods_name,
        spec: res.data?.material?.spec,
      })
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<style lang="scss" scoped></style>

<template>
  <a-drawer :title="title" width="960px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" layout="inline" class="form-container" v-bind="formItemLayout">
      <a-col :span="10">
        <a-form-item label="工厂日历序号" name="id">
          <span v-if="initValue.type === 'add'">默认系统进行自动递增添加</span>
          <span>{{ formState.id }}</span>
        </a-form-item>
      </a-col>
      <a-col :span="14" />
      <a-col :span="10">
        <a-form-item label="日历名称" name="name" :rules="[{ required: true, message: '请输入日历名称' }]">
          <a-input v-model:value.trim="formState.name" allow-clear :maxlength="50" placeholder="请输入日历名称" />
        </a-form-item>
      </a-col>
      <a-col :span="4" class="ml-8">
        <a-form-item label="周六上班" name="is_saturday_work" v-bind="switchFormLayout">
          <a-switch v-model:checked="formState.is_saturday_work" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" />
        </a-form-item>
      </a-col>
      <a-col :span="4">
        <a-form-item label="周日上班" name="is_sunday_work" v-bind="switchFormLayout">
          <a-switch v-model:checked="formState.is_sunday_work" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" />
        </a-form-item>
      </a-col>
      <a-col :span="4">
        <a-form-item label="默认日历" name="is_default" v-bind="switchFormLayout">
          <a-switch v-model:checked="formState.is_default" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" />
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item label="开始月份" name="start_month" :rules="[{ required: true, message: '请选择开始月份' }]">
          <a-select v-model:value="formState.start_month" :options="MONTH_MAP" placeholder="请选择开始月份" />
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item label="结束月份" name="end_month" :rules="[{ required: true, message: '请选择结束月份' }]">
          <a-select v-model:value="formState.end_month" :options="MONTH_MAP" placeholder="请选择结束月份" />
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item label="周起始日" name="start_week">
          <a-select v-model:value="formState.start_week" :options="WEEK_MAP" allow-clear placeholder="请选择周起始日" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" name="remark" :label-col="{ span: 2 }" :wrapper-col="{ span: 21 }" class="remark-item">
          <a-input v-model:value.trim="formState.remark" allow-clear :maxlength="200" placeholder="请输入备注" />
        </a-form-item>
      </a-col>
    </a-form>

    <div class="drawer-title">班别信息</div>
    <a-flex justify="space-between" class="mb-8 flex items-center">
      <span class="text-black">工作时间</span>
      <span>
        <a-button type="primary" class="mr-4" :icon="h(PlusOutlined)" @click="handleAddClass()">添加</a-button>
        <a-button type="primary" danger :icon="h(DeleteOutlined)" @click="handleDeleteClass()">删除</a-button>
      </span>
    </a-flex>
    <vxe-table
      :data="tableData"
      border
      stripe
      ref="tableRef"
      max-height="320px"
      min-height="0"
      class="calendar-table table w-full"
      size="mini"
      show-overflow
      :checkbox-config="{ checkMethod: isRowEditable }"
      @cell-click="handleClickRow"
      @checkbox-all="selectChangeEvent"
      @checkbox-change="selectChangeEvent"
    >
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column field="class_id" title="班别">
        <template #default="{ row }">
          <a-select v-if="row.editable" v-model:value="row.class_id" placeholder="选择班别" :options="classOptions" class="w-90" />
          <span v-else>{{ classOptions?.find((item) => item.value === row.class_id)?.label }}</span>
        </template>
      </vxe-column>
      <vxe-column field="time_period_id" title="时段">
        <template #default="{ row }">
          <a-select v-if="row.editable" v-model:value="row.time_period_id" placeholder="选择时段" :options="timePeriodOptions" class="w-90" />
          <span v-else>{{ timePeriodOptionsValue?.find((item) => item.value === row.time_period_id)?.label }}</span>
        </template>
      </vxe-column>
      <vxe-column field="start_work" title="开始时间(>=)">
        <template #default="{ row }">
          <a-time-picker v-if="row.editable" v-model:value="row.start_work" format="HH:mm" value-format="HH:mm" :allow-clear="false" />
          <span v-else>{{ row.start_work }}</span>
        </template>
      </vxe-column>
      <vxe-column field="end_work" title="结束时间(<)">
        <template #default="{ row }">
          <a-time-picker v-if="row.editable" v-model:value="row.end_work" format="HH:mm" value-format="HH:mm" :allow-clear="false" />
          <span v-else>{{ row.end_work }}</span>
        </template>
      </vxe-column>
      <vxe-column field="is_across_days" title="是否跨天">
        <template #default="{ row }">
          <a-select v-if="row.editable" v-model:value="row.is_across_days" placeholder="选择是否跨天" :options="booleanOptions" class="w-90" />
          <span v-else>{{ row.is_across_days ? '是' : '否' }}</span>
        </template>
      </vxe-column>
      <vxe-column field="is_overtime" title="是否加班">
        <template #default="{ row }">
          <a-select v-if="row.editable" v-model:value="row.is_overtime" placeholder="选择是否加班" :options="booleanOptions" class="w-90" />
          <span v-else>{{ row.is_overtime ? '是' : '否' }}</span>
        </template>
      </vxe-column>
      <vxe-column field="breaks" title="休息时间">
        <template #default="{ row }">
          <a-button :disabled="!row.editable" size="small" :icon="h(PlusOutlined)" @click="handleEditBreaks(row)" />
        </template>
      </vxe-column>
    </vxe-table>

    <template #footer>
      <a-space>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
  <breaks-edit v-if="openModal" v-model:openModal="openModal" type="edit" :row-id="currentRowId" :breaks="currentBreaks" @getBreakTimes="getBreakTimes" />
</template>

<script lang="ts" setup>
import { h, watch, inject } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { VxeTableInstance } from 'vxe-table'
import { viewFactoryCalendar, addFactoryCalendar, editFactoryCalendar } from '@/servers/factoryCalendar'
import { cloneDeep } from '@/utils'
import { MONTH_MAP, WEEK_MAP } from '../types'
import BreaksEdit from './BreaksEditModal.vue'

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    id: string
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const isEditMode = computed(() => props.initValue.type === 'edit') // 是否编辑模式
const isRowEditable = ({ row }) => row.editable // 表格是否可编辑
const title = computed(() => `${TYPE_MAP[props.initValue.type]?.title}工厂日历`)

const TYPE_MAP = {
  add: { title: '新建', api: addFactoryCalendar },
  edit: { title: '编辑', api: editFactoryCalendar },
}

// 下拉选项
const classOptions: any = inject('classOptions') // 班别
const timePeriodOptionsValue: any = inject('timePeriodOptions') // 时段
const timePeriodOptions = computed(() => {
  const usedPeriods = tableData.value?.filter((row) => row.time_period_id && row._X_ROW_KEY !== currentEditRow.value?._X_ROW_KEY)?.map((row) => row.time_period_id)
  return timePeriodOptionsValue.value?.map((option) => ({
    ...option,
    disabled: usedPeriods?.includes(option.value),
  }))
})
const booleanOptions = [
  { value: 1, label: '是' },
  { value: 0, label: '否' },
]

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 17 },
}
const switchFormLayout = {
  labelCol: { span: 14 },
  wrapperCol: { span: 8 },
}
const formState = reactive({
  id: null,
  name: '',
  is_saturday_work: 0, // 0为否 1-为是
  is_sunday_work: 0, // 0为否 1-为是
  is_default: 0, // 0为否 1-为是
  start_month: 1,
  end_month: 1,
  start_week: 1,
  remark: '',
  worktimes: [],
})

const tableRef = ref<VxeTableInstance<any>>()
const tableData = ref<any>([])
const currentRowId = ref<string>('')
const currentBreaks = ref<any>()
const initTableData = {
  class_id: classOptions.value?.[0].value,
  time_period_id: '',
  start_work: '00:00',
  end_work: '00:00',
  is_across_days: 0,
  is_overtime: 0,
  breaks: [],
  editable: true,
}

// 获取当前编辑行
const currentEditRow = ref()
const handleClickRow = ({ row }) => {
  currentEditRow.value = row
}

// 获取表格选中数据
const currentSelectedRows = ref()
const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  currentSelectedRows.value = $table?.getCheckboxRecords() || []
}

// 添加班别
const handleAddClass = () => {
  tableData.value = [...tableData.value, cloneDeep(initTableData)]
}

// 删除班别
const handleDeleteClass = () => {
  const removeProcessList = new Set(currentSelectedRows.value?.map((item) => item._X_ROW_KEY))
  tableData.value = [...tableData.value].filter((item) => !removeProcessList.has(item._X_ROW_KEY))
}

// 编辑休息时间
const openModal = ref(false)
const handleEditBreaks = (row: any) => {
  openModal.value = true
  currentRowId.value = row._X_ROW_KEY
  currentBreaks.value = row.breaks
}
// 获取休息时间
const getBreakTimes = (list: any, id: string) => {
  const target = tableData.value?.find((item) => item._X_ROW_KEY === id)
  if (target) {
    target.breaks = [...list]
  }
}

// 关闭drawer
const onClose = () => {
  formRef.value.resetFields()
  emits('update:open', false)
}

// 提交表单
const onSubmit = () => {
  // 表单检验
  formRef.value
    .validate()
    .then(async () => {
      // 校验工作时间
      if (!tableData.value?.length) {
        message.error('请补充工作时间！')
        return
      }
      console.log('onSubmit tableData:', tableData.value)
      tableData.value?.forEach((item, index) => {
        if (!item.time_period_id) {
          message.error(`第${index + 1}条工作时间未选择时段`)
          throw new Error(`第${index + 1}条工作时间未选择时段`)
        }
      })

      const apiFn = TYPE_MAP[props.initValue.type]?.api
      const worktimes = [...tableData.value]
        // 工作时间需过滤掉存在id的数据（即之前的数据，编辑下存在）
        ?.filter((item) => !item.id)
        // 删除_X_ROW_KEY, editable多余判断字段
        ?.map((item) => {
          const { _X_ROW_KEY, editable, ...reset } = item
          return reset
        })
      const params = { ...formState, worktimes }
      try {
        const fn = isEditMode.value ? apiFn(props.initValue.id, params) : apiFn(params)
        await fn
        message.success(`${TYPE_MAP[props.initValue.type]?.title}工厂日历成功！`)
        emits('update:open', false)
        emits('update')
      } catch (error) {
        console.error(error)
      }
    })
    .catch((error: Error) => {
      console.error(error)
    })
}

watch(
  isEditMode,
  async () => {
    if (isEditMode.value) {
      const res = await viewFactoryCalendar(props.initValue.id)
      // 不传多余参数，避免出现bug
      const initCalendar = {
        id: res.data.id,
        name: res.data.name,
        is_saturday_work: res.data.is_saturday_work,
        is_sunday_work: res.data.is_sunday_work,
        is_default: res.data.is_default,
        start_month: res.data.start_month,
        end_month: res.data.end_month,
        start_week: res.data.start_week,
        remark: res.data.remark,
        worktimes: res.data.worktimes,
      }
      Object.assign(formState, initCalendar)
      tableData.value = res.data?.worktimes?.map((item) => ({
        ...item,
        editable: false,
        class_name: classOptions.value?.find((element) => Number(element.value) === item.class_id)?.label,
        time_period_name: timePeriodOptionsValue.value?.find((element) => Number(element.value) === item.time_period_id)?.label,
      }))
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<style lang="scss" scoped>
.form-container {
  margin-bottom: 24px;

  :deep(.ant-col) {
    margin-bottom: 10px;
  }
}

.remark-item {
  :deep(.ant-form-item-label) {
    margin-left: 14px;
  }
}

.calendar-table {
  :deep(.vxe-cell--valid-error-hint) {
    display: none;
  }
}
</style>

<template>
  <a-modal v-model:open="openModal" title="产品信息" @ok="showModal" centered @cancel="handleCancel" width="1200px">
    <template #footer>
      <div class="flex justify-end">
        <a-button type="primary" @click="handleSubmit">确定</a-button>
        <a-button type="default" @click="handleCancel">取消</a-button>
      </div>
    </template>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="all" tab="全部" />
    </a-tabs>
    <div class="search-form flex gap-8 mb-8">
      <div class="form-input flex w-100% gap-8">
        <a-input class="flex-1" v-model:value="formState.styleCode" allow-clear placeholder="搜索款式编码" />
        <a-input class="flex-1" v-model:value="formState.productCode" allow-clear placeholder="搜索产品编码" />
        <a-input class="flex-1" v-model:value="formState.productName" allow-clear placeholder="搜索产品名称" />
        <a-select class="flex-1" v-model:value="formState.productCategory" show-search placeholder="产品分类" :options="departmentOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.productGroup" show-search placeholder="产品分组" :options="positionOptions" :filter-option="customFilterOption" />
        <a-input class="flex-1" v-model:value="formState.specification" allow-clear placeholder="搜索规格型号" />
        <a-select class="flex-1" v-model:value="formState.productLabel" show-search placeholder="商品标签" :options="productLabelOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.versionNumber" show-search placeholder="版本号" :options="versionNumberOptions" :filter-option="customFilterOption" />
        <a-button class="btn" type="primary" @click="handleSearchProduct" style="margin-right: 10px">查询</a-button>
        <a-button class="btn" @click="handleReset">重置</a-button>
      </div>
    </div>
    <vxe-table :data="tableData" border stripe ref="tableRef" :sort-config="{ trigger: 'cell', remote: false }" height="400" show-overflow>
      <vxe-column type="radio" width="60" align="center"></vxe-column>
      <vxe-column field="styleCode" title="款式编码" width="120" sortable />
      <vxe-column field="productCode" title="产品编码" width="120" sortable />
      <vxe-column field="productName" title="产品名称" width="120" />
      <vxe-column field="productCategory" title="产品分类" width="120" sortable />
      <vxe-column field="productGroup" title="产品分组" width="120" sortable />
      <vxe-column field="specification" title="规格型号" width="120" sortable />
      <vxe-column field="productLabel" title="商品标签" width="120" sortable />
      <vxe-column field="versionNumber" title="版本号" width="120" sortable />
    </vxe-table>
    <div class="flex items-center my-8 flex-justify-end">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="tableParams.total"
          show-size-changer
          v-model:current="tableParams.page"
          v-model:pageSize="tableParams.pageSize"
          :page-size-options="pageSizeOptions"
          @change="handleChangePage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ tableParams.total }}</span>
        </span>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { customFilterOption } from '@/utils/index'

// 标签页key值
const activeKey = ref('all')
// 表单数据
const formState = ref({
  styleCode: '', // 款式编码
  productCode: '', // 产品编码
  productName: '', // 产品名称
  productCategory: '', // 产品分类
  productGroup: '', // 产品分组
  specification: '', // 规格型号
  productLabel: '', // 商品标签
  versionNumber: '', // 版本号
})
// 全部表格分页参数
const tableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 500,
})
const pageSizeOptions = ['20', '50', '100', '250']

// 表格数据
const tableData = ref([])
// 产品分类Option
const departmentOptions = ref([])
// 产品分组Option
const positionOptions = ref([])
// 商品标签Option
const productLabelOptions = ref([])
// 版本号Option
const versionNumberOptions = ref([])
// 是否显示弹窗
const openModal = ref(false)
// 显示弹窗
const showModal = () => {
  openModal.value = true
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}
// 确定
const handleSubmit = () => {
  openModal.value = false
}
// 查询
const handleSearchProduct = () => {}
// 重置
const handleReset = () => {
  Object.keys(formState).forEach((key) => {
    if (typeof formState[key] === 'string') {
      formState[key] = ''
    } else {
      formState[key] = undefined
    }
  })
}
// 切换分页
const handleChangePage = () => {}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>

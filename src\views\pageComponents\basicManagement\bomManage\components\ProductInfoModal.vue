<template>
  <a-modal v-model:open="openModal" title="产品信息" @ok="showModal" centered @cancel="handleCancel" width="1200px">
    <template #footer>
      <div class="flex justify-end">
        <a-button type="primary" @click="handleSubmit">确定</a-button>
        <a-button type="default" @click="handleCancel">取消</a-button>
      </div>
    </template>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="all" tab="全部" />
    </a-tabs>
    <div class="search-form flex gap-8 mb-8">
      <div class="form-input flex w-100% gap-8">
        <a-input class="flex-1" v-model:value="formState.styleCode" allow-clear placeholder="搜索款式编码" />
        <a-input class="flex-1" v-model:value="formState.productCode" allow-clear placeholder="搜索产品编码" />
        <a-input class="flex-1" v-model:value="formState.productName" allow-clear placeholder="搜索产品名称" />
        <a-select class="flex-1" v-model:value="formState.productCategory" show-search placeholder="产品分类" :options="departmentOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.productGroup" show-search placeholder="产品分组" :options="positionOptions" :filter-option="customFilterOption" />
        <a-input class="flex-1" v-model:value="formState.specification" allow-clear placeholder="搜索规格型号" />
        <a-select class="flex-1" v-model:value="formState.productLabel" show-search placeholder="商品标签" :options="productLabelOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.versionNumber" show-search placeholder="版本号" :options="versionNumberOptions" :filter-option="customFilterOption" />
        <a-button class="btn" type="primary" @click="handleSearchProduct" style="margin-right: 10px">查询</a-button>
        <a-button class="btn" @click="handleReset">重置</a-button>
      </div>
    </div>
    <vxe-table :data="tableData" border stripe ref="tableRef" :sort-config="{ trigger: 'cell', remote: false }" height="400" show-overflow>
      <vxe-column type="radio" width="60" align="center"></vxe-column>
      <vxe-column field="styleCode" title="款式编码" width="120" sortable />
      <vxe-column field="productCode" title="产品编码" width="130" sortable />
      <vxe-column field="productName" title="产品名称" width="130" />
      <vxe-column field="productCategory" title="产品分类" width="135" sortable />
      <vxe-column field="productGroup" title="产品分组" width="140" sortable />
      <vxe-column field="specification" title="规格型号" width="140" sortable />
      <vxe-column field="productLabel" title="商品标签" width="140" sortable />
      <vxe-column field="versionNumber" title="版本号" width="140" sortable />
    </vxe-table>
    <div class="flex items-center my-8 flex-justify-end">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="tableParams.total"
          show-size-changer
          v-model:current="tableParams.page"
          v-model:pageSize="tableParams.pageSize"
          :page-size-options="pageSizeOptions"
          @change="handleChangePage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ tableParams.total }}</span>
        </span>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { customFilterOption } from '@/utils/index'
import type { VxeTableInstance } from 'vxe-table'
import { message } from 'ant-design-vue'

const emit = defineEmits(['select'])

// 标签页key值
const activeKey = ref('all')
// 表格引用
const tableRef = ref<VxeTableInstance>()
// 表单数据
const formState = ref({
  styleCode: '', // 款式编码
  productCode: '', // 产品编码
  productName: '', // 产品名称
  productCategory: undefined, // 产品分类
  productGroup: undefined, // 产品分组
  specification: '', // 规格型号
  productLabel: undefined, // 商品标签
  versionNumber: undefined, // 版本号
})
// 全部表格分页参数
const tableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 500,
})
const pageSizeOptions = ['20', '50', '100', '250']

// 表格数据
const tableData = ref([
  {
    styleCode: 'OEM-000001',
    productCode: 'PRD001',
    productName: '智能手机X1',
    productCategory: '电子产品',
    productGroup: '手机',
    specification: '6.5英寸',
    productLabel: '热销',
    versionNumber: 'V1.0',
  },
  {
    styleCode: 'OEM-000002',
    productCode: 'PRD002',
    productName: '无线耳机Pro',
    productCategory: '电子产品',
    productGroup: '音频设备',
    specification: '降噪版',
    productLabel: '新品',
    versionNumber: 'V2.1',
  },
  {
    styleCode: 'OEM-000003',
    productCode: 'PRD003',
    productName: '智能手表S2',
    productCategory: '电子产品',
    productGroup: '可穿戴',
    specification: '1.4英寸',
    productLabel: '推荐',
    versionNumber: 'V1.5',
  },
  {
    styleCode: 'OEM-000004',
    productCode: 'PRD004',
    productName: '平板电脑T10',
    productCategory: '电子产品',
    productGroup: '电脑',
    specification: '10.1英寸',
    productLabel: '办公',
    versionNumber: 'V1.0',
  },
  {
    styleCode: 'OEM-000005',
    productCode: 'PRD005',
    productName: '蓝牙音箱Mini',
    productCategory: '电子产品',
    productGroup: '音响',
    specification: '便携式',
    productLabel: '热销',
    versionNumber: 'V1.2',
  },
  {
    styleCode: 'OEM-000006',
    productCode: 'PRD006',
    productName: '智能摄像头C1',
    productCategory: '安防产品',
    productGroup: '监控设备',
    specification: '1080P',
    productLabel: '家用',
    versionNumber: 'V2.0',
  },
  {
    styleCode: 'OEM-000007',
    productCode: 'PRD007',
    productName: '无线充电器Q5',
    productCategory: '电子产品',
    productGroup: '充电设备',
    specification: '15W快充',
    productLabel: '配件',
    versionNumber: 'V1.1',
  },
  {
    styleCode: 'OEM-000008',
    productCode: 'PRD008',
    productName: '游戏手柄G3',
    productCategory: '电子产品',
    productGroup: '游戏设备',
    specification: '无线版',
    productLabel: '游戏',
    versionNumber: 'V1.3',
  },
  {
    styleCode: 'OEM-000009',
    productCode: 'PRD009',
    productName: '智能门锁L8',
    productCategory: '智能家居',
    productGroup: '安全设备',
    specification: '指纹识别',
    productLabel: '智能',
    versionNumber: 'V2.2',
  },
  {
    styleCode: 'OEM-000010',
    productCode: 'PRD010',
    productName: '车载充电器D2',
    productCategory: '汽车用品',
    productGroup: '车载设备',
    specification: '双USB',
    productLabel: '车用',
    versionNumber: 'V1.0',
  },
])
// 产品分类Option
const departmentOptions = ref([])
// 产品分组Option
const positionOptions = ref([])
// 商品标签Option
const productLabelOptions = ref([])
// 版本号Option
const versionNumberOptions = ref([])
// 是否显示弹窗
const openModal = ref(false)
// 显示弹窗
const showModal = () => {
  openModal.value = true
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}
// 确定
const handleSubmit = () => {
  const selectedData = tableRef.value?.getRadioRecord()
  if (!selectedData) {
    message.warning('请选择一条产品信息')
    return
  }
  emit('select', selectedData)
  openModal.value = false
}
// 查询
const handleSearchProduct = () => {}
// 重置
const handleReset = () => {
  Object.keys(formState).forEach((key) => {
    if (typeof formState[key] === 'string') {
      formState[key] = ''
    } else {
      formState[key] = undefined
    }
  })
}
// 切换分页
const handleChangePage = () => {}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>

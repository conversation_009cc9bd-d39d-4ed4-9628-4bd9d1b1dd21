<template>
  <a-modal v-model:open="openModal" title="产品信息" @ok="showModal" centered @cancel="handleCancel" width="1200px">
    <template #footer>
      <div class="flex justify-end">
        <a-button type="primary" @click="handleSubmit">确定</a-button>
        <a-button type="default" @click="handleCancel">取消</a-button>
      </div>
    </template>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="all" tab="全部" />
    </a-tabs>
    <div class="search-form flex gap-8 mb-8">
      <div class="form-input flex w-100% gap-8">
        <a-input class="flex-1" v-model:value="formState.styleCode" allow-clear placeholder="搜索款式编码" />
        <a-input class="flex-1" v-model:value="formState.productCode" allow-clear placeholder="搜索产品编码" />
        <a-input class="flex-1" v-model:value="formState.productName" allow-clear placeholder="搜索产品名称" />
        <a-select class="flex-1" v-model:value="formState.productCategory" show-search placeholder="产品分类" :options="departmentOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.productGroup" show-search placeholder="产品分组" :options="positionOptions" :filter-option="customFilterOption" />
        <a-input class="flex-1" v-model:value="formState.specification" allow-clear placeholder="搜索规格型号" />
        <a-select class="flex-1" v-model:value="formState.productLabel" show-search placeholder="商品标签" :options="productLabelOptions" :filter-option="customFilterOption" />
        <a-select class="flex-1" v-model:value="formState.versionNumber" show-search placeholder="版本号" :options="versionNumberOptions" :filter-option="customFilterOption" />
        <a-button class="btn" type="primary" @click="handleSearchProduct" style="margin-right: 10px">查询</a-button>
        <a-button class="btn" @click="handleReset">重置</a-button>
      </div>
    </div>
    <vxe-table :data="tableData" border stripe ref="tableRef" :sort-config="{ trigger: 'cell', remote: false }" height="400" show-overflow>
      <vxe-column type="radio" width="60" align="center"></vxe-column>
      <vxe-column field="styleCode" title="款式编码" width="120" sortable />
      <vxe-column field="productCode" title="产品编码" width="120" sortable />
      <vxe-column field="productName" title="产品名称" width="120" />
      <vxe-column field="productCategory" title="产品分类" width="120" sortable />
      <vxe-column field="productGroup" title="产品分组" width="120" sortable />
      <vxe-column field="specification" title="规格型号" width="120" sortable />
      <vxe-column field="productLabel" title="商品标签" width="120" sortable />
      <vxe-column field="versionNumber" title="版本号" width="120" sortable />
    </vxe-table>
    <div class="flex items-center my-8 flex-justify-end">
      <div class="pagination">
        <a-pagination
          show-quick-jumper
          :total="tableParams.total"
          show-size-changer
          v-model:current="tableParams.page"
          v-model:pageSize="tableParams.pageSize"
          :page-size-options="pageSizeOptions"
          @change="handleChangePage"
        />
      </div>
      <div class="ml-8">
        <span>
          总数:
          <span class="page-number">{{ tableParams.total }}</span>
        </span>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { customFilterOption } from '@/utils/index'

// 标签页key值
const activeKey = ref('all')
// 表单数据
const formState = ref({
  styleCode: '', // 款式编码
  productCode: '', // 产品编码
  productName: '', // 产品名称
  productCategory: '', // 产品分类
  productGroup: '', // 产品分组
  specification: '', // 规格型号
  productLabel: '', // 商品标签
  versionNumber: '', // 版本号
})
// 全部表格分页参数
const tableParams = reactive({
  page: 1,
  pageSize: 20,
  total: 500,
})
const pageSizeOptions = ['20', '50', '100', '250']

// 表格数据
const tableData = ref([
  {
    id: 1,
    productCode: 'PRD001',
    productName: '智能手机X1',
    productVersion: 'V1.0',
    productCategory: '电子产品',
    productGroup: '智能设备',
    specification: '6.1英寸屏幕，128GB存储',
    basicUnit: '台',
    status: 1,
    remark: '主力产品',
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
  },
  {
    id: 2,
    productCode: 'PRD002',
    productName: '无线耳机Pro',
    productVersion: 'V2.1',
    productCategory: '电子产品',
    productGroup: '音频设备',
    specification: '降噪功能，续航30小时',
    basicUnit: '副',
    status: 1,
    remark: '热销产品',
    createTime: '2024-01-16 14:20:00',
    updateTime: '2024-01-16 14:20:00',
  },
  {
    id: 3,
    productCode: 'PRD003',
    productName: '智能手表S2',
    productVersion: 'V1.5',
    productCategory: '电子产品',
    productGroup: '可穿戴设备',
    specification: '1.4英寸AMOLED屏，防水IP68',
    basicUnit: '块',
    status: 1,
    remark: '新品推荐',
    createTime: '2024-01-17 09:15:00',
    updateTime: '2024-01-17 09:15:00',
  },
  {
    id: 4,
    productCode: 'PRD004',
    productName: '平板电脑T10',
    productVersion: 'V1.0',
    productCategory: '电子产品',
    productGroup: '计算设备',
    specification: '10.1英寸屏幕，256GB存储',
    basicUnit: '台',
    status: 1,
    remark: '办公首选',
    createTime: '2024-01-18 16:45:00',
    updateTime: '2024-01-18 16:45:00',
  },
  {
    id: 5,
    productCode: 'PRD005',
    productName: '蓝牙音箱Mini',
    productVersion: 'V1.2',
    productCategory: '电子产品',
    productGroup: '音频设备',
    specification: '便携式设计，续航12小时',
    basicUnit: '个',
    status: 1,
    remark: '便携音响',
    createTime: '2024-01-19 11:30:00',
    updateTime: '2024-01-19 11:30:00',
  },
  {
    id: 6,
    productCode: 'PRD006',
    productName: '智能摄像头C1',
    productVersion: 'V2.0',
    productCategory: '电子产品',
    productGroup: '安防设备',
    specification: '1080P高清，夜视功能',
    basicUnit: '台',
    status: 1,
    remark: '家用安防',
    createTime: '2024-01-20 13:25:00',
    updateTime: '2024-01-20 13:25:00',
  },
  {
    id: 7,
    productCode: 'PRD007',
    productName: '无线充电器Q5',
    productVersion: 'V1.1',
    productCategory: '电子产品',
    productGroup: '充电设备',
    specification: '15W快充，支持多设备',
    basicUnit: '个',
    status: 1,
    remark: '快充配件',
    createTime: '2024-01-21 08:50:00',
    updateTime: '2024-01-21 08:50:00',
  },
  {
    id: 8,
    productCode: 'PRD008',
    productName: '游戏手柄G3',
    productVersion: 'V1.3',
    productCategory: '电子产品',
    productGroup: '游戏设备',
    specification: '无线连接，震动反馈',
    basicUnit: '个',
    status: 1,
    remark: '游戏专用',
    createTime: '2024-01-22 15:10:00',
    updateTime: '2024-01-22 15:10:00',
  },
  {
    id: 9,
    productCode: 'PRD009',
    productName: '智能门锁L8',
    productVersion: 'V2.2',
    productCategory: '电子产品',
    productGroup: '智能家居',
    specification: '指纹识别，密码开锁',
    basicUnit: '套',
    status: 1,
    remark: '智能安全',
    createTime: '2024-01-23 12:40:00',
    updateTime: '2024-01-23 12:40:00',
  },
  {
    id: 10,
    productCode: 'PRD010',
    productName: '车载充电器D2',
    productVersion: 'V1.0',
    productCategory: '电子产品',
    productGroup: '车载设备',
    specification: '双USB接口，快充支持',
    basicUnit: '个',
    status: 1,
    remark: '车载必备',
    createTime: '2024-01-24 17:20:00',
    updateTime: '2024-01-24 17:20:00',
  },
])
// 产品分类Option
const departmentOptions = ref([])
// 产品分组Option
const positionOptions = ref([])
// 商品标签Option
const productLabelOptions = ref([])
// 版本号Option
const versionNumberOptions = ref([])
// 是否显示弹窗
const openModal = ref(false)
// 显示弹窗
const showModal = () => {
  openModal.value = true
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}
// 确定
const handleSubmit = () => {
  openModal.value = false
}
// 查询
const handleSearchProduct = () => {}
// 重置
const handleReset = () => {
  Object.keys(formState).forEach((key) => {
    if (typeof formState[key] === 'string') {
      formState[key] = ''
    } else {
      formState[key] = undefined
    }
  })
}
// 切换分页
const handleChangePage = () => {}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>

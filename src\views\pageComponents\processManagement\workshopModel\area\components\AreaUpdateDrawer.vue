<template>
  <a-drawer :title="title" width="650px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" v-bind="formItemLayout">
      <a-form-item
        label="区域编号"
        name="code"
        :rules="[
          { required: true, message: '请输入区域编号' },
          { pattern: /^[^\u4e00-\u9fa5]*$/, message: '不允许包含中文' },
        ]"
      >
        <span v-if="isEditMode">{{ formState.code }}</span>
        <a-input v-else v-model:value.trim="formState.code" :maxlength="30" placeholder="请输入区域编号" />
      </a-form-item>
      <a-form-item label="区域名称" name="name" :rules="[{ required: true, message: '请输入区域名称' }]">
        <a-input v-model:value.trim="formState.name" :maxlength="30" placeholder="请输入区域名称" />
      </a-form-item>
      <a-form-item label="生产阶别" name="data_dictionary_item_id" :rules="[{ required: true, message: '请选择生产阶别' }]">
        <a-select v-model:value="formState.data_dictionary_item_id" :options="productStepOptions" placeholder="请选择生产阶别" />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-switch v-model:checked="formState.status" :checkedValue="1" :unCheckedValue="0" checked-children="启用" un-checked-children="停用" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-input v-model:value.trim="formState.remark" allow-clear :maxlength="200" placeholder="请输入备注" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { message } from 'ant-design-vue'
import { addWorkshopArea, updateWorkshopArea, viewWorkshopArea, getSelectOptions } from '@/servers/workshopArea'

const props = defineProps<{
  open: boolean
  initValue: {
    company_id: string
    workshop_id: number
    type: string
    workshop_area_id: number
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const isEditMode = computed(() => props.initValue.type === 'edit') // 是否编辑模式

const TYPE_MAP = {
  add: { title: '新建', api: addWorkshopArea },
  edit: { title: '编辑', api: updateWorkshopArea },
}
const title = computed(() => `${TYPE_MAP[props.initValue.type]?.title}车间区域`)

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
}
const formState = reactive({
  company_id: props.initValue.company_id,
  workshop_id: props.initValue.workshop_id,
  code: '',
  name: '',
  data_dictionary_item_id: null,
  status: 0,
  remark: '',
})

// 关闭drawer
const onClose = () => {
  formRef.value.resetFields()
  emits('update:open', false)
}

// 提交表单
const onSubmit = () => {
  // 表单检验
  formRef.value
    .validate()
    .then(async () => {
      const apiFn = TYPE_MAP[props.initValue.type]?.api
      try {
        const fn = isEditMode.value ? apiFn(props.initValue.workshop_area_id, formState) : apiFn(formState)
        await fn
        message.success(`${TYPE_MAP[props.initValue.type]?.title}车间区域成功！`)
        emits('update:open', false)
        emits('update')
      } catch (error) {
        console.error(error)
      }
    })
    .catch((error: Error) => {
      console.error(error)
    })
}

watch(
  isEditMode,
  async () => {
    if (isEditMode.value) {
      const res = await viewWorkshopArea(props.initValue.workshop_area_id)
      Object.assign(formState, {
        code: res.data.code,
        name: res.data.name,
        data_dictionary_item_id: res.data.data_dictionary_item_id,
        status: res.data.status,
        remark: res.data.remark,
      })
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

const productStepOptions = ref()
/** 获取生产阶别下拉列表 */
const getProductStepOptions = async () => {
  const res = await getSelectOptions()
  productStepOptions.value = res.data?.production_level?.map((item) => ({
    value: item.id,
    label: item.key,
  }))
}

onMounted(() => {
  getProductStepOptions()
})
</script>

<style lang="scss" scoped></style>

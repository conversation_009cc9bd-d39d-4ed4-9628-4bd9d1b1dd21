// 用户模块
import { request } from './request'

// 原始登录接口
export const Login = (data) => {
  // return request({ url: '/api/Login/AdLogin', data }, 'GET');
  return request({ url: '/api/User/Login', data })
}

// 获取授权登陆连接
export const GetLoginUrl = () => {
  return request({ url: '/api/oauth/getAuthorizationUrl' }, 'GET')
}

// 在UMC登录成功后返回的token传给服务端获取用户信息
export const GetUserInfoByToken = () => {
  return request({ url: '/api/oauth/getUserInfo' }, 'GET')
}

// 获取UMC用户中心链接
export const GetUserCenterUrl = () => {
  return request({ url: '/api/oauth/getUserCenterUrl' }, 'GET')
}

// 获取用户账号列表
export const GetAccountList = () => {
  return request({ url: '/api/oauth/getAccountList' }, 'GET')
}

// 登出（获取跳转umc路径）
export const Logout = () => {
  return request({ url: '/api/oauth/logout' }, 'GET')
}

// 中间页请求登出
export const LogoutLocal = () => {
  return request({ url: 'api/oauth/localLogout' }, 'GET')
}

// 切换帐号
export const SwitchAccount = (data) => {
  return request({ url: '/api/oauth/switch', data })
}

// 获取系统信息
export const Info = () => {
  return request({ url: '/api/Info' }, 'GET')
}

// 获取用户详情
export const UserInfo = () => {
  return request({ url: 'api/personnel/user/userInfo' })
}
// 获取登录用户信息
export const GetUserInfo = (data) => {
  return request({ url: '/api/user/info', data }, 'GET')
}

// 获取用户偏好设置
export const GetUserPreference = () => {
  return request({ url: '/api/User/getUserPreference' }, 'GET')
}

// 设置用户偏好设置
export const SetUserPreference = (data) => {
  return request({ url: '/api/User/setUserPreference', data }, 'POST')
}

// 获取指定用户信息
export const GetUserInfoById = (data) => {
  return request({ url: '/api/User/getUserInfoById', data })
}

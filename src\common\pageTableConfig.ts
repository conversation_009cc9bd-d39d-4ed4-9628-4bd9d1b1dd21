import { PageType } from './enum'

export const pageTableConfig = {
  [PageType.ROLE_MANAGE]: [
    { key: 'role_name', name: '角色名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'created_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'updated_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],

  [PageType.ROLE_USERCONFIG]: [
    { key: 'account_name', name: '账号', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'work_num', name: '工号', width: 130, freeze: 0, is_show: true, is_sort: false },
    { key: 'real_name', name: '真实姓名', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'org_name', name: '组织', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'company_name', name: '公司', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'department_name', name: '部门', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'direct_leader', name: '直接上级', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'created_at', name: '创建时间', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'updated_at', name: '最近修改时间', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 200, freeze: 2, is_show: true },
  ],

  [PageType.USER_MANAGE]: [
    { key: 'account_id', name: '帐号', width: 200, freeze: 0, is_show: true },
    { key: 'user_name', name: '工号', width: 150, freeze: 0, is_show: true },
    { key: 'real_name', name: '真实姓名', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'enterprise', name: '所属企业', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'company', name: '所属公司', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'department', name: '所属部门', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'job_name', name: '所属岗位', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'permissions_role', name: '角色', width: 250, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'sync_time', name: '同步时间', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 120, freeze: 2, is_show: true },
  ],

  [PageType.PROCESS_PARAMTTERS]: [
    { name: '组织机构', key: 'company_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'stage_key', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '工序类别', key: 'category_key', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '参数编码', key: 'parameter_code', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '参数名称', key: 'parameter_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '参数类型', key: 'type_key', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '备注', key: 'remarks', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.PROCESS_INFO]: [
    { name: '组织机构', key: 'company_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '工序编码', key: 'process_code', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '工序名称', key: 'process_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'stage_key', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '工序类别', key: 'category_key', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '工序描述', key: 'describe', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'create_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.PROCESS_FLOW]: [
    { name: '组织机构', key: 'company_name', minWidth: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '工艺编码', key: 'craft_code', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '工艺名称', key: 'craft_name', minWidth: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'stage_key', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '工艺类别', key: 'craft_category_key', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '是否默认', key: 'default', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '前置工艺', key: 'pre_craft_name', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'create_name', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '修改人', key: 'updated_name', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '修改时间', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.PROCESS_GOODS_RELATE]: [
    { name: '商品主图', key: 'image', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '商品编码', key: 'goods_code', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '聚水潭编号', key: 'pool_code', minWidth: 120, freeze: 0, is_show: true, is_sort: false }, // 缺少字段
    { name: '商品名称', key: 'goods_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '规格型号', key: 'spec', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '款式编码', key: 'style_code', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '商品分类', key: 'goods_category_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '材质', key: 'texture', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '单位', key: 'unit', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '标准装箱数', key: 'packing_qty', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '默认供应商', key: 'default_vendor', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.WORK_ORDER]: [
    { name: '公司', key: 'company_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '工单号', key: 'order_number', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '外部系统工单号', key: 'external_order_number', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '排产优先等级', key: 'priority_level_text', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '工单状态', key: 'order_status_text', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '下达状态', key: 'issue_status_text', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '工单类型', key: 'order_type_text', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '物料编码', key: 'material_number', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '物料名称', key: 'material_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'production_stage_text', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '客户', key: 'customer_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '工单来源', key: 'order_source_text', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'created_by_text', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    {
      name: '实际开工时间',
      key: 'actual_start_time',
      width: 200,
      freeze: 0,
      is_show: true,
      is_sort: false,
    },
    {
      name: '实际完工时间',
      key: 'actual_end_time',
      width: 200,
      freeze: 0,
      is_show: true,
      is_sort: false,
    },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.CENTER_ENABLE]: [
    { name: '公司', key: 'company_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '派工单号', key: 'dispatch_order_number', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'order_status_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '工作中心编码', key: 'work_center_code', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '工作中心', key: 'work_center_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '物料编码', key: 'goods_code', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '物料名称', key: 'goods_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'production_stage_key', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '计划数量', key: 'plan_nums', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '上线时间', key: 'online_time', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'created_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '备注', key: 'remark', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.DISPATCH_ORDER]: [
    { name: '公司', key: 'company_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '派工单号', key: 'dispatch_order_number', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '物料编码', key: 'goods_code', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '物料名称', key: 'goods_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'order_status_name', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'production_stage_key', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '工单号', key: 'work_order_number', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'created_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '下达时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.ENTERPRISE_INFO]: [
    { name: '企业编号', key: 'number', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '企业名称', key: 'name', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '来源', key: 'source', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '同步时间', key: 'sync_time', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.COMPANY_INFO]: [
    { name: '公司编号', key: 'number', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '公司名称', key: 'name', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '所属企业', key: 'company', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '上级公司', key: 'parent', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '来源', key: 'source', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '同步时间', key: 'sync_time', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.DEPARTMENT_INFO]: [
    { name: '部门编号', key: 'number', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '部门', key: 'name', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '上级部门', key: 'parent', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '所属公司', key: 'sub_company', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '所属企业', key: 'company', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '来源', key: 'source', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '同步时间', key: 'sync_time', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.POST_INFO]: [
    { name: '组织机构', key: 'company', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '岗位名称', key: 'position_name', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '来源', key: 'source', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改', key: 'updated_at', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.USERPOST_SETTING]: [
    { name: '工号', key: 'work_num', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '真实姓名', key: 'real_name', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '组织', key: 'enterprise', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '公司', key: 'company_name', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '部门', key: 'department_name', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '直接上级', key: 'leader', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 0, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.DICTIONARY_LIST]: [
    { name: '组织机构', key: 'company', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '字典编码', key: 'code', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '字典名称', key: 'name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '是否内置', key: 'is_default', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '备注', key: 'remark', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.DICTIONARY_SETTING]: [
    { name: '组织机构', key: 'company', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '字典标签', key: 'key', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '字典键值', key: 'value', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '备注', key: 'remark', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.PARAMTTERS_LIST]: [
    { name: '组织机构', key: 'org_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '参数名称', key: 'param_name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '参数键名', key: 'param_code', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '参数键值', key: 'param_value', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '系统内置', key: 'is_builtin', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '备注', key: 'remark', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'create_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改', key: 'update_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  // [PageType.AREA_PERSON]: [
  //   { name: '组织机构', key: '组织机构', width: 120, freeze: 0, is_show: true, is_sort: false },
  //   { name: '车间', key: '车间', width: 200, freeze: 0, is_show: true, is_sort: false },
  //   { name: '区域', key: '区域', width: 120, freeze: 0, is_show: true, is_sort: false },
  //   { name: '生产阶别', key: '生产阶别', width: 120, freeze: 0, is_show: true, is_sort: false },
  //   { name: '工作人员', key: '工作人员', width: 120, freeze: 0, is_show: true, is_sort: false },
  //   { name: '岗位', key: '岗位', width: 120, freeze: 0, is_show: true, is_sort: false },
  //   { name: '创建人', key: '创建人', width: 120, freeze: 0, is_show: true, is_sort: false },
  //   { name: '创建时间', key: '创建时间', width: 200, freeze: 0, is_show: true, is_sort: false },
  //   { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  // ],

  [PageType.FACTORY_CALENDAR]: [
    { name: '组织结构', key: 'company_name', width: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '日历名称', key: 'name', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '周起始日', key: 'start_week', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '开始月份', key: 'start_month', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '结束月份', key: 'end_month', width: 120, freeze: 0, is_show: true, is_sort: false },
    { name: '周六上班', key: 'is_saturday_work', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '周日上班', key: 'is_sunday_work', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '默认日历', key: 'is_default', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'creator', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改时间', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.STANDARD_TIME]: [
    { name: '组织结构', key: 'company_name', minWidth: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'step_name', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '物料编码', key: 'goods_code', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '物料名称', key: 'goods_name', minWidth: 240, freeze: 0, is_show: true, is_sort: false },
    { name: '规格型号', key: 'spec', minWidth: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'creator', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改时间', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.WORKSHOP_PERSON]: [
    { name: '组织结构', key: 'company_name', minWidth: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '生产实体', key: 'workshop_name', minWidth: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '生产实体类型', key: 'workshop_type', minWidth: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '工作人员', key: 'user_name', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '岗位', key: 'position', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'created_name', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改时间', key: 'updated_at', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.BARCODE_CONFIG]: [
    { name: '组织结构', key: 'company_name', width: 220, minWidth: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '配置编码', key: 'code', minWidth: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '配置名称', key: 'name', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '配置类型', key: 'barcode_value', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '是否默认', key: 'is_default', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '备注', key: 'remark', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'created_name', width: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '最后修改时间', key: 'updated_at', width: 200, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.WORKSHOP]: [
    { name: '车间编号', key: 'code', minWidth: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '车间名称', key: 'name', minWidth: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'creator_name', minWidth: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', minWidth: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.WORKSHOP_AREA]: [
    { name: '区域编号', key: 'code', minWidth: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '区域名称', key: 'name', minWidth: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '所属车间', key: 'workshop_name', minWidth: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'dataDictionaryItem', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'creator_name', minWidth: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', minWidth: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],

  [PageType.WORKSHOP_CENTER]: [
    { name: '工作中心编号', key: 'code', minWidth: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '工作中心名称', key: 'workcenter_name', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '所属工序', key: 'process_name', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '所属区域', key: 'workshop_area_name', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '所属车间', key: 'workshop_name', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '生产阶别', key: 'stage_name', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 160, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'creator_name', minWidth: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', minWidth: 140, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
  [PageType.BOM_MANAGE]: [
    { name: '组织机构', key: 'company_name', width: 220, minWidth: 220, freeze: 0, is_show: true, is_sort: false },
    { name: '款式编码', key: 'goods_code', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '产品编码', key: 'product_code', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '产品名称', key: 'product_name', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '产品规格', key: 'product_spec', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '产品版本', key: 'product_version', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '状态', key: 'status', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '备注', key: 'remark', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '创建人', key: 'creator_name', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '创建时间', key: 'created_at', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '修改人', key: 'updated_name', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '修改时间', key: 'updated_at', width: 180, freeze: 0, is_show: true, is_sort: false },
    { name: '操作', key: 'operate', width: 200, freeze: 2, is_show: true, is_sort: false },
  ],
}

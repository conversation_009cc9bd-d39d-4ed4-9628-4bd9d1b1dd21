<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看用户"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <!-- <template #extra>
      <a-button @click="changeLogVisible">日志</a-button>
    </template> -->
    <div class="detailAllBox">
      <a-spin v-show="detailloading" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>
          <a-collapse-panel key="1" :header="`基本信息`" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="12">
                <p class="label">用户编号</p>
                <p class="value">{{ target.user_code || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">账号</p>
                <p class="value">{{ target.account_id || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">来源</p>
                <p class="value">{{ 'UMC' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">账号类型</p>
                <p class="value">{{ target.account_type == 0 ? '个人' : '企业' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">别名</p>
                <p class="value">{{ target.user_alias || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">真实姓名</p>
                <p class="value">{{ target.real_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">工号</p>
                <p class="value">{{ target.user_name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">邮箱</p>
                <p class="value">{{ target.email || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">所属企业</p>
                <p class="value">{{ target.enterprise?.name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">所属公司</p>
                <p class="value">{{ target.company?.name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">所属部门</p>
                <p class="value">{{ target.department?.name || '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">所属岗位</p>
                <p class="value">{{ target.job_name ? target.job_name : '--' }}</p>
              </a-col>
              <a-col class="gutter-row" :span="12">
                <p class="label">角色</p>
                <p class="value">{{ target.role ? target.role : '--' }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="其他信息" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="12">
                <p class="label">同步时间</p>
                <p class="value">{{ target.sync_time }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
      <!-- <LogDrawer ref="LogDrawerRef" class="log" v-if="!detailloading && appStore.isOpenLog" /> -->
    </div>
  </a-drawer>
  <!-- 日志 -->
</template>

<script lang="ts" setup>
import { DetailsInner } from '@/servers/UserManager'
import { DoubleRightOutlined } from '@ant-design/icons-vue'
import useAppStore from '@/store/modules/app'
// import LogDrawer from './LogDrawer.vue'

const activeKey = ref(['1', '2'])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'

const appStore = useAppStore()

const { btnPermission } = usePermission()
const LogDrawerRef = ref()
const detailloading = ref(false)
const detailVisible = ref(false)
const logVisble = ref(false)
const target = ref<any>({})
const options = ref({})
const open = (row, selectMap) => {
  console.log('open', row, selectMap)
  target.value = null
  detailVisible.value = true
  options.value = selectMap
  detailloading.value = true

  DetailsInner({ account_id: row.account_id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
      nextTick(() => {
        LogDrawerRef.value?.open(target.value)
      })
    })
    .catch(() => {
      detailloading.value = false
    })
}

// // 查看日志
// const looklog = () => {
//   LogDrawerRef.value.open(target.value.id)
// }

// const changeLogVisible = () => {
//   appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
//   if (appStore.isOpenLog) {
//     nextTick(() => {
//       LogDrawerRef.value?.open(target.value)
//     })
//   }
// }

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>

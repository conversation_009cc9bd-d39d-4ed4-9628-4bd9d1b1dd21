// 角色接口
import { request } from './request'
// 新增角色
export const Add = (data) => {
  return request({ url: '/api/System/Role/Add', data })
}
// 编辑角色
export const Update = (data) => {
  return request({ url: '/api/System/Role/Update', data })
}
// 停用/启用角色
export const UpdateRoleStatus = (data) => {
  return request({ url: '/api/System/Role/UpdateRoleStatus', data })
}
// 删除角色
export const Delete = (data) => {
  return request({ url: '/api/System/Role/Delete', data })
}
// 查询角色列表
export const GetList = (data) => {
  return request({ url: '/api/System/Role/GetList', data })
}
// 根据角色获取权限列表
export const GetListByRole = (data) => {
  return request({ url: '/api/System/Role/GetListByRole', data })
}
// 选择角色授权权限
export const SaveAuthRole = (data) => {
  return request({ url: '/api/System/Role/SaveAuthRole', data })
}
// 获取角色下拉框
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/System/Role/GetRoleSelectOption', data })
}
// 获取角色日志
export const GetOpLogInfos = (data) => {
  return request({ url: '/api/System/Role/GetOpLogInfos', data })
}
// 获取角色详情
export const Detail = (data) => {
  return request({ url: '/api/System/Role/Details', data })
}
// 获取角色详情
export const DetailsByEdit = (data) => {
  return request({ url: '/api/System/Role/DetailsByEdit', data })
}

// 获取角色用户分页列表
export const GetUserAssignList = (data) => {
  return request({ url: '/api/System/UserAssign/GetUserAssignList', data })
}

// 授权用户
export const UserAssign = (data) => {
  return request({ url: '/api/System/UserAssign/UserAssign', data })
}

// 取消授权
export const UserRevoke = (data) => {
  return request({ url: '/api/System/UserAssign/UserRevoke', data })
}

// 批量授权
export const UserBatchAssign = (data) => {
  return request({ url: '/api/System/UserAssign/UserBatchAssign', data })
}

// 批量取消
export const UserBatchRevoke = (data) => {
  return request({ url: '/api/System/UserAssign/UserBatchRevoke', data })
}

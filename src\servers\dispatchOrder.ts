import { request } from './request'
// 获取下拉选项
export const GetOptions = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/optionList', data }, 'GET')
// 获取列表
export const GetList = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/list', data })
// 查看
export const Details = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/detail', data })
// 上线
export const GoOnline = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/goOnline', data })
// 删除
export const Delete = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/delete', data })
// 暂停
export const Pause = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/pause', data })
// 强制完结工单
export const Closure = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/close', data })
// 下线
export const GoOffline = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/goOffline', data })
// 中心启用列表
export const CenterList = (data) => request({ url: '/api/manufacturingCenter/workDispatchOrder/centerList', data })

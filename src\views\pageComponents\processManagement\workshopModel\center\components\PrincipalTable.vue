<template>
  <vxe-table
    :data="tableData"
    border
    align="center"
    ref="tableRef"
    max-height="320px"
    min-height="0"
    class="principal-table table"
    size="mini"
    show-overflow
    :checkbox-config="{ checkMethod: isRowEditable }"
    @cell-click="handleClickRow"
  >
    <vxe-column field="class_id" title="班别" width="150">
      <template #default="{ row }">
        <span v-if="isViewMode">{{ classList?.find((item) => item.id === row.factory_calendar_worktime_id)?.key || '--' }}</span>
        <a-select v-else v-model:value="row.factory_calendar_worktime_id" placeholder="请选择" :options="classList" :field-names="{ label: 'key', value: 'id' }" />
      </template>
    </vxe-column>
    <vxe-column field="principal" title="主要负责人" width="150">
      <template #header>
        <span class="text-[#ff4d4f]">*</span>
        <span>主要负责人</span>
      </template>
      <template #default="{ row, rowIndex }">
        <span v-if="isViewMode">{{ row.principal_account_id || '--' }}</span>
        <div v-else class="search-input-form" @click="handleSelectUserModal(row.principal)" @mouseenter="isHovering = true" @mouseleave="isHovering = false">
          <span :class="row.principal ? 'text-[#606266]' : 'text-[#d3d3d3]'">
            {{ row.principal ? row.principal?.real_name : '请选择' }}
          </span>
          <span>
            <CloseCircleOutlined v-if="isHovering && row.principal" :style="{ color: '#d9d9d9' }" @click.stop="handleClearUser(rowIndex, 'principal')" />
            <SearchOutlined v-else :style="{ color: '#d9d9d9' }" />
          </span>
        </div>
      </template>
    </vxe-column>
    <vxe-column field="craft_principal" title="工艺负责人" width="150">
      <template #default="{ row, rowIndex }">
        <span v-if="isViewMode">{{ row.craft_account_id || '--' }}</span>
        <div v-else class="search-input-form" @click="handleSelectUserModal(row.craft_principal)" @mouseenter="isHovering = true" @mouseleave="isHovering = false">
          <span :class="row.craft_principal ? 'text-[#606266]' : 'text-[#d3d3d3]'">
            {{ row.craft_principal ? row.craft_principal?.real_name : '请选择' }}
          </span>
          <span>
            <CloseCircleOutlined v-if="isHovering && row.craft_principal" :style="{ color: '#d9d9d9' }" @click.stop="handleClearUser(rowIndex, 'craft_principal')" />
            <SearchOutlined v-else :style="{ color: '#d9d9d9' }" />
          </span>
        </div>
      </template>
    </vxe-column>
    <vxe-column field="equipment_principal" title="设备负责人" width="150">
      <template #default="{ row, rowIndex }">
        <span v-if="isViewMode">{{ row.equipment_account_id || '--' }}</span>
        <div v-else class="search-input-form" @click="handleSelectUserModal(row.equipment_principal)" @mouseenter="isHovering = true" @mouseleave="isHovering = false">
          <span :class="row.equipment_principal ? 'text-[#606266]' : 'text-[#d3d3d3]'">
            {{ row.equipment_principal ? row.equipment_principal?.real_name : '请选择' }}
          </span>
          <span>
            <CloseCircleOutlined v-if="isHovering && row.equipment_principal" :style="{ color: '#d9d9d9' }" @click.stop="handleClearUser(rowIndex, 'equipment_principal')" />
            <SearchOutlined v-else :style="{ color: '#d9d9d9' }" />
          </span>
        </div>
      </template>
    </vxe-column>
    <vxe-column field="quality_principal" title="品质负责人" width="150">
      <template #default="{ row, rowIndex }">
        <span v-if="isViewMode">{{ row.quality_account_id || '--' }}</span>
        <div v-else class="search-input-form" @click="handleSelectUserModal(row.quality_principal)" @mouseenter="isHovering = true" @mouseleave="isHovering = false">
          <span :class="row.quality_principal ? 'text-[#606266]' : 'text-[#d3d3d3]'">
            {{ row.quality_principal ? row.quality_principal?.real_name : '请选择' }}
          </span>
          <span>
            <CloseCircleOutlined v-if="isHovering && row.quality_principal" :style="{ color: '#d9d9d9' }" @click.stop="handleClearUser(rowIndex, 'quality_principal')" />
            <SearchOutlined v-else :style="{ color: '#d9d9d9' }" />
          </span>
        </div>
      </template>
    </vxe-column>
    <vxe-column v-if="!isViewMode" title="操作" width="140">
      <template #default="{ rowIndex }">
        <a-button type="primary" size="small" class="mr-4" @click="handleAddRow(rowIndex)">添加</a-button>
        <a-button v-if="tableData?.length > 1" type="primary" danger size="small" @click="handleRemoveRow(rowIndex)">删除</a-button>
      </template>
    </vxe-column>
  </vxe-table>

  <user-search v-if="openModal" v-model:openModal="openModal" type="single" :data="currentUser" :is-need-selected-tab="false" @getAccounts="getSelectedUser" />
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { SearchOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import { getFactoryCalendarClass } from '@/servers/workshopCenter'
import { cloneDeep } from '@/utils'
import UserSearch from '@/components/UserSearchModal.vue'

const props = defineProps<{
  type: string // 模式：view-查看，edit-编辑
  company_id: string
  principalList?: any
}>()

const isViewMode = computed(() => props.type === 'view') // 是否仅为查看模式
const isRowEditable = ({ row }) => row.editable // 表格是否可编辑
const isHovering = ref(false)

const tableRef = ref()
const initTableData = {
  workshop_work_center_id: '', // 工作中心关联ID
  factory_calendar_worktime_id: null, // 工厂日历ID
  principal: '', // 主要负责人ID
  craft_principal: '', //	工艺负责人ID
  equipment_principal: '', // 设备负责人ID
  quality_principal: '', // 品质负责人ID
}
const tableData = ref()

// 获取当前编辑行
const currentEditRow = ref()
const handleClickRow = ({ row, rowIndex, column }) => {
  currentEditRow.value = { row, rowIndex, field: column.field }
}

const openModal = ref(false)
const currentUser = ref()
/** 添加用户-显示弹窗 */
const handleSelectUserModal = (user?: any) => {
  openModal.value = true
  currentUser.value = user ? [{ ...user }] : []
}
/** 获取弹窗中选中的用户 */
const getSelectedUser = (userList) => {
  const { rowIndex, field } = currentEditRow.value
  tableData.value[rowIndex][field] = userList[0]
}
/** 清除用户 */
const handleClearUser = (rowIndex: number, field: string) => {
  tableData.value[rowIndex][field] = null
}

/** 表格添加下一行 */
const handleAddRow = (rowIndex: number) => {
  tableData.value.splice(rowIndex + 1, 0, cloneDeep(initTableData))
}
/** 表格删除该行 */
const handleRemoveRow = (rowIndex: number) => {
  tableData.value.splice(rowIndex, 1)
}

const classList = ref()
/** 获取班别列表 */
const getClassList = async () => {
  const res = await getFactoryCalendarClass()
  classList.value = res.data.class || []
}

onMounted(() => {
  getClassList()
})

watch(
  props,
  (newProps) => {
    if (newProps.principalList?.length) {
      tableData.value = [...newProps.principalList]?.map((item) => ({
        ...item,
        principal: { real_name: item.principal_account_id, account_id: `${item.principal}` },
        craft_principal: { real_name: item.craft_account_id, account_id: `${item.craft_principal}` },
        equipment_principal: { real_name: item.equipment_account_id, account_id: `${item.equipment_principal}` },
        quality_principal: { real_name: item.quality_account_id, account_id: `${item.quality_principal}` },
      }))
    } else {
      tableData.value = [cloneDeep(initTableData)]
    }
  },
  { immediate: true },
)

defineExpose({
  tableData,
})
</script>

<style lang="scss" scoped>
.principal-table {
  :deep(.ant-select) {
    width: 120px;
  }

  .search-input-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 125px;
    height: 28px;
    padding: 3px 11px;
    font-size: 12px;
    border: 1px solid #d3d3d3;
    border-radius: 4px;
  }
}
</style>

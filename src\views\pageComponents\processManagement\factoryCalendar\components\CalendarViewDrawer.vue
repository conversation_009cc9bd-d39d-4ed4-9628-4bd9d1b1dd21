<template>
  <a-drawer title="查看工厂日历" width="960px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" layout="inline" class="form-container" v-bind="formItemLayout">
      <a-col :span="10">
        <a-form-item label="工厂日历序号" name="id">
          <span>{{ formState.id }}</span>
        </a-form-item>
      </a-col>
      <a-col :span="14" />
      <a-col :span="10">
        <a-form-item label="日历名称" name="name">{{ formState.name }}</a-form-item>
      </a-col>
      <a-col :span="4" class="ml-8">
        <a-form-item label="周六上班" name="is_saturday_work" v-bind="switchFormLayout">
          {{ formState.is_saturday_work ? '是' : '否' }}
        </a-form-item>
      </a-col>
      <a-col :span="4">
        <a-form-item label="周日上班" name="is_sunday_work" v-bind="switchFormLayout">
          {{ formState.is_sunday_work ? '是' : '否' }}
        </a-form-item>
      </a-col>
      <a-col :span="4">
        <a-form-item label="默认日历" name="is_default" v-bind="switchFormLayout">
          {{ formState.is_default ? '是' : '否' }}
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item label="开始月份" name="start_month">{{ `${formState.start_month}月` }}</a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item label="结束月份" name="end_month">{{ `${formState.end_month}月` }}</a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item label="周起始日" name="start_week">{{ WEEK_MAP?.find((item) => item.value === formState.start_week)?.label }}</a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" name="remark" :label-col="{ span: 2 }" :wrapper-col="{ span: 21 }" class="remark-item">
          {{ formState.remark || '--' }}
        </a-form-item>
      </a-col>
    </a-form>

    <div class="drawer-title">班别信息</div>
    <a-flex justify="space-between" class="mb-8 flex items-center">
      <span class="text-black">工作时间</span>
    </a-flex>
    <vxe-table :data="tableData" border stripe ref="tableRef" max-height="320px" min-height="0" class="table w-full" size="mini" show-overflow>
      <vxe-column field="class_name" title="班别" />
      <vxe-column field="time_period_name" title="时段" />
      <vxe-column field="start_work" title="开始时间(>=)" />
      <vxe-column field="end_work" title="结束时间(<)" />
      <vxe-column field="is_across_days" title="是否跨天" />
      <vxe-column field="is_overtime" title="是否加班" />
      <vxe-column field="breaktimes" title="休息时间">
        <template #default="{ row }">
          <a-button type="link" @click="handleEditBreaks(row)">查看</a-button>
        </template>
      </vxe-column>
    </vxe-table>
    <div class="drawer-title mt-32">其他信息</div>
    <a-form :model="formState" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
      <a-form-item label="创建时间" name="created_at">
        <span>{{ formState.created_at }}</span>
      </a-form-item>
      <a-form-item label="创建人" name="creator">
        {{ formState.creator?.real_name }}
        <span class="user-info">{{ `（ ${formState.creator?.position_name} | ${formState.creator?.department_name} ）` }}</span>
      </a-form-item>
      <a-form-item label="最后修改时间" name="updated_at">
        {{ formState.updated_at }}
      </a-form-item>
      <a-form-item label="最后修改人" name="modifier">
        {{ formState.modifier?.real_name }}
        <span class="user-info">{{ `（ ${formState.modifier?.position_name} | ${formState.modifier?.department_name} ）` }}</span>
      </a-form-item>
    </a-form>
  </a-drawer>
  <breaks-edit v-if="openModal" v-model:openModal="openModal" type="view" :row-id="currentRowId" :breaks="currentRow.breaks" @getBreakTimes="getBreakTimes" />
</template>

<script lang="ts" setup>
import { inject } from 'vue'
import type { VxeTableInstance } from 'vxe-table'
import { viewFactoryCalendar } from '@/servers/factoryCalendar'
import BreaksEdit from './BreaksEditModal.vue'

const tableRef = ref<VxeTableInstance<any>>()
const classOptions: any = inject('classOptions')
const timePeriodOptions: any = inject('timePeriodOptions')

const props = defineProps<{
  open: boolean
  initValue: {
    id: number
  }
}>()

const WEEK_MAP = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']?.map((item, index) => ({ value: index + 1, label: item }))

const emits = defineEmits(['update:open', 'update'])

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 17 },
}
const switchFormLayout = {
  labelCol: { span: 14 },
  wrapperCol: { span: 8 },
}
const formState = reactive({
  id: null,
  name: '',
  is_saturday_work: 0, // 0为否 1-为是
  is_sunday_work: 0, // 0为否 1-为是
  is_default: 0, // 0为否 1-为是
  start_month: 1,
  end_month: 1,
  start_week: 1,
  remark: '',
  worktimes: [],
  created_at: '',
  creator: { real_name: '', position_name: '', department_name: '' },
  updated_at: '',
  modifier: { real_name: '', position_name: '', department_name: '' },
})

const tableData = ref<any>([])
const currentRowId = ref<string>('')
const currentRow = ref()

// 编辑休息时间
const openModal = ref(false)
const handleEditBreaks = (row: any) => {
  openModal.value = true
  currentRowId.value = row._X_ROW_KEY
  currentRow.value = row
}
// 获取休息时间
const getBreakTimes = (list: any, id: string) => {
  const target = tableData.value?.find((item) => item._X_ROW_KEY === id)
  if (target) {
    target.breaks = [...list]
  }
}

// 关闭drawer
const onClose = () => {
  emits('update:open', false)
}

const getFactoryCalendarDetail = async () => {
  const res = await viewFactoryCalendar(props.initValue.id)
  Object.assign(formState, res.data)
  tableData.value = res.data?.worktimes?.map((item) => ({
    ...item,
    class_name: classOptions.value?.find((element) => Number(element.value) === item.class_id)?.label,
    time_period_name: timePeriodOptions.value?.find((element) => Number(element.value) === item.time_period_id)?.label,
    is_across_days: item.is_across_days ? '是' : '否',
    is_overtime: item.is_overtime ? '是' : '否',
  }))
}

onMounted(() => {
  getFactoryCalendarDetail()
})
</script>

<style lang="scss" scoped>
.form-container {
  margin-bottom: 24px;

  :deep(.ant-col) {
    margin-bottom: 10px;
  }
}

.remark-item {
  :deep(.ant-form-item-label) {
    margin-left: 14px;
  }
}

.user-info {
  color: #8a8a8a;
}
</style>

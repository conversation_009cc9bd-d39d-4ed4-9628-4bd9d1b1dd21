<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.PROCESS_INFO" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.PROCESS_INFO" :get-list="GetList" :isIndex="true" :formFormat="formFormat">
      <template #right-btn>
        <a-button type="primary" @click="tapAdd('add')" :icon="h(PlusOutlined)" v-if="btnPermission[480001]">新建工序信息</a-button>
      </template>
      <template #created_at="{ row }">
        <span>{{ row.created_at ? row.created_at.slice(0, 16) : '' }}</span>
      </template>
      <template #updated_at="{ row }">
        <span>{{ row.updated_at ? row.updated_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row, rowIndex }">
        <div class="btnBox">
          <a-button @click="detail(row, rowIndex)" class="btn" v-if="btnPermission[480003]">查看</a-button>
          <a-dropdown>
            <a-button v-if="btnPermission[480002] || btnPermission[480004]">更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('compiler', row, rowIndex)" v-if="btnPermission[480002]">编辑</a-menu-item>
                <a-menu-item @click="tapAdd('removes', row)" v-if="btnPermission[480004]">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="520"
      :title="roleModuleType == 'add' ? '新建工序信息' : '编辑工序信息'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="序号" name="idx" v-if="roleModuleType == 'compiler'">
          <span>{{ idx }}</span>
        </a-form-item>
        <a-form-item label="生产阶别" name="stage_item_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.stage_item_id" placeholder="请选择" :options="stageOptions"></a-select>
        </a-form-item>
        <a-form-item label="工序类别" name="category_item_id">
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.category_item_id" placeholder="请选择" :options="categoryOptions"></a-select>
        </a-form-item>
        <a-form-item label="工序编码" name="process_code">
          <a-input v-model:value="editForm.process_code" placeholder="请输入" :maxlength="50" />
        </a-form-item>
        <a-form-item label="工序名称" name="process_name">
          <a-input v-model:value="editForm.process_name" placeholder="请输入" :maxlength="50" />
        </a-form-item>
        <a-form-item label="工序描述" name="describe">
          <a-textarea v-model:value="editForm.describe" placeholder="请输入" :maxlength="200" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { h, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Add, Delete, GetList, Update, GetOptions } from '@/servers/processInfo'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()
const formFormat = (data) => {
  return {
    ...data,
    sort_field: 'created_at',
    sort_asc: false,
  }
}
const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  stage_item_id: [{ required: true, message: '请选择生产阶别', trigger: ['change', 'blur'] }],
  category_item_id: [{ required: true, message: '请选择工序类别', trigger: ['change', 'blur'] }],
  process_code: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
  process_name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
}
const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索工序编码',
    value: null,
    type: 'input',
    key: 'process_code',
  },
  {
    label: '请输入工序名称',
    value: null,
    type: 'input',
    key: 'process_name',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'created_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'updated_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
  {
    label: '生产阶别',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'stage_item_id',
    multiple: true,
  },
  {
    label: '工序类别',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'category_item_id',
    multiple: true,
  },
])
const stageOptions = ref([])
const categoryOptions = ref([])
const idx = ref(0)

const editForm = reactive({
  id: null,
  stage_item_id: null,
  category_item_id: null,
  process_code: '',
  process_name: '',
  describe: '',
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.PROCESS_INFO) {
    const arr: any[] = []
    obj.PROCESS_INFO.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  getOptions()
  search()
  initScreening()
})

const getOptions = () => {
  GetOptions({}).then((res) => {
    stageOptions.value =
      res.data.stage_type.map((i) => {
        return {
          label: i.key,
          value: i.id,
        }
      }) || []

    categoryOptions.value =
      res.data.category_type.map((i) => {
        return {
          label: i.key,
          value: i.id,
        }
      }) || []

    formArr.value.forEach((item) => {
      if (item.key === 'stage_item_id') {
        item.selectArr = stageOptions.value
      } else if (item.key === 'category_item_id') {
        item.selectArr = categoryOptions.value
      }
    })
  })
}

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapAdd = (type: string, row: any = '', rowIndex: number = 0) => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      editForm.id = null
      editForm.stage_item_id = null
      editForm.category_item_id = null
      editForm.process_code = ''
      editForm.process_name = ''
      editForm.describe = ''
      break
    case 'compiler':
      idx.value = rowIndex + 1
      editForm.id = row.id
      editForm.stage_item_id = row.stage_id
      editForm.category_item_id = row.category_id
      editForm.process_code = row.process_code
      editForm.process_name = row.process_name
      editForm.describe = row.describe
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除工序信息'
      visibleData.content = `是否确认删除工序信息？删除前，请先删除关联该工序信息的所有数据，否则会操作失败！`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}
// 详情
const detail = (item, idx) => {
  detailDrawerRef.value?.open(item.id, idx + 1)
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

// 新增
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Add(obj).then((res) => {
    if (res.success) {
      message.success('新增成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 编辑
const upRoleDate = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  Update(obj).then((res) => {
    if (res.success) {
      message.success('修改成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  Delete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

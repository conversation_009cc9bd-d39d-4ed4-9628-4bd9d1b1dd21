<template>
  <a-drawer :title="title" width="960px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" layout="inline" class="form-container" v-bind="formItemLayout">
      <a-col :span="12">
        <a-form-item
          label="工作中心编号"
          name="code"
          :rules="[
            { required: true, message: '请输入工作中心编号' },
            { pattern: /^[^\u4e00-\u9fa5]*$/, message: '不允许包含中文' },
          ]"
        >
          <span v-if="isEditMode">{{ formState.code }}</span>
          <a-input v-else v-model:value.trim="formState.code" :maxlength="30" placeholder="请输入工作中心编号" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="工作中心名称" name="name" :rules="[{ required: true, message: '请输入工作中心名称' }]">
          <a-input v-model:value.trim="formState.name" :maxlength="30" placeholder="请输入工作中心名称" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="生产阶别" name="stage_name" :rules="[{ required: true }]">
          <a-input v-model:value="formState.stage_name" disabled />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="所属工序" name="process_information_id" :rules="[{ required: true, message: '请选择所属工序' }]">
          <a-select v-model:value="formState.process_information_id" :options="processOptions" :field-names="{ label: 'process_name', value: 'id' }" placeholder="请选择所属工序" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" name="remark" class="remark-item" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          <a-input v-model:value.trim="formState.remark" allow-clear :maxlength="200" placeholder="请输入备注" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formState.status" :checkedValue="1" :unCheckedValue="0" checked-children="启用" un-checked-children="停用" />
        </a-form-item>
      </a-col>
    </a-form>
    <div class="px-10">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="principal" tab="负责人信息">
          <principal-table ref="principalTableRef" type="edit" :company_id="initValue.company_id" :principal-list="formState.principals" />
        </a-tab-pane>
        <a-tab-pane key="equipment" tab="设备信息" forceRender>
          <equipment-table ref="equipmentTableRef" type="edit" :equipment-list="formState.equipment_data" />
        </a-tab-pane>
      </a-tabs>
    </div>

    <template #footer>
      <a-space>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { message } from 'ant-design-vue'
import { addWorkshopCenter, updateWorkshopCenter, viewWorkshopCenter, getProcessByStage } from '@/servers/workshopCenter'
import { viewWorkshopArea } from '@/servers/workshopArea'
import PrincipalTable from './PrincipalTable.vue'
import EquipmentTable from './EquipmentTable.vue'

const props = defineProps<{
  open: boolean
  initValue: {
    company_id: string
    workshop_id: number
    workshop_area_id: number
    type: string
    workcenter_id?: number
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const isEditMode = computed(() => props.initValue.type === 'edit') // 是否编辑模式

const activeKey = ref('principal')
const principalTableRef = ref()
const equipmentTableRef = ref()

const TYPE_MAP = {
  add: { title: '新建', api: addWorkshopCenter },
  edit: { title: '编辑', api: updateWorkshopCenter },
}
const title = computed(() => `${TYPE_MAP[props.initValue.type]?.title}工作中心`)

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 16 },
}
const formState = reactive({
  company_id: props.initValue.company_id,
  workshop_id: props.initValue.workshop_id,
  workshop_area_id: props.initValue.workshop_area_id,
  code: '',
  name: '',
  stage_name: '',
  process_information_id: null, // 工序ID
  status: 0,
  remark: '',
  principals: null, // 保存原始数据（包含用户id和name)
  principal_data: [],
  equipment_data: [],
})

const stageItem = ref()
/** 根据车间区域id查询生产阶别信息 */
const getStageByWorkshopArea = async () => {
  const res = await viewWorkshopArea(props.initValue.workshop_area_id)
  stageItem.value = res.data.dataDictionaryItem
  formState.stage_name = stageItem.value.key
  getProcessListByStage(stageItem.value.id)
}

const processOptions = ref()
/** 根据生产阶别获取工序 */
const getProcessListByStage = async (stage_id: number) => {
  const res = await getProcessByStage({ stage_id })
  processOptions.value = res.data.information
}

// 关闭drawer
const onClose = () => {
  formRef.value.resetFields()
  emits('update:open', false)
}

// 提交表单
const onSubmit = () => {
  // 表单检验
  formRef.value
    .validate()
    .then(async () => {
      const principalTableData = principalTableRef.value?.tableData || []
      if (principalTableData.length === 0) {
        return message.error('请填写负责人信息')
      }
      const invalidPrincipalIndex = principalTableData.findIndex((item) => !item?.principal)
      if (invalidPrincipalIndex !== -1) {
        return message.error(`第${invalidPrincipalIndex + 1}条未选择主要负责人`)
      }
      const principalList = principalTableData.map((item) => ({
        factory_calendar_worktime_id: item.factory_calendar_worktime_id,
        principal: item.principal?.account_id,
        craft_principal: item.craft_principal?.account_id,
        equipment_principal: item.equipment_principal?.account_id,
        quality_principal: item.quality_principal?.account_id,
      }))

      let equipmentList = equipmentTableRef.value?.tableData?.map(({ code, name }) => ({ code, name }))
      if (equipmentList.length > 0) {
        const invalidEquipmentIndex = equipmentList.findIndex(({ code, name }) => code === '' || name === '')
        if (invalidEquipmentIndex === 0 && equipmentList[0].code === '' && equipmentList[0].name === '') {
          equipmentList = []
        } else if (invalidEquipmentIndex !== -1) {
          return message.error(`第${invalidEquipmentIndex + 1}条未填写设备编码或设备名称`)
        }
      }

      Object.assign(formState, {
        principal_data: principalList,
        equipment_data: equipmentList,
      })
      const { principals, ...params } = formState // 无法直接delete，会报错

      const apiFn = TYPE_MAP[props.initValue.type]?.api
      try {
        const fn = isEditMode.value ? apiFn(props.initValue.workcenter_id, params) : apiFn(params)
        await fn
        message.success(`${TYPE_MAP[props.initValue.type]?.title}工作中心成功！`)
        emits('update:open', false)
        emits('update')
      } catch (error) {
        console.error(error)
      }
    })
    .catch((error: Error) => {
      console.error(error)
    })
}

watch(
  isEditMode,
  async () => {
    if (isEditMode.value) {
      const res = await viewWorkshopCenter(props.initValue.workcenter_id)
      const stage_item = res.data?.process_info?.stage_item
      Object.assign(formState, {
        code: res.data.code,
        name: res.data.name,
        process_information_id: res.data.process_information_id,
        status: res.data.status,
        remark: res.data.remark,
        principals: res.data.principals,
        equipment_data: res.data.equipment,
        stage_name: stage_item?.key,
      })
      getProcessListByStage(stage_item.id)
    } else {
      getStageByWorkshopArea()
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<style lang="scss" scoped>
.form-container {
  margin-bottom: 24px;

  :deep(.ant-col) {
    margin-bottom: 10px;
  }
}

.remark-item {
  :deep(.ant-form-item-label) {
    margin-left: 14px;
  }
}
</style>

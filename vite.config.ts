import vue from '@vitejs/plugin-vue'
import path from 'path'
import UnoCSS from 'unocss/vite'
import autoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv } from 'vite'
import ZipPack from 'vite-plugin-zip-pack'
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_BASE_API, VITE_APP_ENV, VITE_APP_VERSION } = env
  return {
    base: VITE_APP_ENV === 'production' ? './' : '/',
    plugins: [
      vue(),
      UnoCSS(),
      autoImport({
        imports: ['vue', 'vue-router'],
        eslintrc: {
          enabled: true, // <-- this
          globalsPropValue: true,
        },
        dirs: ['src/hook'],
      }),
      Components({
        resolvers: [AntDesignVueResolver({ importStyle: false })],
      }),
      ZipPack({
        outDir: './dist',
        outFileName: 'dist.zip',
        pathPrefix: 'dist',
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/assets/style/mixin.scss" as *;',
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@img': path.resolve(__dirname, 'src/assets/image'),
      },
    },
    server: {
      port: 8899,
      host: true,
      open: true,
      proxy: {
        '/api': {
          target: VITE_APP_BASE_API,
          changeOrigin: true,
          rewrite: (p) => p.replace(/\/api/, '/'),
        },
      },
    },
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-core': ['vue', 'vue-router'],
            'ant-design-vue': ['ant-design-vue'],
            charts: ['@antv/g2'],
            utils: ['dayjs', 'axios'],
            common: ['@/components/BaseTable.vue', '@/components/Form.vue'],
          },
          chunkFileNames: `js/[name]-[hash]-${VITE_APP_VERSION}.js`,
          entryFileNames: `js/[name]-[hash]-${VITE_APP_VERSION}.js`,
          assetFileNames: `[ext]/[name]-[hash]-${VITE_APP_VERSION}.[ext]`,
        },
      },
    },
  }
})

import { request } from './request'

// 获取工艺流程列表
export const getProcedureList = (data) => request({ url: '/api/manufacturingCenter/procedure/list', data })
// 新建工艺流程
export const addProcedure = (data) => request({ url: '/api/manufacturingCenter/procedure/create', data })
// 编辑工艺流程
export const editProcedure = (data) => request({ url: '/api/manufacturingCenter/procedure/update', data })
// 查看工艺流程
export const viewProcedure = (data) => request({ url: '/api/manufacturingCenter/procedure/show', data })
// 删除工艺流程
export const deleteProcedure = (data) => request({ url: '/api/manufacturingCenter/procedure/delete', data })
// 获取生产阶别/工艺类别下拉选项
export const getOptions = (data) => request({ url: '/api/manufacturingCenter/management/list', data }, 'GET')
// 获取前置工艺信息
export const getPreProcedureOptions = (data) => request({ url: '/api/manufacturingCenter/procedure/preInformation', data }, 'GET')
// 工艺管理-工序信息参数选项列表(根据生产阶别调出工序信息)
export const getCategoryByStage = (data) => request({ url: '/api/manufacturingCenter/management/informationList', data })
// 获取关联商品列表
export const getRelateGoodsList = (data) => request({ url: '/api/manufacturingCenter/procedure/goodsList', data })
// 获取关联商品列表表单查询下拉选项
export const getRelateGoodsOptions = (data) => request({ url: '/api/manufacturingCenter/management/relatedOptionList', data }, 'GET')
// 关联商品
export const relateGoods = (data) => request({ url: '/api/manufacturingCenter/procedure/goodsStore', data })
// 更新工艺流程状态、默认值
export const updateProcedureStatus = (data) => request({ url: '/api/manufacturingCenter/procedure/updateStatus', data })

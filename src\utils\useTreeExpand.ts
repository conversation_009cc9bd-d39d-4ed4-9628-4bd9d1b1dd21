import { ref, watch } from 'vue'

interface TreeNode {
  [key: string]: any
  children?: TreeNode[]
}

interface TreeExpandOptions {
  keyField?: string // 节点key字段名，默认 'key'
  childrenField?: string // 子节点字段名，默认 'children'
  defaultExpandAll?: boolean // 是否默认全部展开
}

export function useTreeExpand(treeData: TreeNode[], options: TreeExpandOptions = {}) {
  const { keyField = 'key', childrenField = 'children', defaultExpandAll = false } = options

  // 存储当前展开的节点keys
  const expandedKeys = ref<string[]>([])
  // 是否自动展开父节点
  const autoExpandParent = ref(true)

  // 收集所有节点的key
  const getAllKeys = (nodes: TreeNode[]): string[] => {
    if (!nodes || !nodes.length) return []

    return nodes.reduce<string[]>((keys, node) => {
      // 需过滤掉叶子节点
      if (node[childrenField] && node[childrenField].length > 0) {
        keys.push(node[keyField])
        keys.push(...getAllKeys(node[childrenField]))
      }
      return keys
    }, [])
  }

  // 展开全部节点
  const expandAll = () => {
    expandedKeys.value = getAllKeys(treeData)
    autoExpandParent.value = true
  }

  // 收起全部节点
  const collapseAll = () => {
    expandedKeys.value = []
    autoExpandParent.value = false
  }

  // 处理节点展开/收起
  const handleExpand = (keys: string[], { expanded, node }) => {
    if (!expanded) {
      expandedKeys.value = [...expandedKeys.value]?.filter((uniqueKey) => !uniqueKey?.includes(`${node.uniqueKey}-`))
    } else {
      expandedKeys.value = keys
    }
    autoExpandParent.value = false
  }

  // 初始化展开状态
  const initExpandState = () => {
    if (defaultExpandAll) {
      expandAll()
    } else {
      // 默认只展开第一级，需过滤掉叶子节点
      expandedKeys.value = treeData?.filter((node) => node.children && node.children.length > 0)?.map((node) => node[keyField])
    }
  }

  // 监听treeData变化重新初始化
  watch(
    () => treeData,
    (newVal) => {
      if (newVal && newVal.length) {
        initExpandState()
      }
    },
    { immediate: true, deep: true },
  )

  const init = (data) => {
    if (data && data.length) {
      treeData = data
      initExpandState()
    }
  }

  return {
    init,
    expandedKeys,
    autoExpandParent,
    expandAll,
    collapseAll,
    handleExpand,
  }
}

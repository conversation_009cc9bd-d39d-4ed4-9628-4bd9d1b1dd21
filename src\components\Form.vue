<template>
  <div v-if="showSetting" class="flex justify-between items-center mb-8">
    <div>
      <slot name="header"></slot>
    </div>
    <a-button :icon="h(SettingOutlined)" type="text" @click="emit('setting')">设置</a-button>
  </div>
  <div v-if="count">
    <div class="relative">
      <div class="formBox" v-show="!isConmmence">
        <div class="quickBox" v-if="isQuicks">
          <a-space>
            <template v-for="item in quicks" :key="item.key">
              <div class="quickLine" :style="{ width: item.width + 'px' }">
                <div class="quickLabel">{{ item.quickLabel || item.label }}：</div>
                <div class="quickContent">
                  <div
                    class="quickItem"
                    :class="{
                      active: !item.value || item?.value.length === 0,
                    }"
                    @click="handleQuick(item, null)"
                    v-if="!item.quickNotFull"
                  >
                    全部
                    <div :class="['checkIcon', { '!opacity-100': !item.value || item?.value.length === 0 }]"><CheckOutlined class="icon" /></div>
                  </div>

                  <template v-for="v in item.quicks ? item.quicks : item.selectArr" :key="v.value">
                    <div
                      class="quickItem"
                      :class="{
                        active: Array.isArray(item.value) ? item.value == v.value || item.value.includes(v?.value || v) : item.value == (v?.value || v),
                        // multiple: item.multiple
                      }"
                      @click="handleQuick(item, v)"
                    >
                      {{ v.label || v }}

                      <span v-if="v.count" :style="item.isCountRemind ? 'color: red' : ''">{{ item.isCountRemind ? '+' : '' }}{{ v.count }}</span>
                      <div :class="['checkIcon', { '!opacity-100': Array.isArray(item.value) ? item.value == v.value || item.value.includes(v?.value || v) : item.value == (v?.value || v) }]">
                        <CheckOutlined class="icon" />
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <div v-if="item.line" class="partingLine"></div>
            </template>
          </a-space>
        </div>
        <div class="flex flex-wrap">
          <template v-for="item in form" :key="item.label">
            <div class="item" v-show="item.isShow">
              <div class="li" v-if="item.type == 'inputDlg'" :style="{ width: item.width ? item.width + 'px' : '12.5rem' }">
                <a-input-group compact class="!flex">
                  <a-input v-model:value="item.value" :placeholder="item.label" allow-clear @blur="item.value = `${item.value || ''}`.trim()" @keyup.enter="showInputDlg(item)" />
                  <a-button @click="showInputDlg(item)" class="!flex items-center justify-center !w-40px">
                    <template #icon>
                      <span class="iconfont icon-chazhao_find text-14px c-#000"></span>
                    </template>
                  </a-button>
                </a-input-group>
              </div>
              <div class="li" v-if="item.type == 'input'">
                <a-input v-if="!item.showLabelToolTip" v-model:value="item.value" :placeholder="item.label" allow-clear v-bind="item" :maxlength="200" @keyup.enter="getList" />
                <a-tooltip v-else>
                  <template #title>{{ item.label }}</template>
                  <a-input v-model:value="item.value" :placeholder="item.label" allow-clear v-bind="item" :maxlength="200" @keyup.enter="getList" />
                </a-tooltip>
              </div>
              <div class="li" v-if="item.type == 'inputNumber'">
                <a-input-number
                  class="w-full"
                  v-model:value="item.value"
                  :placeholder="item.label"
                  allow-clear
                  v-bind="item"
                  :controls="false"
                  :max="999999999"
                  @change="changeInput(item)"
                  @keyup.enter="getList"
                />
              </div>
              <div class="li" v-if="item.type == 'select'">
                <a-select
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  v-model:value="item.value"
                  :mode="item.multiple ? 'multiple' : null"
                  style="width: 100%"
                  :placeholder="item.label"
                  maxTagCount="responsive"
                  :options="item.selectArr"
                  :filter-option="(input, option) => customFilterOption(input, option, item.selectKey)"
                  allowClear
                  v-bind="filterProps(item)"
                  @change="
                    (value, option) => {
                      item['onChange'] ? item['onChange'](value, option) : undefined
                    }
                  "
                ></a-select>
              </div>
              <div class="li" v-if="item.type == 'search'">
                <a-auto-complete
                  v-model:value="item.value"
                  style="width: 100%"
                  allowClear
                  :placeholder="item.label"
                  :options="item.searchArr || item.selectArr"
                  @search="handleSearch(item)"
                  v-bind="item"
                ></a-auto-complete>
              </div>
              <div class="li" v-if="item.type == 'select_one'">
                <a-select
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  style="width: 100%"
                  @change="
                    (value, option) => {
                      item['onChange'] ? item['onChange'](value, option) : undefined
                    }
                  "
                  :show-search="item.search ? item.search : false"
                  :filter-option="(input, option) => filterOption(input, option)"
                  v-model:value="item.value"
                  :placeholder="item.label"
                  :maxTagCount="1"
                  allowClear
                >
                  <template v-if="item.showDescription === 1" #dropdownRender="{ menuNode: menu }">
                    <v-nodes :vnodes="menu" />
                    <div style="padding: 8px 0; text-align: center">{{ item.description }}</div>
                  </template>
                  <a-select-option
                    v-for="(option, optionKey) in item.selectArr"
                    :key="optionKey"
                    :value="option[item.fieldNames ? item.fieldNames.value : 'value']"
                    :label="option[item.fieldNames ? item.fieldNames.label : 'label']"
                  >
                    <span>{{ option[item.fieldNames ? item.fieldNames.label : 'label'] }}</span>
                    <div v-show="item.subText && option.subText" style="width: 100%; overflow: hidden; font-size: 12px; color: rgb(0 0 0 / 50%); text-overflow: ellipsis; white-space: nowrap">
                      {{ option.subText }}
                    </div>
                  </a-select-option>
                </a-select>
              </div>
              <div class="li" v-if="item.type == 'select_Tree'">
                <a-tree-select
                  v-if="item.showDescription != 1"
                  v-model:value="item.value"
                  style="width: 100%"
                  :placeholder="item.label"
                  allow-clear
                  :tree-data="item.selectArr"
                  :fieldNames="item.fieldNames ? item.fieldNames : ''"
                  :maxTagCount="1"
                  :listHeight="400"
                  :dropdownMatchSelectWidth="250"
                  v-bind="item"
                />
                <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-else style="width: 100%" :placeholder="item.label">
                  <template #dropdownRender="{ menuNode: menu }">
                    <component :is="menu" />
                    <div style="padding: 8px 0; text-align: center">
                      {{ item.description }}
                    </div>
                  </template>
                </a-select>
              </div>
              <div class="li" v-if="item.type == 'cascader'">
                <a-cascader
                  v-model:value="item.value"
                  :options="item.selectArr"
                  :changeOnSelect="item.changeOnSelect || false"
                  :field-names="item?.fieldNames"
                  :expand-trigger="item.expandTrigger || 'click'"
                  :placeholder="item.label"
                  v-bind="filterProps(item)"
                  style="width: 100%"
                  @change="
                    (value, selectedOptions) => {
                      item['onChange'] ? item['onChange'](value, selectedOptions) : undefined
                    }
                  "
                />
              </div>
              <div class="li2" v-if="item.type == 'range-picker'">
                <div class="rangeBox">
                  <!-- <div class="labelText">{{ item.label }}:</div> -->
                  <div class="nubInputBox">
                    <a-range-picker
                      :placeholder="item.placeholder"
                      v-model:value="item.value"
                      :picker="item.picker"
                      :valueFormat="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
                      :show-time="item.showTime"
                      @change="
                        () =>
                          (item.value = [
                            dayjs(item.value[0])
                              .startOf('day')
                              .format(item.valueFormat || 'YYYY-MM-DD HH:mm:ss'),
                            dayjs(item.value[1])
                              .endOf('day')
                              .format(item.valueFormat || 'YYYY-MM-DD HH:mm:ss'),
                          ])
                      "
                      v-bind="item"
                    />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="flex items-center">
          <a-button type="primary" @click="getList" class="mr-8 w-70">查询</a-button>
          <a-button @click="resetForm" class="mr-8 w-70">重置</a-button>
          <a-button class="mr-8" @click="handleShowQuick">另存为快捷查询</a-button>
          <div v-if="shortcutQueryArr.length">
            <a-tag color="#ebebeb" closable @click="getForm(item)" @close.prevent="tapCloseRome(i)" v-for="(item, i) in shortcutQueryArr" :key="item.label" class="pointer">
              {{ item.label || '快捷查询' + (i + 1) }}
            </a-tag>
          </div>
        </div>
      </div>
      <div class="unfold-box">
        <div class="unfold">
          <div class="lin1"></div>
          <a-button class="unfold-btn" size="small" type="primary" @click="isConmmence = !isConmmence">{{ isConmmence ? '展开' : '收起' }}</a-button>
          <div class="lin2"></div>
        </div>
      </div>
    </div>
    <div class="btnlist">
      <slot name="btnlist"></slot>
    </div>

    <a-modal :width="350" @afterOpenChange="formRef.clearValidate()" v-model:open="isRecordModal" title="另存为快捷查询" okText="确定" cancelText="取消" @ok="tapRecordModalOk">
      <a-form style="margin-top: 20px" :colon="false" :label-col="{ style: { width: '130px', marginRight: '20px' } }" ref="formRef" :model="formData">
        <a-form-item
          label=""
          name="label"
          :rules="[
            { required: true, message: '请输入快捷查询命名' },
            { max: 20, message: '输入内容不可超过20字符' },
          ]"
        >
          <a-input auto-focus v-model:value="formData.label" placeholder="请输入快捷查询命名" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal v-model:open="isShowInputDlg" title="批量输入" okText="确定" @ok="closeInputDlg" @cancel="isShowInputDlg = false" style="top: 200px">
      <a-form-item
        :label="inputLabel"
        :label-col="{
          style: {
            width: '60px',
          },
        }"
      >
        <a-textarea v-model:value="inputText" :placeholder="`多个${inputLabel}，以逗号分隔或每行一个${inputLabel}`" :rows="10" />
      </a-form-item>
      <template #footer>
        <a-button style="float: left" @click="inputText = ''">清空</a-button>
        <a-button @click="isShowInputDlg = false">取消</a-button>
        <a-button type="primary" @click="closeInputDlg">确认</a-button>
      </template>
    </a-modal>
    <Screening ref="screeningRef" @screeningConfirmed="screeningConfirmed"></Screening>
    <ConfirmModal ref="confirmModalRef" />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
import { SettingOutlined, CheckOutlined } from '@ant-design/icons-vue'
import { GetQuickQuery, SaveQuickQuery } from '@/servers/Common'
import ConfirmModal from '@/components/ConfirmModal.vue'
import { filterOption, customFilterOption } from '@/utils/index'

import { h } from 'vue'

const props = defineProps({
  pageType: {
    type: Number,
    default: 0,
  },
  clearCb: {
    type: Function,
    default: () => {},
  },
  showSetting: {
    type: Boolean,
    default: true,
  },
})

/** 注册 v-nodes 组件 */
const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes
  },
})

const confirmModalEl = useTemplateRef('confirmModalRef')
const isConmmence = ref(false)

// 批量输入
const isShowInputDlg = ref(false)
const inputLabel = ref(null)
const inputText = ref('')
const inputKey = ref(null)

const formRef = ref()
const form = defineModel<any>('form')
const formData = ref({
  label: '',
})

const isQuicks = computed(() => form.value.some((v) => v.isQuicks && v.isShow))
const quicks = computed(() => form.value.filter((v) => v.isQuicks && v.isShow))
const emit = defineEmits(['search', 'setting'])
const path = useRoute().path
onMounted(() => {
  initScreening()
  getQuickSearch()
})
const changeInput = (item) => {
  setTimeout(() => {
    if (item.value >= 999999999) {
      item.value = null
    }
  })
}
/** 关闭批量输入 */
const closeInputDlg = () => {
  form.value.forEach((item) => {
    if (item.key == inputKey.value) {
      item.value = dealStr(inputText.value)
    }
  })
  isShowInputDlg.value = false
}
/** 显示批量输入 */
const showInputDlg = (item) => {
  isShowInputDlg.value = true
  inputText.value = item.value
  inputLabel.value = item.label.replace('搜索', '')
  inputKey.value = item.key
}
const initScreening = () => {
  const obj = localStorage.getItem('screeningObj') ? JSON.parse(localStorage.getItem('screeningObj') || '') : {}
  if (obj[path]) {
    const arr = [] as any
    form.value.forEach((y) => {
      obj[path].forEach((x) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    form.value = arr
  } else {
    form.value = form.value.map((v: any) => ({ ...v, isShow: true }))
  }
}

/** 过滤v-bind onChange事件，避免事件处理冲突及解析错误警告 */
const filterProps = (item) => {
  const { onChange, ...rest } = item
  return { ...rest }
}

const handleSearch = (item) => {
  item.searchArr = item.selectArr?.filter((v) => v.label.toLowerCase().indexOf(item.value.toLowerCase()) >= 0 || v.value.toLowerCase().indexOf(item.value.toLowerCase()) >= 0) || []
}

const handleQuick = (item, v) => {
  const value = v?.value || v

  if (v === null) item.value = item.multiple ? [] : null
  else {
    if (item.multiple) {
      if (item.value && item.value.includes(value)) {
        item.value = item.value.filter((x) => x != value)
      } else {
        item.value = item.value ? item.value.concat(value) : [value]
      }
    } else item.value = value
  }

  if (item.onChange) item.onChange()

  nextTick(() => getList())
}

const count = computed(() => form.value.filter((v) => v.isShow).length)

const clearForm = () => {
  form.value.forEach((item) => {
    item.value = item.defaultValue || item.defaultValue >= 0 ? item.defaultValue : item.multiple ? [] : null
  })
  props.clearCb()
}

// 重置表单
const resetForm = () => {
  clearForm()
  getList()
}
// 获取快捷记录
const shortcutQueryArr = ref([] as any)
const getForm = (item) => {
  form.value.forEach((x) => {
    x.value = null
    for (const key in item.value) {
      if (x.key === key) {
        x.value = item.value[key]
      }
    }
  })
  nextTick(() => getList())
}
// 删除快捷记录
const tapCloseRome = (i) => {
  confirmModalEl.value?.confirm({
    title: '删除快捷方式',
    type: 'del',
    width: 440,
    content: [{ text: '此操作不可恢复，确定要删除该快捷方式吗？' }],
    onOk: (close) => {
      shortcutQueryArr.value.splice(i, 1)
      SaveQuickQuery({ page_type: props.pageType, quick_query_data: JSON.stringify(shortcutQueryArr.value) })
      close()
    },
  })
}
// 查询

const getList = () => {
  emit('search')
}

const isRecordModal = ref(false)
const tapRecordModalOk = async () => {
  try {
    await formRef.value.validateFields()
    if (shortcutQueryArr.value.find((e) => e.label === formData.value.label)) {
      message.warning('存在相同命名的快捷查询')
      return
    }

    const obj = {}
    form.value.forEach((x) => {
      if (x.value || x.value === 0) {
        obj[x.key] = x.value
      }
    })
    shortcutQueryArr.value.push({
      label: formData.value.label,
      value: obj,
    })

    console.log('shortcutQueryArr.value', shortcutQueryArr.value)
    SaveQuickQuery({ page_type: props.pageType, quick_query_data: JSON.stringify(shortcutQueryArr.value) })

    isRecordModal.value = false
    formData.value.label = ''
    await formRef.value?.clearValidate()
  } catch (e) {
    console.log(e)
  }
}

const handleShowQuick = async () => {
  await formRef.value?.clearValidate()
  if (!form.value.find((e) => e.value || e.value === 0)) {
    message.warning('未进行筛选')
  } else {
    isRecordModal.value = true
    formData.value.label = ''
  }
}

const screeningRef = ref()
const openScreening = () => {
  screeningRef.value.init({
    formArr: form.value,
  })
}
const screeningConfirmed = (newForm) => {
  form.value = newForm

  const arr = [] as any
  newForm.forEach((x) => {
    arr.push({ key: x.key, isShow: x.isShow })
  })
  console.log(arr)

  const obj = localStorage.getItem('screeningObj') ? JSON.parse(localStorage.getItem('screeningObj') || '') : {}
  obj[path] = arr
  localStorage.setItem('screeningObj', JSON.stringify(obj))
  resetForm()
  getList()
}

const getQuickSearch = async () => {
  const res = await GetQuickQuery({ page_type: props.pageType })
  if (res.data.quick_query_data) {
    shortcutQueryArr.value = JSON.parse(res.data.quick_query_data)
  }
}

defineExpose({
  openScreening,
  clearForm,
})
</script>

<style lang="scss" scoped>
.quickBox {
  display: flex;
  flex-wrap: wrap;
  color: #666;

  .quickLine {
    display: flex;

    .quickLabel {
      box-sizing: border-box;
      padding-top: 3px;
      font-weight: 700;
    }

    .quickContent {
      display: flex;
      flex: 1;
      flex-wrap: wrap;

      .quickItem {
        position: relative;
        height: 24px;
        padding: 3px 8px;
        margin-right: 4px;
        margin-bottom: 8px;
        overflow: hidden;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        background-color: #ebebeb;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        transition: 0.3s all;

        &:hover {
          opacity: 1;

          &.active {
            opacity: 0.7;
          }
        }

        &.active {
          color: #448ef7;
          background: #fff;
          border: 1px solid #448ef7;
          opacity: 1;
        }
      }

      .checkIcon {
        position: absolute;
        top: -2px;
        right: -7px;
        width: 0;
        height: 0;
        border-right: 10px solid transparent;
        border-bottom: 10px solid #1890ff;
        border-left: 10px solid transparent;
        opacity: 0;
        transition: all 0.3s;
        transform: rotate(45deg);

        .icon {
          position: relative;
          top: -5px;
          left: -3px;
          font-size: 6px;
          color: #fff;
          transform: rotate(-45deg);
        }
      }
    }
  }

  .partingLine {
    width: 1px;
    height: 15px;
    margin-inline: 12px;
    margin-top: 5px;
    background-color: #dcdcdc;
  }
}

.formBox {
  position: relative;
  padding: 12px 18px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-bottom: none;
  border-radius: 4px;

  .rangeBox {
    display: flex;
    justify-content: space-between;

    .labelText {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100px;
      padding-left: 8px;
      color: #c0c4cc;
      background-color: #fff;
      border: 0.0625rem solid #d9d9d9;
      border-right: none;
    }

    .nubInputBox {
      flex: 1;

      .ant-picker {
        flex: 1;
      }

      .nubInputLi {
        margin: 0 8px;
      }
    }
  }

  .item {
    .nubInputBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 12.5rem;

      .nubInput {
        width: 5.42rem;
      }

      .nubInputLi {
        width: 0.42rem;
        height: 0.08rem;
        background: #ccc;
      }
    }

    .li {
      width: 12.5rem;
      margin-right: 8px;
      margin-bottom: 0.67rem;

      .select {
        width: 150px;

        &.colorActive {
          color: #ccc;
        }
      }

      .cascader {
        width: 100%;
      }
    }

    .li2 {
      width: 25.72rem;
      margin-right: 8px;
      margin-bottom: 0.67rem;

      .treeSelectBox {
        padding-right: 1.33rem;
      }
    }

    .selectAndRangeBox {
      display: flex;

      .select {
        width: 12.5rem;
        margin-right: 8px;
        border-top: none;
        border-right: none;

        :deep(.ant-select-selector) {
          border-top: none;
          border-right: none;
        }

        &.colorActive {
          color: #ccc;
        }
      }
    }

    .timeBox {
      width: 26.25rem;

      .timeTextSelector {
        width: 40%;

        .ant-select {
          width: 100%;
        }
      }

      .timeSelector {
        width: 60%;
      }
    }
  }

  .li3 {
    width: 40rem;
    margin-right: 0.67rem;
  }

  .li5 {
    width: 41.33rem;
    margin-bottom: 0.67rem;

    .treeSelectBox {
      padding-right: 1.33rem;
    }

    .rangeBox {
      display: flex;
      justify-content: space-between;
      padding-right: 1.33rem;

      .labelText {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 0.83rem;
        font-size: 14px;
        color: #777;
        border: 0.0625rem solid #d9d9d9;
        border-right: none;
      }

      .nubInputBox {
        flex: 1;

        .ant-picker {
          flex: 1;
        }

        .nubInputLi {
          margin: 0 8px;
        }
      }
    }

    .selectAndRangeBox {
      display: flex;

      .select {
        width: 12.5rem;
        margin-right: 8px;
        border-top: none;
        border-right: none;

        :deep(.ant-select-selector) {
          border-top: none;
          border-right: none;
        }

        &.colorActive {
          color: #ccc;
        }
      }
    }

    .timeBox {
      width: 26.25rem;

      .timeTextSelector {
        width: 40%;

        .ant-select {
          width: 100%;
        }
      }

      .timeSelector {
        width: 60%;
      }
    }
  }
}

.btnlist {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  margin-top: 12px;
}

:deep(.ant-tag-has-color) {
  margin-top: 4px;
  line-height: 22px;
  color: #666;
}

.unfold-box {
  position: absolute;
  right: 0;
  bottom: -10px;
  left: 0;
  display: flex;
  align-items: center;
  height: 21px;
}

.unfold {
  display: flex;
  align-items: center;
  width: 100%;

  .lin1 {
    flex: 1;
    height: 2px;
    background: linear-gradient(-90deg, #3d7fff, #f7f8fa);
    border-top-left-radius: 50%;
    border-bottom-left-radius: 50%;
  }

  .lin2 {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, #3d7fff, #f7f8fa);
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
  }

  &-btn {
    display: flex;
    justify-content: center;
    width: 60px;
    height: 24px;
    margin: 0 20px;
    font-size: 12px;
    line-height: 22px;
    border-radius: 30px;
  }

  &-setting {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
  }
}

:deep(.ant-tag-close-icon) {
  color: #666 !important;
}

.pointer {
  cursor: pointer;
}
</style>

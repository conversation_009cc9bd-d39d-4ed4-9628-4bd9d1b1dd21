<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.DISPATCH_ORDER" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.DISPATCH_ORDER" :get-list="GetList" :isIndex="true" :isCheckbox="true">
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button @click="detail(row)" class="btn" v-if="btnPermission[520003]">查看</a-button>
          <a-dropdown>
            <a-button v-if="btnPermission[520001] || btnPermission[520002] || btnPermission[520004] || btnPermission[520005]">更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('pause', row)" v-if="btnPermission[520004]">暂停</a-menu-item>
                <a-menu-item @click="tapAdd('delete', row)" v-if="btnPermission[520001]">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
                <a-menu-item @click="tapAdd('online', row)" v-if="btnPermission[520002]">上线</a-menu-item>
                <a-menu-item @click="tapAdd('closure', row)" v-if="btnPermission[520005]">强制关结</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer v-model:open="isAddRole" @afterOpenChange="formRef.clearValidate()" width="1050px" title="上线派工单" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }">
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-row :gutter="24">
          <a-col class="gutter-row" :span="8">
            <a-form-item label="派工单号" name="dispatch_order_number">
              <span>{{ editForm.dispatch_order_number || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="物料编码" name="material_number">
              <span>{{ editForm.material_number || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="物料名称" name="material_name">
              <span>{{ editForm.material_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="客户" name="customer_name">
              <span>{{ editForm.customer_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="生产阶别" name="production_stage_text">
              <span>{{ editForm.production_stage_text || '-' }}</span>
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="8">
            <a-form-item label="状态" name="order_status_text">
              <span>{{ editForm.order_status_text || '-' }}</span>
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="8">
            <a-form-item label="计划数量" name="plan_nums">
              <span>{{ editForm.plan_nums || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="预计开工日期" name="plan_start_time">
              <a-date-picker show-time placeholder="请选择日期" v-model:value="editForm.plan_start_time" :valueFormat="'YYYY-MM-DD HH:mm:ss'" @change="checkDate('start')" class="w188px" />
            </a-form-item>
          </a-col>
          <a-col class="gutter-row" :span="8">
            <a-form-item label="预计完工日期" name="plan_end_time">
              <a-date-picker show-time placeholder="请选择日期" v-model:value="editForm.plan_end_time" :valueFormat="'YYYY-MM-DD HH:mm:ss'" @change="checkDate('end')" class="w188px" />
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="8">
            <a-form-item label="工艺流程" name="process_flow_id">
              <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="editForm.process_flow_id" placeholder="请选择" :options="flowOptions"></a-select>
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="8">
            <a-form-item label="开始工序" name="start_process">
              <span>{{ editForm.start_process || '-' }}</span>
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="8">
            <a-form-item label="结束工序" name="end_process">
              <span>{{ editForm.end_process || '-' }}</span>
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="8">
            <a-form-item label="下达时间" name="created_at">
              <span>{{ editForm.created_at || '-' }}</span>
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="8">
            <a-form-item label="下达人" name="real_name">
              <span>{{ editForm?.creator?.real_name || '-' }}</span>
            </a-form-item>
          </a-col>

          <a-col class="gutter-row" :span="24">
            <a-form-item label="备注" name="remark">
              <a-input v-model:value="editForm.remark" placeholder="请输入" :maxlength="200" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />

    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <a-form ref="remarkFormRef" :model="remarkForm" :rules="remarkRules">
        <a-form-item label="备注" name="closure_remark" v-if="roleModuleType == 'closure'" :label-col="{ style: { flex: '0 0 50px' } }">
          <a-textarea v-model:value="remarkForm.closure_remark" placeholder="请输入强制关结派工单的原因" :rows="4" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { onMounted } from 'vue'

import { Delete, GetList, GoOnline, GetOptions, Closure, Pause, Details } from '@/servers/dispatchOrder'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import DetailDrawer from './components/DetailDrawer.vue'

const { btnPermission } = usePermission()
const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  process_flow_id: [{ required: false, message: '请选择工艺流程', trigger: ['change', 'blur'] }],
}
const remarkRules: Record<string, Rule[]> = {
  closure_remark: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 200),
      message: '输入内容不可超过200字符',
    },
  ],
}
const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索派工单号',
    value: '',
    type: 'input',
    key: 'dispatch_order_number',
  },
  {
    label: '生产阶别',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'production_stage',
    multiple: true,
  },
  {
    label: '状态',
    value: [],
    type: 'select',
    selectArr: [],
    key: 'order_status',
    multiple: true,
  },
  {
    label: '下达时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['下达开始时间', '下达结束时间'],
  },
  {
    label: '请输入工单号',
    value: '',
    type: 'input',
    key: 'work_order_number',
  },
  {
    label: '请输入物料编码',
    value: '',
    type: 'input',
    key: 'goods_code',
  },
])
const stageOptions = ref([])
const statusOptions = ref([])
const flowOptions = ref([])
const editForm = ref({
  id: null,
  dispatch_order_number: '',
  material_number: '',
  material_name: null,
  customer_name: '',
  production_stage_text: '',
  order_status_text: '',
  plan_nums: 0,
  plan_start_time: null,
  plan_end_time: null,
  sale_order_number: '',
  process_flow_id: null,
  start_process: '',
  end_process: '',
  created_at: '',
  creator: '',
  remark: '',
})
const remarkForm = reactive({
  closure_remark: '',
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.DISPATCH_ORDER) {
    const arr: any[] = []
    obj.DISPATCH_ORDER.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(async () => {
  await getOptions()
  console.log(formArr.value, 'form')
  search()
  initScreening()
})

const getOptions = () => {
  GetOptions({}).then((res) => {
    stageOptions.value = res.data.production_stage || []
    statusOptions.value = res.data.order_status || []
    formArr.value.forEach((item) => {
      if (item.key === 'order_status') {
        item.selectArr = statusOptions.value
      } else if (item.key === 'production_stage') {
        item.selectArr = stageOptions.value
      }
    })
  })
}

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    goOnline()
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const getDetail = async (id) => {
  Details({ id }).then((res) => {
    editForm.value = res.data
    editForm.value.process_flow_id = null
    isAddRole.value = true
    roleModuleType.value = 'online'
  })
}
const tapAdd = (type: string, row: any = '') => {
  switch (type) {
    case 'online':
      getDetail(row.id)
      break
    case 'delete':
      roleModuleType.value = 'delete'
      visibleData.isShow = true
      visibleData.title = '删除派工单'
      visibleData.content = `您确定要删除该派工单吗？此操作将永久删除派工单相关信息，包括派工单详情、处理记录等，且无法恢复。请谨慎确认`
      visibleData.confirmBtnText = '确认删除'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    case 'pause':
      roleModuleType.value = 'pause'
      visibleData.isShow = true
      visibleData.title = '暂停派工单'
      visibleData.content = `您确认暂停该派工单吗？暂停后将暂停该派工单的所有生产任务流转，直至手动恢复。请确认操作无误。`
      visibleData.confirmBtnText = '确认'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        pauseRole(row.id)
      }
      break
    case 'closure':
      roleModuleType.value = 'closure'
      visibleData.isShow = true
      visibleData.title = '强制关结派工单'
      visibleData.content = `确认要强制关结该派工单吗？一旦关结，派工单状态将变为已完成，所有未处理完的任务将被视为已完成，如有未记录的工作内容，可能会丢失。请谨慎操作！`
      visibleData.confirmBtnText = '确认'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      remarkForm.closure_remark = ''
      visibleData.okFn = () => {
        closureRole(row.id)
      }
      break
    default:
      break
  }
}
// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id)
}

const tableRef = ref()
const formRef = ref()
const remarkFormRef = ref()
const search = () => tableRef.value.search()

// 上线
const goOnline = () => {
  const obj = {
    id: editForm.value.id,
    process_flow_id: editForm.value.process_flow_id,
    plan_start_time: editForm.value.plan_start_time,
    plan_end_time: editForm.value.plan_end_time,
    remark: editForm.value.remark,
  }
  GoOnline(obj).then((res) => {
    if (res.success) {
      message.success('上线派工单成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  Delete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}

const pauseRole = (id) => {
  Pause({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('暂停成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}

// 强制关结工单
const closureRole = async (id) => {
  try {
    await remarkFormRef.value.validateFields()
    Closure({ id, closure_remark: remarkForm.closure_remark })
      .then((res) => {
        if (res.success) {
          visibleData.isShow = false
          message.success('强制关结工单成功')
          search()
        } else {
          message.error(res.message)
        }
      })
      .catch(() => {
        visibleData.isShow = false
      })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const checkDate = (type) => {
  if (editForm.value.plan_end_time && editForm.value.plan_start_time) {
    if (new Date(editForm.value.plan_end_time) < new Date(editForm.value.plan_start_time)) {
      if (type == 'start') {
        editForm.value.plan_start_time = null
      } else {
        editForm.value.plan_end_time = null
      }
      message.error('预计完工时间不能小于计划开工时间！')
    }
  }
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 0;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

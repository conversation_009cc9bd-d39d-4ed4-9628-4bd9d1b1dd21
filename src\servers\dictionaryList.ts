import { request } from './request'
// 获取列表
export const GetDictList = (data) => request({ url: '/api/System/DataDictionary/List', data })
// 新建
export const Add = (data) => request({ url: '/api/System/DataDictionary/Store', data })
// 查看
export const Details = (data) => request({ url: '/api/System/DataDictionary/Detail', data })
// 编辑
export const Update = (data) => request({ url: '/api/System/DataDictionary/Update', data })
// 删除
export const Delete = (data) => request({ url: '/api/System/DataDictionary/Destroy', data })
// 修改字典状态
export const ChangeStatus = (data) => request({ url: '/api/System/DataDictionary/ChangeStatus', data })
// 字典编码列表
export const CodeList = (data) => request({ url: '/api/System/DataDictionary/CodeList', data })
// 字典筛选列表
export const ItemList = (data) => request({ url: '/api/System/DataDictionary/ItemList', data })

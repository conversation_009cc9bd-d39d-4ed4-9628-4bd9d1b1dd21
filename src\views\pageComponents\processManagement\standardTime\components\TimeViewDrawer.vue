<template>
  <a-drawer title="查看标准工时" width="650px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" v-bind="formItemLayout">
      <a-form-item label="生产阶别" name="data_dictionary_item_id">
        {{ formState.data_dictionary_item?.key || '--' }}
      </a-form-item>
      <a-form-item label="物料编码" name="material_id">
        {{ formState.material.goods_code || '--' }}
      </a-form-item>
      <a-form-item label="物料名称" name="material_code">
        {{ formState.material.goods_name || '--' }}
      </a-form-item>
      <a-form-item label="型号规格" name="material_spec">
        {{ formState.material.spec || '--' }}
      </a-form-item>
      <a-form-item label="标准工时" name="standard_time">
        {{ `${formState.standard_time} 秒(s)` }}
      </a-form-item>
      <a-form-item label="前置时间" name="lead_time">
        {{ `${formState.lead_time} 秒(s)` }}
      </a-form-item>
      <a-form-item label="备注" name="remark">
        {{ formState.remark || '--' }}
      </a-form-item>
    </a-form>
    <div class="drawer-title mt-32">其他信息</div>
    <a-form :model="formState" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
      <a-form-item label="创建时间" name="created_at">
        <span>{{ formState.created_at }}</span>
      </a-form-item>
      <a-form-item label="创建人" name="creator">
        {{ formState.creator?.real_name }}
        <span class="user-info">{{ `（ ${formState.creator?.position_name} | ${formState.creator?.department_name} ）` }}</span>
      </a-form-item>
      <a-form-item label="最后修改时间" name="updated_at">
        {{ formState.updated_at }}
      </a-form-item>
      <a-form-item label="最后修改人" name="modifier">
        {{ formState.modifier?.real_name }}
        <span class="user-info">{{ `（ ${formState.modifier?.position_name} | ${formState.modifier?.department_name} ）` }}</span>
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { viewStandardTime } from '@/servers/standardTime'

const props = defineProps<{
  open: boolean
  initValue: {
    type: string
    id: string
  }
}>()

const emits = defineEmits(['update:open', 'update'])

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
}

const formState = reactive({
  id: null,
  data_dictionary_item_id: '',
  data_dictionary_item: { key: '' },
  material: { goods_code: '', goods_name: '', spec: '' },
  material_code: '',
  material_spec: '',
  standard_time: '',
  lead_time: '',
  remark: '',
  created_at: '',
  creator: { real_name: '', position_name: '', department_name: '' },
  updated_at: '',
  modifier: { real_name: '', position_name: '', department_name: '' },
})

// 关闭drawer
const onClose = () => {
  emits('update:open', false)
}

// 获取标准工时详情
const getStandardTimeDetail = async () => {
  const res = await viewStandardTime(props.initValue.id)
  Object.assign(formState, res.data)
}

onMounted(() => {
  getStandardTimeDetail()
})
</script>

<style lang="scss" scoped>
.user-info {
  color: #8a8a8a;
}
</style>

<template>
  <a-modal :open="open" title="删除生产实体人员" width="420px" @cancel="handleCancel">
    <span>{{ content }}</span>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" danger @click="handleSubmit">确认删除</a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { deleteWorkshopPerson } from '@/servers/workshopPerson'

const props = defineProps<{
  open: boolean
  initValue: { id: number }
}>()

const emits = defineEmits(['update:open', 'update'])

const content = '是否确认删除生产实体人员？删除前，请先移除关联该生产实体人员的所有数据，否则会操作失败'

const handleCancel = () => {
  emits('update:open', false)
}

const handleSubmit = async () => {
  const res = await deleteWorkshop<PERSON><PERSON>({ id: props.initValue.id })
  message.success(res.message)
  emits('update:open', false)
  emits('update')
}
</script>

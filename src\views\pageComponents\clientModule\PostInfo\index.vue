<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.POST_INFO" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.POST_INFO" :get-list="Post" :isIndex="true">
      <template #right-btn>
        <a-button type="primary" @click="tapAdd('add')" :icon="h(PlusOutlined)" v-if="btnPermission[140001]">新建岗位</a-button>
      </template>
      <template #status="{ row }">
        <a-switch class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" :disabled="!btnPermission[140003]" />
      </template>
      <template #company="{ row }">
        <span>{{ row?.company?.name || '系统默认' }}</span>
      </template>
      <template #created_at="{ row }">
        <span>{{ row.created_at ? row.created_at.slice(0, 16) : '' }}</span>
      </template>
      <template #updated_at="{ row }">
        <span>{{ row.updated_at ? row.updated_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row, rowIndex }">
        <div class="btnBox">
          <a-button @click="detail(row, rowIndex)" class="btn" v-if="btnPermission[140006]">查看</a-button>
          <a-dropdown>
            <a-button>更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapAdd('compiler', row, rowIndex)" v-if="btnPermission[140002]">编辑</a-menu-item>
                <a-menu-item @click="tapAdd('removes', row)">
                  <span class="text-red-500" v-if="btnPermission[140005]">删除</span>
                </a-menu-item>
                <a-menu-item @click="tapAdd('setting', row)" v-if="btnPermission[140004]">分配用户</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="520"
      :title="roleModuleType == 'add' ? '新建岗位' : '编辑岗位'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="editForm" :rules="rules">
        <a-form-item label="序号" name="rowIndex" v-if="roleModuleType == 'compiler'">
          <span>{{ rowIdx }}</span>
        </a-form-item>
        <a-form-item label="岗位名称" name="position_name">
          <a-input v-model:value="editForm.position_name" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="岗位状态" name="status">
          <a-switch class="btn" v-model:checked="editForm.status" checked-children="启用" un-checked-children="停用" :checkedValue="1" :unCheckedValue="0" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-input v-model:value="editForm.remark" placeholder="请输入" :maxlength="200" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { h, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { AddPosition, PositionDelete, Post, PositionUpdate, PositionStatus } from '@/servers/0rganization'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { useRouter } from 'vue-router'
import DetailDrawer from './components/DetailDrawer.vue'

const router = useRouter()
const { btnPermission } = usePermission()

const roleModuleType = ref('add')
const rules: Record<string, Rule[]> = {
  position_name: [
    { required: true, trigger: ['change', 'blur'] },
    {
      validator: (_rule, value) => validateStr(_rule, value, 50),
      message: '输入内容不可超过50字符',
    },
  ],
}
const isAddRole = ref(false)
// 查看
const detailDrawerRef = ref()
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索岗位名称',
    value: null,
    type: 'input',
    key: 'position_name',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const rowIdx = ref(0)
const editForm = reactive({
  id: null,
  position_name: '',
  remark: '',
  status: 1,
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.POST_INFO) {
    const arr: any[] = []
    obj.POST_INFO.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  search()
  initScreening()
})

const tapSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapAdd = (type: string, row: any = '', rowIndex: number = 0) => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      editForm.id = null
      editForm.position_name = ''
      editForm.remark = ''
      editForm.status = 1
      break
    case 'compiler':
      rowIdx.value = rowIndex + 1
      editForm.id = row.id
      editForm.position_name = row.position_name
      editForm.remark = row.remark
      editForm.status = row.status
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      console.log(editForm, 'editForm')
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除岗位'
      visibleData.content = `是否确认删除岗位？删除前，请先删除关联该岗位的所有数据，否则会操作失败！`
      visibleData.confirmBtnText = '确认删除'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    case 'setting':
      openConfigList(row)
      break
    default:
      break
  }
}

const tapSwitch = (e, row) => {
  visibleData.isShow = true
  visibleData.title = '岗位'
  visibleData.content = `是否确认${row.status ? '停用' : '启用'}该岗位？`
  visibleData.confirmBtnText = '确认'
  visibleData.okType = 'danger'
  visibleData.isCancelBtn = true
  visibleData.okFn = () => {
    positionStatus({ id: row.id, status: e ? 1 : 0 })
  }
}

const positionStatus = (obj) => {
  PositionStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}

// 详情
const detail = (item, idx) => {
  detailDrawerRef.value?.open(item.id, idx + 1)
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

// 新增
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  AddPosition(obj).then((res) => {
    if (res.success) {
      message.success('新增成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}
// 编辑
const upRoleDate = () => {
  const obj = JSON.parse(JSON.stringify(editForm))
  PositionUpdate(obj).then((res) => {
    if (res.success) {
      message.success('修改成功')
      isAddRole.value = false
      search()
    } else {
      message.error(res.message)
    }
  })
}

// 删除
const deleteRole = (id) => {
  PositionDelete({ id })
    .then((res) => {
      if (res.success) {
        visibleData.isShow = false
        message.success('删除成功')
        search()
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      visibleData.isShow = false
    })
}

const openConfigList = (row) => {
  const sessionPageStr = sessionStorage.getItem('sessionPage')
  let sessionPage = {}
  if (sessionPageStr) {
    sessionPage = JSON.parse(sessionPageStr)
    sessionPage[`userPostSetting${row.id}`] = 'true'
  } else {
    sessionPage[`userPostSetting${row.id}`] = 'true'
  }
  sessionStorage.setItem('sessionPage', JSON.stringify(sessionPage))
  router.push(`/userPostSetting/${row.id}/${row.position_name}`)
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

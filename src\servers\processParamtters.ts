import { request } from './request'
// 获取列表
export const GetList = (data) => request({ url: '/api/manufacturingCenter/parameters/list', data })
// 新建
export const Add = (data) => request({ url: '/api/manufacturingCenter/parameters/create', data })
// 查看
export const Details = (data) => request({ url: '/api/manufacturingCenter/parameters/show', data })
// 编辑
export const Update = (data) => request({ url: '/api/manufacturingCenter/parameters/update', data })
// 删除
export const Delete = (data) => request({ url: '/api/manufacturingCenter/parameters/delete', data })
// 获取下拉选项
export const GetOptions = (data) => request({ url: '/api/manufacturingCenter/management/list', data }, 'GET')

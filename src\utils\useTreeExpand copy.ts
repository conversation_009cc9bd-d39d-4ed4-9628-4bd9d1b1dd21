/* eslint-disable no-unused-vars */
import { ref, computed } from 'vue'

interface TreeExpandOptions {
  autoExpandParent?: boolean
  getChildren?: () => any[]
}

interface TreeExpandReturn {
  expandedKeys: Ref<string[]>
  autoExpandParent: Ref<boolean>
  expandAll: () => void
  collapseAll: () => void
  expandToLevel: (level: number) => void
  handleExpand: (keys: any) => void
  isAllExpanded: ComputedRef<boolean>
}

/**
 * Tree展开/收起控制Hook
 * @param {Array} treeData 树形数据
 * @param {Object} options 配置项
 * @param {Boolean} [options.autoExpandParent=true] 是否自动展开父节点
 * @param {Function} [options.getChildren] 自定义children字段获取方法
 * @returns {Object} 控制方法和状态
 */
export default function useTreeExpand(treeData: any = [], options: TreeExpandOptions | any = {}): TreeExpandReturn {
  console.log('tree:', treeData)
  const { autoExpandParent = true, getChildren = (node) => node.children } = options

  const expandedKeys = ref<any>([])
  const autoExpandParentRef = ref(autoExpandParent)
  console.log('autoExpandParentRef:', autoExpandParentRef.value)

  /**
   * 递归获取所有可展开节点的key
   * @param {Array} data 树数据
   * @returns {Array} 所有可展开节点的key数组
   */
  const getAllExpandableKeys = (data) => {
    let keys: any = []
    data?.forEach((item) => {
      const children = getChildren(item)
      console.log('children:', children)
      if (children?.length) {
        keys.push(item.key)
        keys = keys.concat(getAllExpandableKeys(children))
      }
    })
    return keys
  }

  /**
   * 展开到指定层级
   * @param {Number} level 要展开到的层级
   * @param {Array} data 树数据（默认为根数据）
   * @param {Number} currentLevel 当前层级（内部使用）
   * @returns {Array} 展开的keys数组
   */
  const getKeysByLevel = (level, data = treeData.value, currentLevel = 1) => {
    let keys: any = []
    data.forEach((item) => {
      const children = getChildren(item)
      if (children?.length && currentLevel <= level) {
        keys.push(item.key)
        keys = keys.concat(getKeysByLevel(level, children, currentLevel + 1))
      }
    })
    return keys
  }

  // 全部展开
  const expandAll = () => {
    expandedKeys.value = getAllExpandableKeys(treeData)
    console.log('expandedKeys:', expandedKeys.value)
    autoExpandParentRef.value = true
  }

  // 全部收起
  const collapseAll = () => {
    expandedKeys.value = []
  }

  // 展开到指定层级
  const expandToLevel = (level: number) => {
    expandedKeys.value = getKeysByLevel(level)
    autoExpandParentRef.value = true
  }

  // 处理手动展开/收起
  const handleExpand = (keys) => {
    expandedKeys.value = keys
    autoExpandParentRef.value = false
  }

  return {
    expandedKeys,
    autoExpandParent: autoExpandParentRef,
    expandAll,
    collapseAll,
    expandToLevel,
    handleExpand,
    // 计算属性：当前是否全部展开
    isAllExpanded: computed(() => {
      const allKeys = getAllExpandableKeys(treeData.value)
      return allKeys.length > 0 && allKeys.every((key) => expandedKeys.value.includes(key))
    }),
  }
}

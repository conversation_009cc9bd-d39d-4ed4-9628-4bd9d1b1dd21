import { request } from './request'

// 获取工作中心树形数据
export const getWorkshopCenterTree = (data) => request({ url: '/api/businessBase/workshopWorkcenter/leftStructureColumn', data })
// 获取工作中心列表
export const getWorkshopCenters = (data) => request({ url: '/api/businessBase/workshopWorkcenter/index', data })
// 新建工作中心
export const addWorkshopCenter = (data) => request({ url: '/api/businessBase/workshopWorkcenter/store', data })
// 更新工作中心
export const updateWorkshopCenter = (id, data) => request({ url: `/api/businessBase/workshopWorkcenter/update/${id}`, data })
// 查看工作中心详情
export const viewWorkshopCenter = (id) => request({ url: `/api/businessBase/workshopWorkcenter/show/${id}` })
// 删除工作中心
export const deleteWorkshopCenter = (id) => request({ url: `/api/businessBase/workshopWorkcenter/destroy/${id}` })
// 更新工作中心状态
export const updateCenterStatus = (id, data) => request({ url: `/api/businessBase/workshopWorkcenter/status/${id}`, data })
// 获取下拉选项
export const getSelectOptions = () => request({ url: '/api/businessBase/workshopWorkcenter/getSelect' })
// 获取车间负责人
export const getPrincipals = (companyId) => request({ url: `/api/businessBase/workshopWorkcenter/getPrincipalList/${companyId}` })
// 获取工厂日历班别数据
export const getFactoryCalendarClass = () => request({ url: '/api/businessBase/factoryCalendar/getSelect' })
// 获取工作中心列表（不做权限控制）
export const getWorkCentersWithoutPerm = () => request({ url: '/api/businessBase/workshopWorkcenter/getWorkCenterList' })
// 根据生产阶别ID获取工序参数
export const getProcessByStage = (data) => request({ url: '/api/businessBase/workshopWorkcenter/getFlowOptionList', data })

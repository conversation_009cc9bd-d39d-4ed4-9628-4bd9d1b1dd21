import dayjs from 'dayjs'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import { number2 } from '.'

VxeUI.formats.add('number', {
  tableCellFormatMethod: ({ cellValue }) => number2(cellValue, 2),
  tableFooterCellFormatMethod: ({ itemValue }) => number2(itemValue, 2),
})
VxeUI.formats.add('number4', {
  tableCellFormatMethod: ({ cellValue }) => number2(cellValue, 4),
  tableFooterCellFormatMethod: ({ itemValue }) => number2(itemValue, 4),
})

VxeUI.formats.add('date', {
  tableCellFormatMethod: ({ cellValue }) => dayjs(cellValue).format('YYYY-MM-DD'),
  tableFooterCellFormatMethod: ({ itemValue }) => dayjs(itemValue).format('YYYY-MM-DD'),
})
export default VxeUI

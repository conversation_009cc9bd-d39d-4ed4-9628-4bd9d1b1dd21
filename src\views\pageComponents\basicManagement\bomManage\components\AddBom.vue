<template>
  <a-drawer width="1200" @close="handleClose" v-model:open="openDrawer">
    <template #title>
      <span>新建产品BOM</span>
    </template>
    <template #footer>
      <a-button type="primary" @click="handleSubmit" class="!mr-10px">保存</a-button>
      <a-button @click="handleClose">取消</a-button>
    </template>
    <a-form ref="formRef" :model="formState" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="产品编码" name="productCode" :rules="[{ required: true, message: '请输入产品编码' }]">
            <a-input-group compact class="!flex">
              <a-input
                v-model:value="formState.productCode"
                placeholder="搜索产品编码"
                allow-clear
                @blur="formState.productCode = `${formState.productCode || ''}`.trim()"
                style="width: 240px"
                :readonly="true"
              />
              <a-button @click="showInputDlg" class="!flex items-center justify-center !w-40px">
                <span class="iconfont icon-chazhao_find text-14px c-#000"></span>
              </a-button>
            </a-input-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品名称" name="productName" :rules="[{ required: true, message: '请输入产品名称' }]">
            <a-input v-model:value="formState.productName" style="width: 280px" :disabled="true" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品版本" name="productVersion" :rules="[{ required: true, message: '请输入产品版本' }]">
            <a-input v-model:value="formState.productVersion" style="width: 280px" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" name="remark">
            <a-input v-model:value="formState.remark" style="width: 280px" :maxlength="200" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="状态" name="status">
            <a-switch v-model:checked="formState.status" :checkedValue="1" :unCheckedValue="0" checked-children="启用" un-checked-children="停用"></a-switch>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item name="materialControl">
            <template #label>
              <a-tooltip>
                <template #title>
                  <span class="c-#d9001b">注意：</span>
                  <br />
                  <span>启用后，BOM中替代料需与主物料「用料阶别」严格匹配。若业务无需限制，可选择不勾选，替代料将不再强制匹配用料阶别。</span>
                </template>
                <QuestionCircleOutlined class="mr-5" />
              </a-tooltip>
              <span>用料阶别管控</span>
            </template>
            <a-checkbox v-model:checked="formState.materialControl"></a-checkbox>
          </a-form-item>
        </a-col>
      </a-row>
      <a-flex justify="space-between" class="mb-8 flex items-center ml-5 mt-40">
        <span class="c-#02a7f0">物料列表</span>
        <!-- <span>
          <a-input v-model:value="materialCodeSearch" style="width: 280px" placeholder="输入物料编码搜索" :style="{ width: '200px' }" allow-clear />
        </span> -->
      </a-flex>
    </a-form>
    <a-form ref="tableFormRef" :model="{ materialData }" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
      <vxe-table
        :data="filteredMaterialData"
        ref="materialTableRef"
        border
        :checkbox-config="{ showHeader: false }"
        :key="tableKey"
        :expand-config="expandConfig"
        @toggle-row-expand="handleToggleExpand"
      >
        <!-- <vxe-column type="checkbox" width="60" align="center"></vxe-column> -->
        <vxe-column type="expand" width="50" align="center">
          <template #content></template>
        </vxe-column>
        <vxe-column type="seq" width="70" title="顺序">
          <template #default="{ row }">
            <span v-if="row.isAlternative">{{ getMainSequenceByIndex(row.parentRowIndex) }}.{{ row.alternativeSequence }}</span>
            <span v-else>
              {{ getMainSequenceByIndex(materialData.indexOf(row)) }}
            </span>
          </template>
        </vxe-column>
        <vxe-column width="100" title="替代顺序" v-if="hasAlternativeMaterials">
          <template #default="{ row }">
            <span v-if="row.isAlternative">{{ row.alternativeSequence }}</span>
          </template>
        </vxe-column>

        <vxe-column field="materialLevel" title="用料阶别" width="140" align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item :name="['materialData', materialData.indexOf(row), 'materialLevel']" :rules="[{ required: true, message: '请选择用料阶别' }]">
                <a-select v-model:value="row.materialLevel" :options="materialLevelOptions" placeholder="请选择" style="width: 110px" allow-clear :disabled="!formState.materialControl"></a-select>
              </a-form-item>
            </div>
          </template>
          <template #header>
            <span class="c-#f87171 mr-4">*</span>
            <span>用料阶别</span>
          </template>
        </vxe-column>
        <vxe-column field="materialCode" title="物料编码" width="180" required align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item :name="['materialData', materialData.indexOf(row), 'materialCode']" :rules="[{ required: true, message: '请输入物料编码' }]">
                <a-input-group compact class="!flex w-160">
                  <a-input v-model:value="row.materialCode" placeholder="填写主料" @blur="row.materialCode = `${row.materialCode || ''}`.trim()" :readonly="true" />
                  <a-button @click="editCurrentMaterial()" class="!flex items-center justify-center !w-40px !h-30px">
                    <span class="iconfont icon-chazhao_find text-14px c-#000"></span>
                  </a-button>
                </a-input-group>
              </a-form-item>
            </div>
          </template>
          <template #header>
            <span class="c-#f87171 mr-4">*</span>
            <span>物料编码</span>
          </template>
        </vxe-column>
        <vxe-column field="materialName" title="物料名称" width="140" required align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item :name="['materialData', materialData.indexOf(row), 'materialName']" :rules="[{ required: true, message: '请输入物料名称' }]">
                <a-input v-model:value="row.materialName" allow-clear style="width: 110px" :disabled="true"></a-input>
              </a-form-item>
            </div>
          </template>
          <template #header>
            <span class="c-#f87171 mr-4">*</span>
            <span>物料名称</span>
          </template>
        </vxe-column>
        <vxe-column field="materialSpec" title="物料规格" width="140" align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item>
                <a-input v-model:value="row.materialSpec" allow-clear style="width: 110px" :disabled="true"></a-input>
              </a-form-item>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="materialCategory" title="物料分类" width="140" align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item>
                <a-input v-model:value="row.materialCategory" allow-clear style="width: 110px" :disabled="true"></a-input>
              </a-form-item>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="materialGroup" title="物料分组" width="140" align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item>
                <a-input v-model:value="row.materialGroup" allow-clear style="width: 110px" :disabled="true"></a-input>
              </a-form-item>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="version" title="版本号" width="140" align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item>
                <a-input v-model:value="row.version" allow-clear style="width: 110px" :disabled="true"></a-input>
              </a-form-item>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="dosageNumerator" title="用量：分子" width="160" required align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item :name="['materialData', materialData.indexOf(row), 'dosageNumerator']" :rules="[{ required: true, message: '请输入用量：分子' }]">
                <a-input v-model:value="row.dosageNumerator" placeholder="请输入" allow-clear style="width: 110px"></a-input>
              </a-form-item>
            </div>
          </template>
          <template #header>
            <span class="c-#f87171 mr-4">*</span>
            <span>用量：分子</span>
          </template>
        </vxe-column>
        <vxe-column field="dosageDenominator" title="用量：分母" width="160" required align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item :name="['materialData', materialData.indexOf(row), 'dosageDenominator']" :rules="[{ required: true, message: '请输入用量：分母' }]">
                <a-input v-model:value="row.dosageDenominator" placeholder="请输入" allow-clear style="width: 110px"></a-input>
              </a-form-item>
            </div>
          </template>
          <template #header>
            <span class="c-#f87171 mr-4">*</span>
            <span>用量：分母</span>
          </template>
        </vxe-column>
        <vxe-column field="baseUnit" title="基本单位" width="140" align="center">
          <template #default="{ row }">
            <div class="columnAnt">
              <a-form-item>
                <a-input v-model:value="row.baseUnit" allow-clear style="width: 110px" :disabled="true"></a-input>
              </a-form-item>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="action" title="操作" :width="actionColumnWidth" fixed="right" align="center">
          <template #default="{ row }">
            <a-button class="mr-10" v-if="row.isAlternative" :disabled="isFirstAlternative(row)" @click="moveAlternativeUp(row)">上移</a-button>
            <a-button class="mr-10" v-if="row.isAlternative" :disabled="isLastAlternative(row)" @click="moveAlternativeDown(row)">下移</a-button>
            <a-button @click="addAlternative(row)" class="mr-10">{{ !row.isAlternative ? '新增替代料' : '新增' }}</a-button>
            <a-button @click="addRow(row)" class="mr-10" v-if="!row.isAlternative">新增</a-button>
            <a-button @click="deleteRow(row)" :disabled="isDeleteDisabled(row)">删除</a-button>
          </template>
        </vxe-column>
      </vxe-table>
    </a-form>
    <MaterialInfoModal ref="materialInfoModalRef" @onSelectMaterial="handleSelectMaterial" />
    <ProductInfoModal ref="productInfoModalRef" @select="handleSelectProduct"></ProductInfoModal>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { VxeTableInstance } from 'vxe-table'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import MaterialInfoModal from './MaterialInfoModal.vue'
import ProductInfoModal from './ProductInfoModal.vue'

// 表格key
const tableKey = ref(0)
// VXE展开配置
const expandConfig = {
  showIcon: true,
  iconOpen: 'vxe-icon-square-minus',
  iconClose: 'vxe-icon-square-plus',
  visibleMethod: ({ row }) => {
    return !row.isAlternative && hasAlternatives(row)
  },
}

// 展开状态管理
const expandedRows = ref<Set<number>>(new Set())
// 选择物料ref
const materialInfoModalRef = ref()
// 选择产品ref
const productInfoModalRef = ref()
// 抽屉是否打开
const openDrawer = ref(false)
// 表单数据
const formState = ref({
  productCode: '',
  productName: '',
  productVersion: '',
  remark: '',
  status: 1,
  materialControl: false,
  // materialCode: '',
  // materialName: '',
  // dosageNumerator: undefined,
  // dosageDenominator: undefined,
  // // 其余字段可选
  // materialLevel: undefined,
  // materialSpec: undefined,
  // materialCategory: undefined,
  // materialGroup: undefined,
  // version: undefined,
  // baseUnit: undefined,
  // action: undefined,
  // isAlternative: false,
  // parentRowIndex: 0,
  // alternativeSequence: 0,
  // mainSequence: 0,
})
// 当前操作的行索引，用于区分第一行数据新增添加到当前行
const currentRowIndex = ref(-1)
// 是否正在添加替代料
const isAddingAlternative = ref(false)
// 物料编码
const materialCodeSearch = ref('')
// 物料列表表格
const materialData = ref<materialRow[]>([
  {
    materialCode: '',
    materialName: '',
    dosageNumerator: undefined,
    dosageDenominator: undefined,
    // 其余字段可选
    materialLevel: undefined,
    materialSpec: undefined,
    materialCategory: undefined,
    materialGroup: undefined,
    version: undefined,
    baseUnit: undefined,
    action: undefined,
    isAlternative: false,
    parentRowIndex: 0,
    alternativeSequence: 0,
    mainSequence: 0,
  },
])
// 物料列表表格ref
const materialTableRef = ref<VxeTableInstance<materialRow>>()
interface materialRow {
  materialLevel?: string // 用料阶别
  materialCode: string // *物料编码
  materialName: string // *物料名称
  materialSpec?: string // 物料规格
  materialCategory?: string // 物料分类
  materialGroup?: string // 物料分组
  version?: string // 版本号
  dosageNumerator: any // *用量:分子
  dosageDenominator: any // *用量:分母
  baseUnit?: string // 基本单位
  action?: string // 操作
  // 新增替代料相关字段
  isAlternative?: boolean // 是否为替代料
  parentRowIndex?: number // 主料行索引（替代料专用）
  alternativeSequence?: number // 替代料序号（1, 2, 3...）
  mainSequence?: number // 主料序号
}
// 用料阶别下拉数据
const materialLevelOptions = ref<any>([])
// 表单ref
const formRef = ref()
// 表格表单ref
const tableFormRef = ref()
// 显示抽屉
const showDrawer = () => {
  openDrawer.value = true
}
// 关闭抽屉
const handleClose = () => {
  // 重置产品信息表单
  formRef.value?.resetFields()

  // 重置物料表单
  tableFormRef.value?.resetFields()

  // 重置表单数据
  Object.assign(formState, {
    productCode: '',
    productName: '',
    productVersion: '',
    remark: '',
    status: 1,
    materialControl: false,
  })

  // 重置物料数据
  materialData.value = [
    {
      materialCode: '',
      materialName: '',
      dosageNumerator: undefined,
      dosageDenominator: undefined,
      // 其余字段可选
      materialLevel: undefined,
      materialSpec: undefined,
      materialCategory: undefined,
      materialGroup: undefined,
      version: undefined,
      baseUnit: undefined,
      action: undefined,
      isAlternative: false,
    },
  ]
  // 重置其他状态
  currentRowIndex.value = -1
  isAddingAlternative.value = false
  expandedRows.value.clear()
  // 关闭抽屉
  openDrawer.value = false
}
// 强制刷新表格
const refreshTable = () => {
  tableKey.value++
}
// 判断当前主料是否有替代料
const hasAlternatives = (row: materialRow) => {
  if (row.isAlternative) return false
  // 搜索隐藏展开图标
  if (materialCodeSearch.value && materialCodeSearch.value.trim()) {
    return false
  }
  // 空数据不显示展开图标
  if (!row.materialCode && !row.materialName) return false

  const mainRowIndex = materialData.value.findIndex((item) => item.materialCode === row.materialCode && item.materialName === row.materialName && item.isAlternative === row.isAlternative)
  if (mainRowIndex === -1) return false
  const result = materialData.value.some((item) => item.parentRowIndex === mainRowIndex && item.isAlternative)
  console.log('hasAlternatives:', {
    rowCode: row.materialCode,
    mainRowIndex,
    result,
    alternatives: materialData.value.filter((item) => item.parentRowIndex === mainRowIndex && item.isAlternative),
  })
  return result
}
// 处理展开/折叠事件
const handleToggleExpand = ({ row, expanded }: any) => {
  const mainRowIndex = materialData.value.findIndex((item) => item.materialCode === row.materialCode && item.materialName === row.materialName && item.isAlternative === row.isAlternative)
  console.log('展开/折叠:', { row, expanded, mainRowIndex })
  if (expanded) {
    expandedRows.value.add(mainRowIndex)
  } else {
    expandedRows.value.delete(mainRowIndex)
  }
  console.log('当前展开状态:', expandedRows.value)
}
// 过滤数据（隐藏被折叠的替代料）
const filteredMaterialData = computed(() => {
  // 过滤展开/折叠状态
  let result = materialData.value.filter((row) => {
    if (!row.isAlternative) return true // 主料始终显示

    // 替代料：检查其主料是否展开
    return row.parentRowIndex !== undefined && expandedRows.value.has(row.parentRowIndex)
  })

  // 搜索过滤
  if (materialCodeSearch.value) {
    const searchValue = materialCodeSearch.value.toLowerCase().trim()

    result = result.filter((row) => {
      return row.materialCode && row.materialCode.toLowerCase().includes(searchValue)
    })
  }

  return result
})
// 判断是否有替代料数据
const hasAlternativeMaterials = computed(() => {
  const result = materialData.value.some((row) => row.isAlternative)
  console.log('hasAlternativeMaterials:', result)
  console.log('materialData:', materialData.value)
  return result
})
// 动态计算操作列宽度
const actionColumnWidth = computed(() => {
  return hasAlternativeMaterials.value ? 300 : 240
})
// 根据索引获取主料序号
const getMainSequenceByIndex = (rowIndex: number) => {
  let mainCount = 0
  for (let i = 0; i <= rowIndex; i++) {
    if (materialData.value[i] && !materialData.value[i].isAlternative) {
      mainCount++
    }
  }
  return mainCount
}

// 判断是否是当前主料的第一个替代料
const isFirstAlternative = (row: materialRow) => {
  if (!row.isAlternative) return false

  const sameParentAlternatives = materialData.value.filter((item) => item.parentRowIndex === row.parentRowIndex && item.isAlternative)

  return sameParentAlternatives[0] === row
}

// 判断是否是当前主料的最后一个替代料
const isLastAlternative = (row: materialRow) => {
  if (!row.isAlternative) return false

  const sameParentAlternatives = materialData.value.filter((item) => item.parentRowIndex === row.parentRowIndex && item.isAlternative)

  return sameParentAlternatives[sameParentAlternatives.length - 1] === row
}

// 替代料上移
const moveAlternativeUp = (row: materialRow) => {
  const currentIndex = materialData.value.findIndex((item) => item.materialCode === row.materialCode && item.materialName === row.materialName && item.isAlternative === row.isAlternative)
  const sameParentAlternatives = materialData.value.filter((item) => item.parentRowIndex === row.parentRowIndex && item.isAlternative)

  const currentAlternativeIndex = sameParentAlternatives.indexOf(row)
  const prevAlternative = sameParentAlternatives[currentAlternativeIndex - 1]
  const prevIndex = materialData.value.indexOf(prevAlternative)

  // 保存原来的序号
  const currentSequence = row.alternativeSequence
  const prevSequence = prevAlternative.alternativeSequence

  // 交换除序号外的所有数据
  const tempData = { ...row }
  const prevData = { ...prevAlternative }

  // 交换数据，但保持序号不变
  Object.assign(materialData.value[currentIndex], prevData, {
    alternativeSequence: currentSequence,
  })
  Object.assign(materialData.value[prevIndex], tempData, {
    alternativeSequence: prevSequence,
  })
}

// 替代料下移
const moveAlternativeDown = (row: materialRow) => {
  const currentIndex = materialData.value.indexOf(row)
  const sameParentAlternatives = materialData.value.filter((item) => item.parentRowIndex === row.parentRowIndex && item.isAlternative)

  const currentAlternativeIndex = sameParentAlternatives.indexOf(row)
  const nextAlternative = sameParentAlternatives[currentAlternativeIndex + 1]
  const nextIndex = materialData.value.indexOf(nextAlternative)

  // 保存原来的序号
  const currentSequence = row.alternativeSequence
  const nextSequence = nextAlternative.alternativeSequence

  // 交换除序号外的所有数据
  const tempData = { ...row }
  const nextData = { ...nextAlternative }

  // 交换数据，但保持序号不变
  Object.assign(materialData.value[currentIndex], nextData, {
    alternativeSequence: currentSequence,
  })
  Object.assign(materialData.value[nextIndex], tempData, {
    alternativeSequence: nextSequence,
  })
}

const isDeleteDisabled = (row: materialRow) => {
  // 替代料可以删除
  if (row.isAlternative) return false

  // 只有一条主料且为空时不可删除
  const mainRows = materialData.value.filter((item) => !item.isAlternative)
  if (mainRows.length === 1) {
    const onlyMainRow = mainRows[0]
    if (!onlyMainRow.materialCode && !onlyMainRow.materialName) {
      return true
    }
  }

  // 当前主料有替代料时不可删除
  const mainRowIndex = materialData.value.indexOf(row)
  const hasAlternativeRows = materialData.value.some((item) => item.parentRowIndex === mainRowIndex && item.isAlternative)

  return hasAlternativeRows
}

// 保存
const handleSubmit = () => {
  // 只校验产品信息表单
  formRef.value
    .validateFields(['productCode', 'productName', 'productVersion'])
    .then(() => {
      // 产品信息校验通过，再校验物料表单
      if (materialData.value.length > 0) {
        // 校验物料表单
        tableFormRef.value
          .validate() // 或者不传参数，校验整个表单
          .then(() => {
            // 两个表单都校验通过
            message.success('保存成功')
            handleClose()
          })
          .catch(() => {
            message.error('请完善物料信息')
          })
      } else {
        // 没有物料数据，直接保存
        message.success('保存成功')
        handleClose()
      }
    })
    .catch(() => {
      message.error('请完善产品信息')
    })
}
// 列表行删除
const deleteRow = (row: materialRow) => {
  const idx = materialData.value.findIndex((item) => item.materialCode === row.materialCode && item.materialName === row.materialName && item.isAlternative === row.isAlternative)
  if (idx === -1) return

  // 第一条主料数据，清空而不是删除
  if (idx === 0 && !row.isAlternative) {
    // 清空第一条数据
    Object.assign(materialData.value[0], {
      materialCode: '',
      materialName: '',
      dosageNumerator: undefined,
      dosageDenominator: undefined,
      materialLevel: undefined,
      materialSpec: undefined,
      materialCategory: undefined,
      materialGroup: undefined,
      version: undefined,
      baseUnit: undefined,
      action: undefined,
      isAlternative: false,
    })
    return
  }

  // 正常删除
  const wasAlternative = row.isAlternative
  materialData.value.splice(idx, 1)

  // 如果删除的是替代料，刷新表格
  if (wasAlternative) {
    refreshTable()
  }
}

// 新增
const addRow = (row?: materialRow) => {
  // 记录当前操作的行索引
  if (row) {
    currentRowIndex.value = materialData.value.findIndex((item) => item.materialCode === row.materialCode && item.materialName === row.materialName && item.isAlternative === row.isAlternative)
  } else {
    currentRowIndex.value = -1
  }
  materialInfoModalRef.value.showModal('radio')
}
// 选择产品弹窗
const showInputDlg = () => {
  productInfoModalRef.value.showModal()
}
// 新增替代料
const addAlternative = (row: materialRow) => {
  // 检查当前主料是否已选择物料
  if (!row.materialCode || !row.materialName) {
    message.warning('当前物料还没选择，不可新增替代料')
    return
  }
  // 记录当前主料行索引，用于后续添加替代料
  currentRowIndex.value = materialData.value.findIndex((item) => item.materialCode === row.materialCode && item.materialName === row.materialName && item.isAlternative === row.isAlternative)
  // 标记为添加替代料模式
  isAddingAlternative.value = true
  materialInfoModalRef.value.showModal('checkbox')
}
// 处理选择产品
const handleSelectProduct = (product: any) => {
  formState.value.productCode = product.productCode
  formState.value.productName = product.productName
}
// 处理选择物料的回调
const handleSelectMaterial = (selectedMaterial: any) => {
  // 如果是多选数组，处理多个物料
  const materials = Array.isArray(selectedMaterial) ? selectedMaterial : [selectedMaterial]

  if (isAddingAlternative.value) {
    // 添加替代料模式
    const mainRowIndex = currentRowIndex.value

    // 获取当前主料的现有替代料数量
    const existingAlternatives = materialData.value.filter((row) => row.parentRowIndex === mainRowIndex && row.isAlternative)
    const baseAlternativeCount = existingAlternatives.length

    // 获取当前主料的物料编码
    const mainMaterialCode = materialData.value[mainRowIndex]?.materialCode

    // 获取当前主料的现有替代料编码
    const existingAlternativeCodes = materialData.value
      .filter((row) => row.parentRowIndex === mainRowIndex && row.isAlternative)
      .map((row) => row.materialCode)
      .filter((code) => code)

    // 过滤掉重复的替代料
    const newMaterials = materials.filter((material) => {
      // 检查是否与当前主料重复
      if (material.materialCode === mainMaterialCode) {
        message.warning(`替代料与当前主料重复：${material.materialCode}，已过滤`)
        return false
      }

      // 检查是否与现有替代料重复
      if (existingAlternativeCodes.includes(material.materialCode)) {
        message.warning(`当前物料下已有该替代料：${material.materialCode}`)
        return false
      }

      return true
    })

    newMaterials.forEach((material, index) => {
      // 计算替代顺序：基于现有数量 + 当前索引
      const alternativeSequence = baseAlternativeCount + index + 1

      const newAlternativeRow: materialRow = {
        materialCode: material.materialCode,
        materialName: material.materialName,
        materialCategory: material.materialCategory,
        materialGroup: material.materialGroup,
        version: material.versionNumber,
        baseUnit: material.basicUnit,
        materialSpec: material.specification,
        dosageNumerator: undefined,
        dosageDenominator: undefined,
        materialLevel: undefined,
        action: undefined,
        // 替代料特有字段
        isAlternative: true,
        parentRowIndex: mainRowIndex,
        alternativeSequence, // 1, 2, 3...
      }

      // 插入到主料后面的正确位置
      const insertIndex = mainRowIndex + 1 + baseAlternativeCount + index
      materialData.value.splice(insertIndex, 0, newAlternativeRow)
    })

    // 重置替代料模式
    isAddingAlternative.value = false
    refreshTable()
    nextTick(() => {
      const targetRow = materialData.value[mainRowIndex]
      if (targetRow && materialTableRef.value) {
        // 先添加到自定义状态
        expandedRows.value.add(mainRowIndex)
        // 再同步到 VXE Table
        materialTableRef.value.setRowExpand(targetRow, true)
      }
    })
  } else {
    // 获取所有已存在的物料编码
    const existingMainMaterialCodes = materialData.value
      .filter((row) => !row.isAlternative) // 只检查主料
      .map((row) => row.materialCode)
      .filter((code) => code) // 过滤空值

    // 过滤掉重复的物料
    const newMaterials = materials.filter((material) => {
      const isDuplicate = existingMainMaterialCodes.includes(material.materialCode)
      if (isDuplicate) {
        message.warning(`当前已有该物料：${material.materialCode}`)
      }
      return !isDuplicate
    })
    if (newMaterials.length === 0) {
      return
    }
    // 主料添加模式
    const material = newMaterials[0] // 主料只取第一个

    // 计算主料序号
    const mainCount = materialData.value.filter((row) => !row.isAlternative).length + 1

    const currentRow = materialData.value[currentRowIndex.value] || materialData.value[materialData.value.length - 1]
    const isEmptyRow = !currentRow.materialCode && !currentRow.materialName

    if (isEmptyRow) {
      // 第一次新增：回显到当前行
      const targetRow = currentRowIndex.value >= 0 ? materialData.value[currentRowIndex.value] : materialData.value[materialData.value.length - 1]

      targetRow.materialCode = material.materialCode
      targetRow.materialName = material.materialName
      targetRow.materialCategory = material.materialCategory
      targetRow.materialGroup = material.materialGroup
      targetRow.version = material.versionNumber
      targetRow.baseUnit = material.basicUnit
      targetRow.materialSpec = material.specification
      targetRow.mainSequence = mainCount
      targetRow.isAlternative = false
    } else {
      // 后续新增：直接新增一条物料列表
      const newRow: materialRow = {
        materialCode: material.materialCode,
        materialName: material.materialName,
        materialCategory: material.materialCategory,
        materialGroup: material.materialGroup,
        version: material.versionNumber,
        baseUnit: material.basicUnit,
        materialSpec: material.specification,
        dosageNumerator: undefined,
        dosageDenominator: undefined,
        materialLevel: undefined,
        action: undefined,
        isAlternative: false,
        mainSequence: mainCount,
      }
      materialData.value.push(newRow)
      nextTick(() => {
        expandedRows.value.clear()
        refreshTable()
      })
    }
  }
}
// 修改当前行物料（替代料）数据
const editCurrentMaterial = () => {
  materialInfoModalRef.value.showModal('radio')
}
// 监听物料编码搜索，清空展开状态和表格数据（用于清空搜索）
watch(materialCodeSearch, (newValue, oldValue) => {
  if (oldValue && oldValue.trim() && (!newValue || !newValue.trim())) {
    nextTick(() => {
      expandedRows.value.clear()
      if (materialTableRef.value) {
        materialTableRef.value.clearRowExpand()
      }
      refreshTable()
    })
  }
})
defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped>
.columnAnt {
  .ant-form-item {
    margin-bottom: 0;
  }
}
</style>

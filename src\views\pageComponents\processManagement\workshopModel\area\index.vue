<template>
  <div class="flex flex-1 !flex-row px-20 py-12">
    <tree-selector ref="treeRef" type="area" @update="refresh" />
    <div class="flex flex-1 flex-col">
      <div class="header-container">
        <div class="header-item">车间</div>
        <div class="header-item active">区域</div>
        <div class="header-item">工作中心</div>
      </div>
      <Form v-model:form="formArr" :page-type="PageType.WORKSHOP_AREA" :showSetting="false" @search="search" @setting="tableRef?.showTableSetting()" />
      <!-- 表格 -->
      <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.WORKSHOP_AREA" :formFormat="formFormat" :get-list="getWorkshopAreas">
        <template #right-btn>
          <a-button type="primary" :icon="h(PlusOutlined)" v-if="btnPermission[PERM_CODE.CREATE] && canCreateItem" @click="handleUpdateArea('add')">新建区域</a-button>
        </template>
        <template #workshop_name="{ row }">{{ row.workshop?.name || '--' }}</template>
        <template #dataDictionaryItem="{ row }">{{ row.dataDictionaryItem?.key || '--' }}</template>
        <template #status="{ row, rowIndex }">
          <a-switch
            v-if="btnPermission[PERM_CODE.EDIT]"
            v-model:checked="row.status"
            :checkedValue="1"
            :unCheckedValue="0"
            checked-children="启用"
            un-checked-children="停用"
            @click="handleSwitch(row, rowIndex)"
          />
          <span v-else>{{ row.status ? '启用' : '停用' }}</span>
        </template>
        <template #creator_name="{ row }">
          <span>{{ row.creator?.real_name || '--' }}</span>
        </template>
        <template #created_at="{ row }">
          <span>{{ row.creator?.time || '--' }}</span>
        </template>
        <template #operate="{ row }">
          <div class="btnBox">
            <a-button v-if="btnPermission[PERM_CODE.VIEW]" @click="handleViewArea(row.id)" class="btn">查看</a-button>
            <a-dropdown>
              <a-button>更多</a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="btnPermission[PERM_CODE.EDIT]" @click="handleUpdateArea('edit', row.id)">编辑</a-menu-item>
                  <a-menu-item v-if="btnPermission[PERM_CODE.DELETE]" @click="handleDeleteArea(row.id)">
                    <span class="text-red-500">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </BaseTable>
    </div>
    <component v-if="open" :is="component" v-model:open="open" :init-value="initValue" @update="refresh" />
  </div>
</template>

<script lang="ts" setup>
import { h, shallowRef, watch, onMounted, provide } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getWorkshopAreas, getSelectOptions } from '@/servers/workshopArea'
import { getWorkshopNames } from '@/servers/workshop'
import { PageType } from '@/common/enum'
import { PERM_CODE } from './type'
import TreeSelector from '../TreeSelector.vue'
import AreaUpdateDrawer from './components/AreaUpdateDrawer.vue'
import AreaViewDrawer from './components/AreaViewDrawer.vue'
import AreaDeleteModal from './components/AreaDeleteModal.vue'
import StatusUpdateModal from './components/StatusUpdateModal.vue'

const { btnPermission } = usePermission()

const treeRef = ref()
const company_id = computed(() => treeRef.value?.company_id || localStorage.getItem('tree_company'))
const canCreateItem = computed(() => treeRef.value?.canCreateItem)
const areaOptions = ref() // 车间区域下拉选项
const workshop_id = computed(() => (treeRef.value?.selectedNode?.workshop_type === 1 ? treeRef.value?.selectedNode?.id : treeRef.value?.selectedNode?.workshop_id)) // 车间id

// 查询表单
const formArr = ref<any[]>([
  {
    label: '请选择区域',
    value: null,
    type: 'select',
    key: 'name',
    selectArr: areaOptions.value,
    showSearch: true,
    selectKey: 'name',
    fieldNames: { label: 'name', value: 'name' },
  },
  {
    label: '请选择生产阶别',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'data_dictionary_item_id',
  },
])

const tableRef = ref()
const search = () => tableRef.value.search()
// 额外传参
const formFormat = (data) => ({
  ...data,
  company_id: company_id.value,
  workshop_id: workshop_id.value,
})

// component
const component = shallowRef()
const open = ref(false)
const initValue = reactive<any>({
  type: '',
  workshop_area_id: null,
  company_id: '',
  workshop_id: '',
})

const refresh = (type: string = 'index') => {
  tableRef.value.refresh()
  getAreaOptions() // 更新区域下拉选项
  if (type === 'index') {
    treeRef.value.getTreeData() // 更新树结构数据
  }
}

/** 创建/编辑车间区域 */
const handleUpdateArea = (type: string, workshop_area_id?: number) => {
  component.value = AreaUpdateDrawer
  initValue.type = type
  initValue.company_id = company_id
  initValue.workshop_id = workshop_id.value
  initValue.workshop_area_id = workshop_area_id
  open.value = true
}
/** 查看车间区域详情 */
const handleViewArea = (workshop_area_id: number) => {
  component.value = AreaViewDrawer
  initValue.workshop_area_id = workshop_area_id
  open.value = true
}
/** 删除车间区域 */
const handleDeleteArea = (workshop_area_id: number) => {
  component.value = AreaDeleteModal
  initValue.workshop_area_id = workshop_area_id
  open.value = true
}
/** 切换车间区域启用状态 */
const handleSwitch = (row, rowIndex: number) => {
  component.value = StatusUpdateModal
  initValue.workshop_area_id = row.id
  initValue.name = row.name
  initValue.status = row.status // 此时status状态已改变
  tableRef.value.tableData[rowIndex].status = row.status === 1 ? 0 : 1 // 确保切换成功之前status状态不变
  open.value = true
}

const productStepOptions = ref()
provide('productStepOptions', productStepOptions)
/** 获取生产阶别下拉列表 */
const getProductStepOptions = async () => {
  const res = await getSelectOptions()
  productStepOptions.value = res.data?.production_level?.map((item) => ({
    value: item.id,
    label: item.key,
  }))
  const target = formArr.value?.find((item) => item.key === 'data_dictionary_item_id')
  target.selectArr = productStepOptions.value
}

/** 获取表单查询选项 */
const getAreaOptions = async () => {
  const params = {
    type: 'area',
    company_id: company_id.value,
    workshop_id: workshop_id.value,
  }
  const res = await getWorkshopNames(params)
  areaOptions.value = res.data || []
}

watch(
  areaOptions,
  (newOptions) => {
    formArr.value[0].selectArr = newOptions
  },
  { immediate: true },
)

// 当workshop_id变化时才重新更新数据
watch(
  workshop_id,
  () => {
    tableRef.value?.search()
    getAreaOptions()
  },
  { immediate: true },
)

onMounted(() => {
  getProductStepOptions()
})
</script>

<style lang="scss" scoped>
.header-container {
  display: inline-flex;
  margin-bottom: 8px;

  .header-item {
    align-content: center;
    width: 60px;
    height: 28px;
    color: #000;
    text-align: center;
    border: 1px solid #d3d3d3;
    border-right: 0;
  }

  .header-item:last-child {
    width: 70px;
    border: 1px solid #d3d3d3;
  }

  .header-item.active {
    color: #fff;
    background: #409eff;
    border-color: #409eff;
  }
}
</style>

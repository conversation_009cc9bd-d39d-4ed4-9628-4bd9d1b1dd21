<template>
  <a-drawer title="查看工作中心" width="980px" :open="open" :maskClosable="false" @close="onClose">
    <a-form ref="formRef" :model="formState" name="basic" layout="inline" class="form-container" v-bind="formItemLayout">
      <a-col :span="12">
        <a-form-item label="工作中心编号" name="code">
          {{ formState.code }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="工作中心名称" name="name">
          {{ formState.name }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="生产阶别" name="stage_name">
          {{ formState.process_info?.stage_item?.key }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="所属工序" name="process_info_name">
          {{ formState.process_info?.process_name || '--' }}
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="备注" name="remark" class="remark-item" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          {{ formState.remark || '--' }}
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="状态" name="status">
          {{ formState.status ? '启用' : '停用' }}
        </a-form-item>
      </a-col>
    </a-form>
    <div class="px-10">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="principal" tab="负责人信息">
          <principal-table type="view" :company_id="initValue.company_id" :principalList="formState.principals" />
        </a-tab-pane>
        <a-tab-pane key="equipment" tab="设备信息">
          <equipment-table type="view" :equipmentList="formState.equipment" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="drawer-title mt-32">其他信息</div>
    <a-form :model="formState" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
      <a-form-item label="创建时间" name="created_at">
        <span>{{ formState.created_at }}</span>
      </a-form-item>
      <a-form-item label="创建人" name="creator">
        {{ formState.creator?.real_name }}
        <span class="user-info">{{ `（ ${formState.creator?.position_name || '--'} | ${formState.creator?.department_name || '--'} ）` }}</span>
      </a-form-item>
      <a-form-item label="最后修改时间" name="updated_at">
        {{ formState.updated_at }}
      </a-form-item>
      <a-form-item label="最后修改人" name="modifier">
        {{ formState.modifier?.real_name }}
        <span class="user-info">{{ `（ ${formState.modifier?.position_name || '--'} | ${formState.modifier?.department_name || '--'} ）` }}</span>
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { viewWorkshopCenter } from '@/servers/workshopCenter'
import PrincipalTable from './PrincipalTable.vue'
import EquipmentTable from './EquipmentTable.vue'

const props = defineProps<{
  open: boolean
  initValue: {
    company_id: string
    type: string
    workcenter_id: number
  }
}>()

const activeKey = ref('principal')
const emits = defineEmits(['update:open', 'update'])

const formRef = ref()
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 16 },
}
const formState = reactive({
  company_id: props.initValue.company_id,
  code: '',
  name: '',
  process_info: { process_name: '', stage_item: { key: '' } },
  status: '',
  remark: '',
  principals: [],
  equipment: [],
  created_at: '',
  creator: { real_name: '', position_name: '', department_name: '' },
  updated_at: '',
  modifier: { real_name: '', position_name: '', department_name: '' },
})

// 关闭drawer
const onClose = () => {
  emits('update:open', false)
}

// 获取工作中心详情
const getWorkshopCenterDetail = async () => {
  const res = await viewWorkshopCenter(props.initValue.workcenter_id)
  Object.assign(formState, res.data)
  console.log('principals:', formState.equipment)
}

onMounted(() => {
  getWorkshopCenterDetail()
})
</script>

<style lang="scss" scoped>
.form-container {
  margin-bottom: 24px;

  :deep(.ant-col) {
    margin-bottom: 10px;
  }
}

.remark-item {
  :deep(.ant-form-item-label) {
    margin-left: 14px;
  }
}

.user-info {
  color: #8a8a8a;
}
</style>

import transformerDirectives from '@unocss/transformer-directives'
import { defineConfig, presetUno } from 'unocss'

export default defineConfig({
  // ...UnoCSS options
  postprocess: (util) => {
    // 禁用自动转换rem
    util.entries.forEach((i) => {
      const value = i[1]
      if (typeof value === 'string' && value.includes('rem')) {
        i[1] = value.replace(/[\d.]+rem/, (v) => {
          return `${parseFloat(v) * 4}px`
        })
      }
    })
  },
  presets: [presetUno()],
  theme: {
    fontSize: {
      xs: ['12px', '1.5'], // 字号为 16px，行高为 1.5 倍
      sm: ['14px', '1.5'], // 字号为 16px，行高为 1.5 倍
      base: ['16px', '1.5'], // 字号为 16px，行高为 1.5 倍
      lg: ['18px', '1.5'],
      xl: ['20px', '1.5'],
      // 添加更多字号配置...
    },
    lineHeight: {
      normal: '1.5',
      // 或者根据需要添加其他行高配置
    },
    colors: {
      info: '#2C81DB',
      success: '#67C23A',
      warn: '#E6A23C',
      error: '#F56C6C',
    },
  },
  transformers: [transformerDirectives() as any],
  shortcuts: [['center', 'flex justify-center items-center']],
  rules: [
    ['regular', { 'font-family': 'PingFangSC-Regular, sans-serif' }],
    ['bold', { 'font-family': 'PingFangSC-Semibold, sans-serif' }],
    [
      'input',
      {
        'background-color': 'transparent',
        border: 'none',
        outline: 'none',
      },
    ],
  ],
})

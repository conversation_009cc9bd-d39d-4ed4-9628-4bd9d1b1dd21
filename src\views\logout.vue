<template>
  <div class="page">
    <a-spin />
  </div>
</template>

<script lang="ts" setup>
import { LogoutLocal } from '@/servers/User'
import { beforLogout } from '@/utils/index'

LogoutLocal().then(() => {
  beforLogout()
  window.location.href = '/'
})
</script>

<style lang="scss" scoped>
.page {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
}
</style>

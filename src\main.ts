import '@/assets/style/index.scss'
import router from '@/router'

import VxeUI from '@/utils/VxeUi'
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

import Antd from 'ant-design-vue'
import 'virtual:uno.css'
import { createApp } from 'vue'
import { beforLogout, jumpUMCForLogin } from '@/utils'
import { GetUserInfoByToken } from '@/servers/User'
import App from './App.vue'
import 'ant-design-vue/dist/reset.css'
import store from './store'

const app = createApp(App).use(Antd).use(VxeUI).use(VxeTable).use(router).use(store)

router.beforeEach((to, _from, next) => {
  const userData = localStorage.getItem('userData') || ''
  const code: any = to.query.code || ''
  const sessionId = to.query.sessionId
  if (code || to.fullPath === '/loding') {
    if (code) {
      localStorage.setItem('code', code)
      next({ path: '/loding' })
    } else {
      next()
    }
  } else if (userData) {
    localStorage.setItem('lastUseTime', String(Math.floor(Date.now() / 1000)))
    next()
  } else if (sessionId && to.path === '/loginSuccess') {
    const userData = {
      login_token: sessionId,
    }
    localStorage.setItem('userData', JSON.stringify(userData))
    GetUserInfoByToken().then((res) => {
      const userInfo = res?.data
      userInfo.login_token = sessionId
      localStorage.setItem('userData', JSON.stringify(userInfo))
      next('/processParamtters')
    })
  } else if (to.path === '/404') {
    if (sessionId) {
      const userData = {
        login_token: sessionId,
      }
      localStorage.setItem('userData', JSON.stringify(userData))
    }
    next()
  } else {
    next()
    beforLogout()
    jumpUMCForLogin()
  }
})
app.mount('#app')

// 获取版本号
// const edition = () => {
//   const edition = localStorage.getItem('edition')
//   const VERSION = import.meta.env.VITE_APP_VERSION
//   if (edition) {
//     if (edition != VERSION) {
//       localStorage.clear()
//       return true
//     }
//   } else {
//     localStorage.setItem('edition', import.meta.env.VITE_APP_VERSION)
//   }
//   return false
// }
// 自动登录
// if (window.performance.navigation.type == 0) {
//   if (localStorage.getItem('autoLogin') !== 'true') {
//     localStorage.removeItem('userData')
//   }
// }
// 超十五天未使用
if (Math.floor(Date.now() / 1000) - Number(localStorage.getItem('lastUseTime')) > 3600 * 24 * 15) {
  localStorage.removeItem('userData')
}

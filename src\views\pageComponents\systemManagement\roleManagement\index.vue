<template>
  <div class="main">
    <Form v-model:form="formArr" :page-type="PageType.ROLE_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.ROLE_MANAGE" :get-list="GetList" :formFormat="formFormat">
      <template #right-btn>
        <a-button id="roleManagementIncrease" type="primary" @click="tapManipulate('add')" :icon="h(PlusOutlined)" v-if="btnPermission[220001]">新增角色</a-button>
      </template>
      <template #status="{ row }">
        <a-switch :disabled="row.is_def" class="btn" @click="tapSwitch($event, row)" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" />
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>
      <template #operate="{ row }">
        <div class="btnBox">
          <a-button id="roleManagementDetail" @click="detail(row)" class="btn">查看</a-button>
          <a-button id="roleManagementEdit" class="btn" @click="tapManipulate('compiler', row)" v-if="btnPermission[220002]">编辑</a-button>
          <a-button id="roleManagementPermission" class="btn" @click="tapManipulate('permissions', row)" v-if="btnPermission[220004]">权限</a-button>
          <a-dropdown>
            <a-button v-if="btnPermission[220003] || btnPermission[220005]">更多</a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="tapManipulate('config', row)" v-if="btnPermission[220005]">分配用户</a-menu-item>
                <a-menu-item @click="tapManipulate('removes', row)" :disabled="row.is_def" v-if="btnPermission[220003]">
                  <span class="text-red-500">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="520"
      :title="roleModuleType == 'add' ? '新建角色' : '编辑角色'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="addRoleData">
        <a-form-item
          label="角色名称"
          name="role_name"
          :rules="[
            { required: true },
            {
              validator: (_rule, value) => validateStr(_rule, value, 50),
              message: '输入内容不可超过50字符',
            },
          ]"
        >
          <a-input id="role_name" v-model:value="addRoleData.role_name" placeholder="请输入角色名称" />
        </a-form-item>

        <!-- <a-form-item label="角色编码" name="role_code" v-if="roleModuleType == 'add'">
          <a-input id="role_code" v-model:value="addRoleData.role_code" placeholder="请输入角色编码" />
        </a-form-item> -->

        <a-form-item label="状态" name="status">
          <a-switch id="status" :disabled="addRoleData.is_def" v-model:checked="[false, true][addRoleData.status]" @click="startStopForm()" checked-children="启用" un-checked-children="停用" />
          <!-- <a-radio-group v-model:value="addRoleData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">停用</a-radio>
          </a-radio-group> -->
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 0.8333rem" type="primary" @click="tapAddRoleSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="1111" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 0.8333rem" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <!-- 权限 -->
    <EditLimits ref="editLimitsRef" @query="search"></EditLimits>
  </div>
</template>
<script lang="ts" setup>
import Form from '@/components/Form.vue'
import { PageType } from '@/common/enum'
import { Add, Delete, GetList, Update, UpdateRoleStatus } from '@/servers/Role'
import { validateStr } from '@/utils/index'
import { message } from 'ant-design-vue'
import { onMounted, h } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import DetailDrawer from './components/DetailDrawer.vue'
import EditLimits from './components/PermissionDrawer.vue'

const formFormat = (data) => {
  return {
    ...data,
    sort_field: 'create_at',
    sort_asc: false,
  }
}
const router = useRouter()
const isAddRole = ref(false)
const editLimitsRef = ref()
// const showFilter = ref(false)

const { btnPermission } = usePermission()

const roleModuleType = ref('add')
// 查看
const detailDrawerRef = ref()
const oldStatus = ref(null)
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '搜索角色名称',
    value: null,
    type: 'input',
    key: 'role_name',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      {
        label: '停用',
        value: 0,
      },
      {
        label: '启用',
        value: 1,
      },
      {
        label: '已离职',
        value: 2,
      },
      {
        label: '待邀请',
        value: 3,
      },
    ],
    key: 'status',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'create_at',
    formKeys: ['created_at_start', 'created_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'update_at',
    formKeys: ['updated_at_start', 'updated_at_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const addRoleData = reactive({
  id: null,
  role_name: '',
  role_code: '',
  scope: 1,
  status: 1,
  is_def: false,
})
const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.ROLE_MANAGE) {
    const arr: any[] = []
    obj.ROLE_MANAGE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}
onMounted(() => {
  search()
  initScreening()
})

const tapAddRoleSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapManipulate = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      addRoleData.id = null
      addRoleData.role_name = ''
      addRoleData.role_code = ''
      addRoleData.scope = 1
      addRoleData.status = 1
      addRoleData.is_def = false
      break
    case 'compiler':
      oldStatus.value = row.status
      addRoleData.is_def = row.is_def
      addRoleData.id = row.id
      addRoleData.role_name = row.role_name
      addRoleData.scope = row.scope
      addRoleData.status = row.status
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      break
    case 'permissions':
      openEditLimits(row)
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除角色'
      visibleData.content = `即将删除该角色，删除后：
  - 现有系统权限配置可能会受到影响，请确保已经调整了相关权限设置。

请在执行此操作前确认：
  - 已检查并调整系统中与该角色相关的权限配置。
  - 已通知受影响的用户，且已重新分配必要的角色或权限。

此操作不可恢复，确定要删除该角色吗？`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    case 'config':
      openConfigList(row)
      break
    default:
      break
  }
}
const tapSwitch = (e, row) => {
  if (!row.status) {
    updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
  } else {
    visibleData.isShow = true
    visibleData.title = '停用角色'
    visibleData.content = `即将停用该角色，停用后：
  - 所有使用此角色的用户将失去与该角色相关的权限。
  - 受影响的用户将无法执行与此角色权限相关的操作，直到他们被分配新的角色。

确定要停用该角色吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.okType = 'danger'
    visibleData.isCancelBtn = true
    visibleData.okFn = () => {
      updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
    }
  }
}

const startStopForm = () => {
  if (addRoleData.status == 1) {
    addRoleData.status = 0
  } else {
    addRoleData.status = 1
  }
}

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id, btnPermission.value[31007])
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()
// const showTableSetting = () => tableRef.value.showTableSetting()
// const tapScreeningShow = () => formRef.value.openScreening()

// 角色启用停用
const updateRoleStatus = (obj) => {
  UpdateRoleStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}
// 新增角色
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(addRoleData))
  Add(obj).then(() => {
    message.success('新增成功')
    isAddRole.value = false
    search()
  })
}
// 编辑角色
const upRoleDate = () => {
  const fn = () => {
    const obj = JSON.parse(JSON.stringify(addRoleData))
    Update(obj).then(() => {
      message.success('修改成功')
      isAddRole.value = false
      search()
    })
  }
  if (addRoleData.status == 0 && oldStatus.value == 1) {
    // 停用
    visibleData.isShow = true
    visibleData.title = '停用角色'
    visibleData.content = `即将停用该角色，停用后：
  - 所有使用此角色的用户将失去与该角色相关的权限。
  - 受影响的用户将无法执行与此角色权限相关的操作，直到他们被分配新的角色。

确定要停用该角色吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.isCancelBtn = true
    visibleData.okType = 'danger'
    visibleData.okFn = () => {
      visibleData.isShow = false
      fn()
    }
  } else {
    fn()
  }
}
// 删除角色
const deleteRole = (id) => {
  Delete({ id })
    .then(() => {
      visibleData.isShow = false
      message.success('删除成功')
      search()
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
// 打开权限弹窗
const openEditLimits = (item) => {
  editLimitsRef.value.opendrawer(item)
}

const openConfigList = (row) => {
  if (row.status === 0) {
    message.warning('该角色已停用，无法进行分配操作，请选择其他有效角色！')
    return
  }
  const sessionPageStr = sessionStorage.getItem('sessionPage')
  let sessionPage = {}
  if (sessionPageStr) {
    sessionPage = JSON.parse(sessionPageStr)
    sessionPage[`roleUserConfig${row.id}`] = 'true'
  } else {
    sessionPage[`roleUserConfig${row.id}`] = 'true'
  }
  sessionStorage.setItem('sessionPage', JSON.stringify(sessionPage))
  router.push(`/roleUserConfig/${row.id}`)
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5rem;
    margin: 0.6667rem 0;

    .title {
      font-size: 1.3333rem;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-top: 1.25rem;
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 2.6667rem;

      .tag {
        padding: 0 0.8333rem;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 0.8333rem;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 8.3333rem;
    min-width: 8.3333rem;
    margin-right: 2.5rem;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 1.1667rem;
  word-break: break-word;
  white-space: pre-wrap;
}

.tableBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 2.6667rem;
  margin-top: 0.8333rem;
  margin-bottom: 0.8333rem;
  font-size: 1.3333rem;
  font-weight: bold;
  color: #000;
}

.vxe-icon {
  width: 1.3333rem;
  height: 1.3333rem;
}
</style>

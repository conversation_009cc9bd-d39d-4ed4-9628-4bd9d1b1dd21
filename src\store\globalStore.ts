import { defineStore } from 'pinia'
// import router from '@/router'

const globalStore = defineStore('page', {
  state: () => ({
    token: '',
    userInfo: {},
  }),
  actions: {
    setToken(value: string) {
      this.token = value
      localStorage.setItem('token', value)
      setTimeout(() => {
        // getUserInfo({ auth: value }).then((data: any) => {
        //     if (data.userHade && !data.userHade.includes('http')) {
        //         data.userHade = config.BT_userHade_baseURL + data.userHade
        //     }
        //     this.userInfo = data
        //     // this.GetExperienceInfo()
        // }).catch(() => {
        //     this.unLogin()
        // })
        // this.initExperience()
      }, 0)
    },
    setUserInfo(userInfo: any) {
      this.userInfo = userInfo
    },
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    unLogin(callBack?: Function) {
      this.userInfo = {}
      this.token = ''
      localStorage.removeItem('token')
      // eslint-disable-next-line no-unused-expressions, @typescript-eslint/no-unused-expressions
      callBack && callBack()
      setTimeout(() => {
        // router.push('/')
      }, 2000)
    },
  },
})

export default globalStore
